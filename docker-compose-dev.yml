version: '3'
services:

  # We use ngrok when developing locally so that we can redirect back to this URL from stripe when testing payments...
#  portal-ngrok:
#    container_name: portal-ngrok
#    image: wernight/ngrok
#
#    restart: unless-stopped
#    privileged: true
#
#    networks:
#      - portal-net
#
#    ports:
#      - 4040:4040
#
#    environment:
#      - NGROK_AUTH=5Ubn3fz6Pd24wNeVMrKnW_5EbUZdNmpgiLhwckGZGB4
#      - NGROK_PORT=portal-server:8010
#      - NGROK_REGION=au

  portal-server:
    container_name: portal-server
    build:
      context: ./portal-server
      dockerfile: Dockerfile.server.dev

    entrypoint: ["bash", "start-dev.sh", "pm2"]

    restart: unless-stopped
    privileged: true

    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - SHOW_CONFIG=${SHOW_CONFIG}
      - ENABLE_CRONS=${ENABLE_CRONS:-false}
      - SWAGGER_UI=${SWAGGER_UI}
      - DEFAULT_DEVELOPMENT_USER=${DEFAULT_DEVELOPMENT_USER:-<EMAIL>}
      - AWS_PROFILE=${AWS_PROFILE:-12round}

    expose:
      - 8010
    ports:
      - 8010:8010

    volumes:
      - $HOME/.aws/credentials:/root/.aws/credentials:ro
      - ./:/opt

    networks:
      - portal-net

    depends_on:
      - portal-local-db
      - portal-local-sqs

  portal-server-sls:
    container_name: portal-server-sls
    build:
      context: ./portal-server
      dockerfile: Dockerfile.serverless.dev

    entrypoint: ["bash", "start-dev.sh", "sls"]
    #command: []

    #restart: unless-stopped
    privileged: true

    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - AWS_PROFILE=${AWS_PROFILE:-12round}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-}

    expose:
      - 3030
    ports:
      - 3030:3030

    volumes:
      - $HOME/.aws/credentials:/root/.aws/credentials:ro
      - ./:/opt

    depends_on:
      - portal-local-db
      - portal-local-sqs

    networks:
      - portal-net

  portal-front-end:
    container_name: portal-front-end
    build:
      context: ./portal-front-end
      dockerfile: Dockerfile.frontend

    #restart: unless-stopped
    privileged: true

    environment:
      - AWS_PROFILE=${AWS_PROFILE:-12round}
      - BS_PROXY=portal-server:8010
      - BS_PORT=3000

    expose:
      - 3000

    ports:
      - 3000:3000
      - 3001:3001

    volumes:
      - $HOME/.aws/credentials:/root/.aws/credentials:ro
      - ./:/opt

    networks:
      - portal-net

  portal-local-db:

    image: amazon/dynamodb-local
    container_name: portal-local-db

    volumes:
      - ./.db:/opt/db

    # Refer to the following configuration for all options: https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DynamoDBLocal.UsageNotes.html
    command: -jar DynamoDBLocal.jar -dbPath /opt/db -port 27028 -sharedDb
    ports:
      - 27028:27028

    networks:
      - portal-net

  portal-local-db-admin-ui:
    image: aaronshaf/dynamodb-admin
    container_name: portal-local-db-admin-ui
    ports:
      - "8091:8001"
    environment:
      - DYNAMO_ENDPOINT=http://portal-local-db:27028
      - AWS_ACCESS_KEY_ID=MockAccessKeyId
      - AWS_SECRET_ACCESS_KEY=MockSecretAccessKey
      - AWS_REGION=localhost
    depends_on:
      - portal-local-db
    networks:
      - portal-net

  # Local SQS Emulator...
  portal-local-sqs:
    image: softwaremill/elasticmq-native
    container_name: portal-local-sqs
    ports:
      - "9324:9324"
      - "9325:9325"

    networks:
      - portal-net

  # Local Redis Server
  portal-local-redis:
    image: docker.io/bitnami/redis:latest
    container_name: portal-local-redis

    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_DISABLE_COMMANDS=FLUSHDB,FLUSHALL

    volumes:
      - ./.redis_data:/bitnami/redis/data

    ports:
      - "6379:6379"

    networks:
      - portal-net

  # Uncomment this section if you want to use SES for sending emails
  # portal-sesrelay:
  #   image: building5/ses-relay
  #   privileged: true
  #   container_name: ses-relay
  #   environment:
  #     - SMTP_USERNAME=AKIAIA3L54YSLLLTBN2Q
  #     - SMTP_PASSWORD=Avkkxpk9BMdVsECz0JaV15TwUJCpPm7c5paaJTdHTIfA
  #     - AWS_REGION=us-east-1
  #   networks:
  #     - portal-net

networks:
  portal-net:
    external: true
