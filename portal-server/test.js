// test.js
const { OpenAI } = require('openai'); // Ensure 'openai' is installed via npm
const fs = require('fs');

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'FAKE_KEY_TO_PASS_JENKINS_API_SPEC_BUILD' 
});

// --------------------------------------------

const oldText = `
# Welcome

This is the **UBX Wiki**. Use \`npm install\` to get started.

<h2>Code Example</h2>

\`\`\`js
function greet() {
  return "Hello";
}
\`\`\`
`;

const newText = `
# Welcome

This is the **UBX Platform Wiki**. Use \`npm install --legacy-peer-deps\` to get started.

<h2>Code Example</h2>

\`\`\`js
function greet(name) {
  return "Hello, " + name;
}
\`\`\`
`;

// --------------------------------------------


function generateComparisonPrompt(oldText, newText) {
  return `Previous version:
<<<START_OLD>>>
${oldText}
<<<END_OLD>>>

New version:
<<<START_NEW>>>
${newText}
<<<END_NEW>>>`;
}

async function runChangeLogTest() {
  const messages = [
    {
      role: 'system',
      content: `You are a precise and structured assistant that compares two versions of a wiki page. These pages may include Markdown, HTML, inline code (\`like this\`), and multi-line code blocks (e.g., triple backticks). 

Your task is to generate a detailed changelog in the following strict JSON format:
{
  "changes": {
      "type": "added" | "removed" | "modified" | "none",
      "note": "brief human-readable explanation of changes"
    }
  }
}

Guidelines:
- Ignore whitespace-only or formatting-only differences unless they affect meaning.
- Do not render or interpret HTML/Markdown — treat it as plain text.
- If code blocks change, describe what changed (e.g., a function name, output line, logic).
- Use backticks or quotes as appropriate inside the JSON without escaping them unnecessarily.
- If no meaningful changes are found, return: { "changes": { "type": "none" }}
- Only return one object (one clear summary of all the changes)`
    },
    {
      role: 'user',
      content: generateComparisonPrompt(oldText, newText)
    }
  ];

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages
    });

    const responseContent = completion.choices[0].message.content;
    console.log('🔍 Raw model response:\n', responseContent);

    let parsedResponse;
    try {
      parsedResponse = JSON.parse(responseContent);
      console.log('\n✅ Parsed JSON:\n', JSON.stringify(parsedResponse, null, 2));
    } catch (err) {
      throw new Error(`❌ Failed to parse model response as JSON:\n${responseContent}`);
    }

  } catch (err) {
    console.error('🚨 OpenAI request failed:', err.message);
  }
}

runChangeLogTest();