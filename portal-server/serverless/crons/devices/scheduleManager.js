const geoTz = require('geo-tz');
const moment = require('moment-timezone');
const mqtt = require('mqtt');
const SunCalc = require('suncalc');

const {
  upsert: updateSettings,
  querySettings: queryAudioSettings,
} = require('../../../app/services/dynamodb/audiocontrolconfig');
const {
  querySettings: queryDisplaySettings,
  update: updateLastBellVolume,
} = require('../../../app/services/dynamodb/deviceautomations');
const { scan: scanClubs } = require('../../../app/services/dynamodb/club');
const { isEuropeanCountry, extractSpotifyUri } = require('../../../app/shared-functions/utils');

const MQTT_WS_SERVER = process.env.MQTT_WS_SERVER || 'wss://ws.stage.gymsystems.co/';
const MQTT_USERNAME = process.env.MQTT_USERNAME || 'client';
const MQTT_PASSWORD = process.env.MQTT_PASSWORD || 'HsPhyZT5EoDMKbknJVti';

const MAX_EXEC_TIME = 30000; // 30-secs

module.exports.handler = async (event) => {
  console.log('Starting Device Automations Schedule Manager...', new Date());

  let client = null; // MQTT client

  try {
    const [, clubs] = await scanClubs();

    // Query audio control settings so we're certain we only process clubs
    // that have set their Sonos settings (because auto-volume is disabled
    // by default)
    const audioSettings = await queryAudioSettings();
    console.log(audioSettings.length, 'audio settings found.');

    const displaySettings = await queryDisplaySettings();
    console.log(displaySettings.length, 'display settings found.');

    // Hash map of clubId and the instructions
    const clubInstructions = {};
    for (const clubData of clubs) {
      const { id: clubID, hours: clubHours, placeData } = clubData;
      try {
        let hasInstructions = 0;
        const timezone = geoTz(clubData.latitude, clubData.longitude)[0];

        if (!(clubID in clubInstructions)) {
          clubInstructions[clubID] = {};
        }

        clubInstructions[clubID].clubCountry = placeData?.country?.iso_code;

        let audioSetting = audioSettings.find((item) => item.clubID === clubData.id);
        if (!audioSetting) {
          console.log(`No audio settings yet for ${clubID}. Using global settings.`);
          audioSetting = audioSettings.find((item) => item.clubID === 'global');
        }
        if (!audioSetting) {
          console.log(`No audio settings yet for ${clubID} and having issues getting global settings.`);
          continue;
        }

        const {
          minsBeforeOpenMusicStart,
          minsAfterCloseMusicStop,
          enableAutoVolume,
          overrideAutoVolume,
          volumeSchedule,
          enableMusicSourceSchedule,
          streamSchedule,
          extendedHoursEnabled,
          minsLastCheckInMusicOff,
          lastCheckIn,
        } = audioSetting.general;

        console.log('Club:', clubID, timezone, '\nClub hours:', clubHours);

        const [shouldPlay, shouldPause] = checkIfShouldStartStop(
          clubHours,
          timezone,
          minsBeforeOpenMusicStart,
          minsAfterCloseMusicStop,
        );
        if (shouldPlay) {
          hasInstructions++;
          clubInstructions[clubID].play = true;
        }

        if (shouldPause) {
          hasInstructions++;
          clubInstructions[clubID].pause = true;
        }

        if (extendedHoursEnabled) {
          const nowIsOutsideHours = !isWithinTimeBlock(
            makeScheduleObject(clubHours),
            timezone,
          );
          if (nowIsOutsideHours) {
            const minsCheckInElapsed = (Date.now() - lastCheckIn) / 60000;
            console.log('Mins elapsed from last check in:', minsCheckInElapsed);
            console.log('Check value:', Math.abs(minsCheckInElapsed - minsLastCheckInMusicOff));
            if (Math.abs(minsCheckInElapsed - minsLastCheckInMusicOff) < 5 &&
              minsCheckInElapsed > minsLastCheckInMusicOff &&
              !clubInstructions[clubID].play // If sonos is scheduled to play, ignore this part
            ) {
              hasInstructions++;
              clubInstructions[clubID].pause = true;
            } else if (minsCheckInElapsed < minsLastCheckInMusicOff) {
              if (clubInstructions[clubID].pause) {
                // Do not pause Sonos, check-in detected
                console.log('Removing pause instruction...');
                delete clubInstructions[clubID].pause;
                hasInstructions--;
              }
            }
          }
        }

        if (overrideAutoVolume) {
          await resetOverrideIfEOD(clubHours, timezone, audioSetting);
        }

        if (enableAutoVolume && !overrideAutoVolume) {
          const volume = getScheduledVolume(volumeSchedule, timezone);
          if (volume !== undefined) {
            hasInstructions++;
            clubInstructions[clubID].volume = volume;
          }
        }

        if (enableMusicSourceSchedule) {
          const musicSource = getScheduledMusicSource(streamSchedule, timezone);
          if (musicSource !== undefined) {
            console.log('Play music source');
            hasInstructions++;
            clubInstructions[clubID].musicSource = musicSource;
          }
        }

        let displaySetting = displaySettings.find((item) => item.club === clubData.id);
        if (displaySetting &&
          (!displaySetting.minsBeforeOpenDisplayOn || !displaySetting.minsAfterCloseDisplayOff) // Means this is an old setting
        ) {
          if (displaySetting.autoPowerDisplays === false) {
            console.log(`Skipping display automations for ${clubID}.`);
            continue;
          } else {
            console.log(`Old display settings says autoPowerDisplays for ${clubID}. Using global settings.`);
            displaySetting = displaySettings.find((item) => item.club === 'global');
          }
        }
        if (!displaySetting) {
          console.log(`No display settings yet for ${clubID}. Using global settings.`);
          displaySetting = displaySettings.find((item) => item.club === 'global');
        }
        if (!displaySetting) {
          console.log(`No display settings yet for ${clubID} and having issues getting global settings.`);
          continue;
        }

        const {
          enableBellAutoVolume,
          bellVolumeSchedule,
          eaEnabled,
          minsBeforeOpenDisplayOn,
          minsAfterCloseDisplayOff,
          minsLastCheckInDisplayOff,
          enableAutoBrightness,
          dayTriggerType,
          dayTimeStart,
          dayBrightnessLevel,
          nightTriggerType,
          nightTimeStart,
          nightBrightnessLevel,
        } = displaySetting;

        const [shouldOn, shouldOff] = checkIfShouldStartStop(
          clubHours,
          timezone,
          minsBeforeOpenDisplayOn,
          minsAfterCloseDisplayOff,
        );
        if (shouldOn) {
          hasInstructions++;
          clubInstructions[clubID].displaysOn = true;
        }

        if (shouldOff) {
          hasInstructions++;
          clubInstructions[clubID].displaysOff = true;
        }

        if (eaEnabled) {
          const nowIsOutsideHours = !isWithinTimeBlock(
            makeScheduleObject(clubHours),
            timezone,
          );
          if (nowIsOutsideHours) {
            const minsCheckInElapsed = (Date.now() - lastCheckIn) / 60000;
            console.log('CS: lastCheckIn', lastCheckIn);
            console.log('CS: Mins elapsed from last check in:', minsCheckInElapsed);
            console.log('CS: Turn  displays off after (minutes): ', minsLastCheckInDisplayOff);
            console.log('CS: Check value:', Math.abs(minsCheckInElapsed - minsLastCheckInDisplayOff));
            if (Math.abs(minsCheckInElapsed - minsLastCheckInDisplayOff) < 5 && minsCheckInElapsed > minsLastCheckInDisplayOff) {
              hasInstructions++;
              clubInstructions[clubID].displaysOff = true;
            } else if (minsCheckInElapsed < minsLastCheckInDisplayOff) {
              if (clubInstructions[clubID].displaysOff) {
                // Do not turn displays off, check-in detected
                console.log('Removing displayOff instruction...');
                delete clubInstructions[clubID].displaysOff;
                hasInstructions--;
              }
            }
          }
        }

        if (enableBellAutoVolume) {
          const volume = getScheduledVolume(bellVolumeSchedule, timezone);
          if (volume !== undefined) {
            hasInstructions++;
            clubInstructions[clubID].bellVolume = volume;
          }
        }

        if (enableAutoBrightness) {
          const brightness = getScheduledBrightness({
            dayTriggerType,
            dayTimeStart,
            dayBrightnessLevel,
            nightTriggerType,
            nightTimeStart,
            nightBrightnessLevel,
            latitude: clubData.latitude,
            longitude: clubData.longitude
          });
          if (brightness !== undefined) {
            hasInstructions++;
            clubInstructions[clubID].brightness = brightness;
          }
        }

        // Remove instructions object for club if no instructions given
        if (!hasInstructions) {
          delete clubInstructions[clubID];
        }
      } catch (err) {
        console.log(`ERROR PROCESSING club: ${clubID}.`);
        console.log(err);
        continue;
      }
    }

    // Clean up
    Object.keys(clubInstructions).forEach((key) => {
      if (Object.keys(clubInstructions[key]).length === 0) {
          delete clubInstructions[key];
      }
    });
    console.log('Instructions:', clubInstructions);

    // Check if there's no instruction to process
    if (!Object.values(clubInstructions).some(val => Object.keys(val).length)) {
      console.log('Nothing to process this time');
      return {};
    }

    console.log('ENV MQTT Server', process.env.MQTT_WS_SERVER);

    // IMPORTANT: Create fresh MQTT client every function invocation.
    // As observed, if client was created on the top level (outside the handler),
    // the connections, listeners, and other client states remain store until
    // the next invokes.
    client = mqtt.connect(MQTT_WS_SERVER, {
      clientId: `ph_serverless_${Math.random().toString(16).substr(2, 8)}`,
      username: MQTT_USERNAME,
      password: MQTT_PASSWORD,
    });

    await Promise.race([
      new Promise((resolve, reject) => {
        // Process instructions to those who replied
        const processed = new Set();
        let replies = 0;
        console.log('Setting up on message callback');
        client.on('message', async (topic, message) => {
          if (!topic.includes('/sonos/master_speaker')) return;

          const clubId = topic.split('/').shift();

          // We might receive message for the same topic more than once
          // Introduce a random delay, simple prevention of simultaneous call
          await waitFor(Math.floor(Math.random() * 50));

          // Skip already processed
          if (processed.has(clubId)) return;

          processed.add(clubId);
          replies++

          console.log('Received reply for', topic);

          const parsedMessage = JSON.parse(message.toString());
          const masterSpeaker = parsedMessage.uuid;
          const topicPrefix = `${clubId}/sonos/${masterSpeaker}`;

          console.log(topicPrefix);

          const instructions = clubInstructions[clubId];
          console.log('Instructions for', clubId);
          console.log(instructions);
          console.log('Processed clubs:', processed.values());

          if (!instructions) return;

          const { play, pause, volume, musicSource, clubCountry = 'AU' } = instructions;

          if (play) {
            client.publish(
              topicPrefix + '/control',
              JSON.stringify({
                command: 'play',
              }),
              { qos: 2 },
              (error) => {
              if (error) {
                console.error('Failed to publish play:', error);
              } else {
                console.log('Command "play" published successfully');
              }
            });
            await waitFor(100);
          }

          if (pause) {
            client.publish(
              topicPrefix + '/control',
              JSON.stringify({
                command: 'pause',
              }),
              { qos: 2 },
              (error) => {
              if (error) {
                console.error('Failed to publish pause:', error);
              } else {
                console.log('Command "pause" published successfully');
              }
            });
            await waitFor(100);
          }

          if (volume !== undefined) {
            client.publish(
              topicPrefix + '/control',
              JSON.stringify({
                command: 'adv-command',
                input: {
                  cmd: 'GroupRenderingControlService.SnapshotGroupVolume',
                }
              }),
              { qos: 2 },
              (error) => {
              if (error) {
                console.error('Failed to publish volume snapshot:', error);
              } else {
                console.log('Command "volume snapshot" published successfully');
              }
            });

            await waitFor(200);

            console.log(`Now setting the volume for ${clubId} sonos to ${volume}`, new Date());
            client.publish(
              topicPrefix + '/control',
              JSON.stringify({
                command: 'adv-command',
                input: {
                  cmd: 'GroupRenderingControlService.SetGroupVolume',
                  val: { InstanceID: 0, DesiredVolume: volume }
                }
              }),
              { qos: 2 },
              (error) => {
              if (error) {
                console.error('Failed to publish set volume:', error);
              } else {
                console.log('Command "set volume" published successfully');
              }
            });
          }

          if (musicSource !== undefined) {
            console.log(`Now changing music source to ${musicSource.name}.`, new Date());

            const { url, trackUri, art, name } = musicSource;

            if (!trackUri) {
              const DEFAULT_RADIO_PROTOCOL = 'x-rincon-mp3radio://';
              client.publish(
                topicPrefix + '/control',
                JSON.stringify({
                  command: 'setavtransporturi',
                  // The value for input from WS payload is the radio stream URL
                  input: DEFAULT_RADIO_PROTOCOL + url
                }),
                { qos: 2 },
                (error) => {
                if (error) {
                  console.error('Failed to publish set av transport:', error);
                } else {
                  console.log('Command "set av transport" published successfully');
                }
              });
            } else if (!trackUri.includes('cpcontainer') && !trackUri.includes('spotify') && !trackUri.includes('file://')) {
              client.publish(
                topicPrefix + '/control',
                JSON.stringify({
                  command: 'setavtransporturi',
                  input: trackUri
                }),
                { qos: 2 },
                (error) => {
                if (error) {
                  console.error('Failed to publish set av transport uri:', error);
                } else {
                  console.log('Command "set av transport uri" published successfully');
                }
              });
            } else {
              // 1 - Remove queue
              client.publish(
                topicPrefix + '/control',
                JSON.stringify({
                  command: 'adv-command',
                  // The value for input from WS payload is the radio stream URL
                  input: {
                    cmd: 'AVTransportService.RemoveAllTracksFromQueue'
                  }
                }),
                { qos: 2 },
                (error) => {
                if (error) {
                  console.error('Failed to publish remove queue:', error);
                } else {
                  console.log('Command "remove queue" published successfully');
                }
              });

              const spotifyUri = extractSpotifyUri(trackUri);
              const parts = spotifyUri.split(':');
              if ((parts.length === 3 || parts.length === 5) && parts[0] === 'spotify') {
                const region = isEuropeanCountry(clubCountry) ? '2311' : '3079';
                const updatedMetaData = {
                  AlbumArtUri: art,
                  ItemId: undefined,
                  ParentId: undefined,
                  Title: name,
                  TrackUri: trackUri,
                  CdUdn: `SA_RINCON${region}_X_#Svc${region}-0-Token`,
                  UpnpClass: getUpnpClass(parts[1])
                }

                const payload = {
                  InstanceID: 0,
                  EnqueuedURI: updatedMetaData.TrackUri,
                  EnqueuedURIMetaData: updatedMetaData,
                  DesiredFirstTrackNumberEnqueued: 0,
                  EnqueueAsNext: true,
                };

                // 2 - Add playlist to queue
                await waitFor(200);
                client.publish(
                  topicPrefix + '/control',
                  JSON.stringify({
                    command: 'adv-command',
                    input: {
                      cmd: 'AVTransportService.AddURIToQueue',
                      val: payload,
                    }
                  }),
                  { qos: 2 },
                  (error) => {
                  if (error) {
                    console.error('Failed to publish add uri to queue:', error);
                  } else {
                    console.log('Command "add uri to queue" published successfully');
                  }
                });
              } else {
                // 2 - Add playlist to queue
                await waitFor(200);
                client.publish(
                  topicPrefix + '/control',
                  JSON.stringify({
                    command: 'adv-command',
                    input: {
                      cmd: 'AddUriToQueue',
                      val: trackUri
                    }
                  }),
                  { qos: 2 },
                  (error) => {
                  if (error) {
                    console.error('Failed to publish add uri to queue:', error);
                  } else {
                    console.log('Command "add uri to queue" published successfully');
                  }
                });
              }

              // 3. Switch to queue
              await waitFor(200);
              client.publish(
                topicPrefix + '/control',
                JSON.stringify({ command: 'switchtoqueue' }),
                { qos: 2 },
                (error) => {
                if (error) {
                  console.error('Failed to publish switch to queue:', error);
                } else {
                  console.log('Command "switch to queue" published successfully');
                }
              });
            }
          }

          if (clubInstructions.length === replies) {
            console.log(`Successfully processed ${replies} clubs.`);
            client.removeAllListeners();
            client.end();
            resolve('All done!');
          }
        });

        let didInitiate = false;
        const connectHandler = () => {
          if (didInitiate) return;

          console.log('Connected to MQTT', { didInitiate });

          doDisplayAutomations(client, clubInstructions);

          // Loop through all clubs then request for master speaker
          Object.keys(clubInstructions).forEach(clubId => {
            // Skip instructions that are not with Sonos
            if (!hasSonosInstructs(clubInstructions[clubId])) return;

            console.log('Subscribing to', clubId + '/sonos/master_speaker');
            client.subscribe(clubId + '/sonos/master_speaker');
          });

          Object.keys(clubInstructions).forEach(clubId => {
            // Skip instructions that are not with Sonos
            if (!hasSonosInstructs(clubInstructions[clubId])) return;

            console.log('Requesting master speaker for', clubId);
            client.publish(
              clubId + '/sonos/who_is_master_speaker',
              null,
              { qos: 2 },
              (error) => {
              if (error) {
                console.error('Failed to publish whoismasterspeaker:', error);
              } else {
                console.log('Command "whoismasterspeaker" published successfully');
              }
            });
          });

          didInitiate = true;
        }

        console.log('Setting up on connect callback');
        client.on('connect', connectHandler);

        setTimeout(() => {
          console.log(`Processed ${replies} clubs for audio automations.`);
          client.removeAllListeners();
          client.end();
        }, MAX_EXEC_TIME - 100);

        client.on('error', function (err) {
          console.log('MQTT client error', err);
          client.removeAllListeners();
          client.end();
          reject(err);
        });

        console.log('Client was connected:', client.connected);
        if (client.connected) {
          console.log('Calling connect handler immediately as client is already connected');
          connectHandler();
        }
      }),
      timeoutHandler()
    ]);

    console.log('Removing client listeners and disconnecting...');
    client.removeAllListeners();
    client.end();
  } catch (err) {
    if (client) {
      client.removeAllListeners();
      client.end();
    }

    console.log(err);
  }

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(`The function used approximately ${Math.round(used * 100) / 100} MB`);

  return {};
}

function timeoutHandler() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log('Max time to process cron has elapsed.');
      resolve();
    }, MAX_EXEC_TIME);
  });
}

function getScheduledVolume(schedule, timezone) {
  const APPROXIMATION = 2 // The number of minutes to approximately match a schedule

  const now = moment().tz(timezone);

  // Match the "day"
  const keyMap = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
  const day = keyMap[now.day()];

  // Get settings for the day
  const volumeSettings = schedule[day];
  console.log('Today\'s volume setting:', volumeSettings);

  // Find matched schedule and get volume
  const volume = volumeSettings.reduce((acc, cur, i, arr) => {
    const [hour, decimal = 0] = String(cur.start).split('.');
    const minute = 60 * `.${decimal}`;

    const start = moment.tz(moment().tz(timezone).format('YYYY-MM-DD'), timezone);
    start.set('hour', hour);
    start.set('minute', minute);
    start.set('second', 0);
    start.set('millisecond', 0);

    // The functions fires approx. on the "dot" when the volume is scheduled
    // so time of execution is always ahead
    if (now.unix() - start.unix() > 0 && now.unix() - start.unix() < APPROXIMATION * 60) {
      // Eject right away when matched
      arr.splice(1);

      return cur.volume || cur.level;
    }

    return undefined;
  }, undefined);

  return volume;
}

function getScheduledMusicSource(schedule, timezone) {
  const APPROXIMATION = 2 // The number of minutes to approximately match a schedule

  const now = moment().tz(timezone);

  // Match the "day"
  const keyMap = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
  const day = keyMap[now.day()];

  // Get settings for the day
  const msSettings = schedule[day];
  console.log('Today\'s music source setting:', msSettings);

  // Find matched schedule and get music source info
  const ms = msSettings.reduce((acc, cur, i, arr) => {
    const [hour, decimal = 0] = String(cur.start).split('.');
    const minute = 60 * `.${decimal}`;

    const start = moment.tz(moment().tz(timezone).format('YYYY-MM-DD'), timezone);
    start.set('hour', hour);
    start.set('minute', minute);
    start.set('second', 0);
    start.set('millisecond', 0);

    // The functions fires approx. on the "dot" when the music source is scheduled
    // so time of execution is always ahead
    if (now.unix() - start.unix() > 0 && now.unix() - start.unix() < APPROXIMATION * 60) {
      // Eject right away when matched
      arr.splice(1);

      return cur;
    }

    return undefined;
  }, undefined);

  return ms;
}

function getScheduledBrightness(options) {
  const {
    dayTriggerType,
    dayTimeStart,
    dayBrightnessLevel,
    nightTriggerType,
    nightTimeStart,
    nightBrightnessLevel,
    latitude,
    longitude
  } = options;

  let brightness;

  // Current time
  const now = moment();

  // Determine the timezone based on the latitude and longitude
  const tz = geoTz(latitude, longitude)[0];

  // Convert the current time to the timezone of the location
  const currentTime = now.tz(tz);

  // Calculate sunrise and sunset times
  const times = SunCalc.getTimes(new Date(), latitude, longitude);
  const autoDayTime = moment(times.dawn).tz(tz);
  const autoNightTime = moment(times.night).tz(tz);

  // Auto brightness logic
  if (dayTriggerType === 'auto' && currentTime.isBetween(autoDayTime, autoDayTime.clone().add(5, 'minutes'))) {
    brightness = dayBrightnessLevel;
  } else if (nightTriggerType === 'auto' && currentTime.isBetween(autoNightTime, autoNightTime.clone().add(5, 'minutes'))) {
    brightness = nightBrightnessLevel;
  }

  // Custom brightness logic
  if (dayTriggerType === 'custom') {
    const dayTime = moment().tz(tz);
    const time = moment(dayTimeStart, 'YYYY-MM-DD HH:mm:ss');
    dayTime.set({
      hour: time.get('hour'),
      minute: time.get('minute'),
      second: time.get('second')
    });

    if (currentTime.isBetween(dayTime, dayTime.clone().add(5, 'minutes'))) {
      brightness = dayBrightnessLevel;
    }
  }

  if (nightTriggerType === 'custom') {
    const nightTime = moment().tz(tz);
    const time = moment(nightTimeStart, 'YYYY-MM-DD HH:mm:ss');
    nightTime.set({
      hour: time.get('hour'),
      minute: time.get('minute'),
      second: time.get('second')
    });
    if (currentTime.isBetween(nightTime, nightTime.clone().add(5, 'minutes'))) {
      brightness = nightBrightnessLevel;
    }
  }

  return brightness;
}

function checkIfShouldStartStop(clubHours, timezone, minsBeforeOpenStart, minsAfterCloseStop) {
  const APPROXIMATION = 2; // The number of minutes to approximately match a schedule
  const startTimeDelta = (APPROXIMATION + minsBeforeOpenStart) * 60;
  const endTimeDelta = (APPROXIMATION + minsAfterCloseStop) * 60;

  const now = moment().tz(timezone);

  // Get time blocks for the day
  const day = now.format('dddd');
  const blocks = clubHours[day];

  // Find matched schedule return true if it should play
  const [shouldStart, shouldStop] = blocks.reduce((acc, block, i, arr) => {
    // Calculate if scheduled to start
    const blockStart = moment(block.start, 'HH:mma');
    const start = moment.tz(moment().tz(timezone).format('YYYY-MM-DD'), timezone);
    start.hour(blockStart.hour());
    start.minute(blockStart.minute());
    if (start.unix() - now.unix() > 0 &&
      start.unix() - now.unix() >= (minsBeforeOpenStart - APPROXIMATION) * 60 &&
      start.unix() - now.unix() < startTimeDelta) {
      acc[0] = true;
    }

    // Calculate if scheduled to stop
    const blockEnd = moment(block.end, 'HH:mma');
    const end = moment.tz(moment().tz(timezone).format('YYYY-MM-DD'), timezone);
    end.hour(blockEnd.hour());
    end.minute(blockEnd.minute());
    if (now.unix() - end.unix() > 0 &&
      now.unix() - end.unix() >= minsAfterCloseStop * 60 &&
      now.unix() - end.unix() < endTimeDelta) {
      acc[1] = true;
    }

    return acc;
  }, [false, false]);

  return [shouldStart, shouldStop];
}

async function waitFor(timeout = 1000) {
  return await new Promise((resolve) => setTimeout(() => resolve(), timeout));
}

async function resetOverrideIfEOD(clubHours, timezone, settings) {
  if (moment().tz(timezone).hour() === 0) {
    await updateSettings({
      clubID: settings.clubID,
      user: settings.user,
      general: {
        ...settings.general,
        overrideAutoVolume: false
      }
    });
  }
}

function isWithinTimeBlock(schedule, timezone, timestamp) {
  const date = timestamp ? new Date(timestamp) : new Date();
  const currentDate = date.toLocaleString('en-US', {
    timeZone: timezone,
  });
  const currentDay = new Date(currentDate)
    .toLocaleString('en-US', { weekday: 'short' })
    .toLowerCase();
  const currentTime =
    new Date(currentDate).getHours() + new Date(currentDate).getMinutes() / 60;

  if (schedule[currentDay]) {
    for (const block of schedule[currentDay]) {
      const start = block.start;
      const end = block.end;

      if (currentTime >= start && currentTime <= end) {
        return true;
      }
    }
  }

  return false;
}

function makeScheduleObject(clubHours) {
  const scheduleObject = Object.keys(clubHours).reduce((acc, dayString) => {
    const key = dayString.toLowerCase().substr(0, 3);
    acc[key] = [];

    // Iterate over blocks of hours
    clubHours[dayString].forEach((block, i) => {
      // Normalize
      const normal = {
        start: parseToCustomTime(block.start),
        end: parseToCustomTime(block.end),
      };
      acc[key][i] = {
        id: Math.random().toString(36),
        start: normal.start,
        end: normal.end,
      };
    });

    return acc;
  }, {});

  return scheduleObject;
}

function parseToCustomTime(timeString) {
  const [time, modifier] = timeString.split(/(?<=\d)(am|pm)/i);
  let [hours, minutes] = time.split(':');
  hours = parseInt(hours, 10);
  minutes = parseInt(minutes || '0', 10);

  if (modifier.toLowerCase() === 'pm' && hours !== 12) {
    hours += 12;
  } else if (modifier.toLowerCase() === 'am' && hours === 12) {
    hours = 0;
  }

  return hours + minutes / 60;
}

async function doDisplayAutomations(client, clubInstructions) {
  const clubIds = Object.keys(clubInstructions);

  for (const clubId of clubIds) {
    const instructions = clubInstructions[clubId];

    const { displaysOn, displaysOff, bellVolume, brightness } = instructions;
    if (displaysOn) {
      // Signal to CCTVs to pause uploads for a minute
      client.publish(
        clubId,
        JSON.stringify({
          command: 'startDeviceStream',
        }),
        { qos: 2 },
        (error) => {
        if (error) {
          console.error('Failed to publish startDeviceStream:', error);
        } else {
          console.log('Command "startDeviceStream" published successfully');
        }
      });

      console.log('Publishing displayOn', clubId);
      client.publish(
        clubId,
        JSON.stringify({
          command: 'displayOn',
          arguments: false,
        }),
        { qos: 2 },
        (error) => {
        if (error) {
          console.error('Failed to publish displaysOn:', error);
        } else {
          console.log('Command "displaysOn" published successfully');
        }
      });
    }

    if (displaysOff) {
      console.log('Publishing displayOff', clubId);
      client.publish(
        clubId,
        JSON.stringify({
          command: 'displayOff',
          arguments: false,
        }),
        { qos: 2 },
        (error) => {
        if (error) {
          console.error('Failed to publish displaysOff:', error);
        } else {
          console.log('Command "displaysOff" published successfully');
        }
      });
    }

    if (bellVolume !== undefined) {
      console.log(`Publishing setvolume twice for ${clubId}`);

      for (let i = 0; i < 2; i++) {
        client.publish(
          clubId,
          JSON.stringify({
            clockCommand: `setvolume-${bellVolume}`,
          }),
          { qos: 2 },
          (error) => {
            if (error) {
              console.error(`Failed to publish bellVolume (attempt ${i + 1}):`, error);
            } else {
              console.log(`Command "bellVolume" published successfully (attempt ${i + 1})`);
            }
          }
        );

        // Wait 5s only after the first publish before publishing a second time
        // (locations with crappy internet don't always receive the first MQTT message)
        if (i === 0) await waitFor(5000);
      }

      await updateLastBellVolume(clubId, { lastBellVolume: bellVolume });
    }

    if (brightness !== undefined) {
      console.log('Publishing brightness', clubId);
      client.publish(
        clubId,
        JSON.stringify({
          command: 'easeBrightness',
          arguments: String(brightness / 100),
        }),
        { qos: 2 },
        (error) => {
        if (error) {
          console.error('Failed to publish easeBrightness:', error);
        } else {
          console.log('Command "easeBrightness" published successfully');
        }
      });
    }

    console.log(`Command sent to ${clubId}.`);
  }
}

function getUpnpClass(kind = '') {
  switch (kind) {
    case 'album':
      return 'object.container.album.musicAlbum';
    case 'artistRadio':
      return 'object.item.audioItem.audioBroadcast.#artistRadio';
    case 'artistTopTracks':
      return 'object.container.playlistContainer';
    case 'playlist':
      return 'object.container.playlistContainer';
    case 'track':
      return 'object.item.audioItem.musicTrack';
    case 'user':
      return 'object.container.playlistContainer';
    default:
      return undefined;
  }
}

function hasSonosInstructs(instruction) {
  const sonosIntructs = ['play', 'pause', 'volume', 'musicSource'];

  return sonosIntructs
    .some(key => Object.prototype.hasOwnProperty.call(instruction, key));
}
