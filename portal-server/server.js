// TODO: Remove condition when we have already have prod config
if (process.env.NODE_ENV !== 'production') {
  require('dotenv').config()
}

// AWS Global Config
const AWS = require('aws-sdk');
const crypto = require('crypto');

const { safelyParseJSON } = require('./app/shared-functions/utils');
const dbConfig = safelyParseJSON(process.env.DYNAMO_CUSTOM_CONFIG) || {};
AWS.config.dynamodb = dbConfig;

const DEFAULT_DEVELOPMENT_USER = process.env.DEFAULT_DEVELOPMENT_USER || '<EMAIL>';
const SWAGGER_UI = (process.env.SWAGGER_UI === 'true') || false;
const port = process.env.LISTEN_PORT || 3000;

const wsOptions = {
    path: '/ws',
    cors: {
        origin: '*',
    },
    serveClient: true
};

const logger = require('./app/logger');
const app = require('./app/createExpressApp.js')({ logger });
const mqtt = require('./app/shared-functions/mqtt');
const { setupAudioControlMsgBroker } = require('./app/shared-functions/audio-control/messageBroker');
const sqsDesigns = require('./app/shared-functions/sqs-rendered-designs-worker.js');
const { listClubStripeRecords } = require('./app/api/stripe/list-club-stripe-records.js');
const { update: updateLastBellVolume } = require('./app/services/dynamodb/deviceautomations');

const server = require('http').createServer(app);
const { createClient } = require('redis');
const { Server: SocketServer } = require('socket.io');
const io = new SocketServer(server, wsOptions);

// Defined further down if redis enabled
let deDuplicationDB;

// Only create the redis adapter if we're actually running the sevrer ... If
// we're just generating API docs skip over this logic so jenkins does not
// fail.

if (process.env.API_DOCS === undefined || process.env.API_DOCS === 'false') {
    const { createAdapter } = require('@socket.io/redis-adapter');

    // Parse Redis URL for host and port
    const redisUrl = process.env.REDIS_HOST ?? 'redis://portal-local-redis:6379';

    // Please see here for a list of available DBs:
    //     https://confluence.aws.gymsystems.co/confluence/display/TS/Redis+Indices+and+Their+Corresponding+Features

    const SOCKETIO_CLUSTER_DB_NUMBER = 4;
    const MQTT_DEDUPE_DB = 7;

    deDuplicationDB = createClient({ url: `${redisUrl}/${MQTT_DEDUPE_DB}` });
    deDuplicationDB.connect();

    const pubClient = createClient({ url: `${redisUrl}/${SOCKETIO_CLUSTER_DB_NUMBER}` });
    const subClient = pubClient.duplicate();

    // Error handling for both clients, doesn't really do anything but will
    // prevent server crash for any client errors
    pubClient.on('error', (err) => console.error('Redis Publisher Client Error', process.env.NODE_APP_INSTANCE, err));
    subClient.on('error', (err) => console.error('Redis Subscriber Client Error', process.env.NODE_APP_INSTANCE, err));

    Promise.all([pubClient.connect(), subClient.connect()]).then(() => {
        console.log('SUCCESSFULLY CONNECTED REDIS CLIENTS');
        io.adapter(createAdapter(pubClient, subClient));
    }).catch((err) => {
        console.log('REDIS CONNECT ERR', err);
    });
}

// ------------------------------------------------------------------------
// NB: we're using express to manage this API.

// Increase the number of listeners that can bind to the same event (We're binding some functions to the same event multiple times when running async code...)
process.setMaxListeners(25);

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

// ------------------------------------------------------------------------

app.set('socketio', io);

// Use this in your routes like so:
/*
    ...
    function(req,res){
        var io = req.app.get('socketio');
        io.emit('it works');
    }
    ...
*/


server
    .on('listening', function() {

        const addr = this.address()
        const bind = typeof addr === 'string' ? `pipe ${addr}` : `port ${addr.port}`
        logger.info(`Listening on ${bind}`)

    })
    .on('clientError', (err, socket) => {
        logger.error(`client error ${err}`)
        // also handle requests somehow
        socket.end('HTTP/1.1 200 stuff\r\n\r\n');
    })
    .on('error', function(error) {

        if (error.syscall !== 'listen') throw error
        const addr = this.address() || { port }
        const bind = typeof addr === 'string' ? `pipe ${addr}` : `port ${addr.port}`

        switch (error.code) {
            case 'EACCES':
                logger.error(`${bind} requires elevated privileges`)
                process.exit(1)
            case 'EADDRINUSE':
                logger.error(`${bind} is already in use`)
                process.exit(1)
            default:
                throw error
        }
    })
;


// Websocket logic...
io.on("connection", async (socket) => {

    console.info(`WS: Connect: ${socket.id}`);

    // Manually set the user's header if we're running in local dev mode as there is no google SSO proxy in front of the performance hub when locally developing...
    if(SWAGGER_UI) socket.request.headers['x-forwarded-user'] = DEFAULT_DEVELOPMENT_USER
    
    // Join the users room if they have a user header from the google SSO proxy...
    if(socket.request.headers['x-forwarded-user'] !== undefined) socket.join(socket.request.headers['x-forwarded-user']);
    if(SWAGGER_UI) console.info('Rooms: ', socket.rooms);

    socket.on("disconnect", (reason) => {
        console.info(`WS: Disconnect: ${socket.id}, ${reason}`);
    });

    // Respond to UI 'Ping' requests...
    socket.on("ping", async () => {
        socket.emit("pong");
    });

    socket.on('change-club-room', ({ prev, cur }) => {
        if (cur === '') return;
        if (prev !== '') socket.leave(prev);

        socket.join(cur);
        setupAudioControlMsgBroker({ room: cur, io, socket });

/*
        // TODO: Remove this direct websocket call
        socket.on('round-display-command', (data) => {
            const clubID = cur;

            // Forward command directly to MQTT topic...
            mqtt.client.publish(clubID, JSON.stringify({
                clockCommand: data.clockCommand
            }));
        });
*/

        socket.on('round-display-command', async (data) => {
          const clubID = cur;
          const { clockCommand } = data;

          // Check and extract bellVolume.
          // we store this in the DB each time it's set, so devices can recall
          // the last bell volume when they boot up (if the volume changed
          // while they were offline)
          
          const bellVolumeMatch = /^setvolume-(\d+(?:\.\d+)?)$/.exec(clockCommand);
          if (bellVolumeMatch) {
            const bellVolume = parseFloat(bellVolumeMatch[1]);
            await updateLastBellVolume(clubID, { lastBellVolume: bellVolume });
          }

          // Forward command directly to MQTT topic...
          mqtt.client.publish(clubID, JSON.stringify({ clockCommand }));
        });

    });

    socket.on('change-region-room', ({ prev, cur }) => {
        if (cur === '') return;
        if (prev !== '') socket.leave(prev);
        socket.join(cur);
    });

    socket.on('request-club-stripe-records', (data) => {
        const { clubID, fromDate, toDate, sessionID } = data;
        const emit = (data) => socket.emit('request-club-stripe-records', { ...data, sessionID });
        listClubStripeRecords(clubID, fromDate, toDate, emit);
    });

    socket.on('request-future-payouts', (data) => {
        const { clubID, fromDate, toDate } = data;
        const emit = (data) => socket.emit('request-future-payouts', data);
        listClubStripeRecords(clubID, fromDate, toDate, emit);
    });

    // Emitting event from only server-1 to test whether all client gets it or not
    // if (process.env.NODE_APP_INSTANCE == 0) {
    //     setInterval(() => {
    //         io.emit('test_msg', 'Test from server: ' + process.env.NODE_APP_INSTANCE)
    //     }, 5000)
    // }

    // setInterval(() => {
    //     socket.emit('test_msg', 'You are connected to server: ' + process.env.NODE_APP_INSTANCE)
    // }, 5000)
});

// Default timeout of 10 mins for long-running processes such as renders etc...
server.timeout = 1000 * 600;

// Bind any device responses and send over to the client (for any clients connected to a specific room that matches the clubID)...
mqtt.onDeviceResponse(async function (responseData) {
    try {
        // Check if the message contains 'masterClock' and bypass de-duplication if true
        const containsMasterClock = responseData?.data?.masterClock !== undefined;

        if (containsMasterClock) {
            // console.log('Bypassing de-duplication for masterClock message:', responseData);
            io.to(responseData.clubID).emit('deviceResponse', responseData);
            return; // Skip further processing
        }

        if (deDuplicationDB) {
            const messageUID = generateMessageUID(responseData);
            // console.log('messageUID', messageUID, responseData);

            // Attempt to set the key only if it does not exist, with an expiration time
            const setResult = await deDuplicationDB.set(messageUID, 'true', { EX: 5, NX: true });

            // If the key was successfully set (i.e., it did not exist before), emit the message
            if (setResult === 'OK') {
                // console.log(messageUID, 'emitted to client');
                io.to(responseData.clubID).emit('deviceResponse', responseData);
            } else {
                // console.log(messageUID, 'already processed, skipping emission');
            }
        } else {
            // No Redis support - local development fallback
            console.log('De-duplication logic not running at this time');
            io.to(responseData.clubID).emit('deviceResponse', responseData);
        }
    } catch (error) {
        console.error('Error processing device response:', error);
    }
});

// Poll completed designs and return to UI vi
sqsDesigns.setupCompletedDesignsSQSPoller(io);

// Star the Server
server.listen(port);


// Helper function to de-dupe MQTT responses back via websockets (due to multiple threads of performance hub)
function generateMessageUID(messageData) {
    // Convert the entire messageData object to a string representation
    const messageString = JSON.stringify(messageData);

    // Use crypto to generate a hash of the entire messageString
    return crypto.createHash('sha256').update(messageString).digest('hex');
}
