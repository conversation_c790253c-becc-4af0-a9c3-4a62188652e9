# NOTE THESE CONFIGURATIONS ARE SHARED BETWEEN BOTH SERVERLESS & THE MAIN DOCKER CONTAINER / CODEBASE.
# UPDATED JAN 2022 - DN.

# Environment key is shared between Serverless Functions & Performance Hub Codebase.
# PLEASE USE THE `performanceHub:` KEY FOR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> HUB CONFIG THAT DOES NOT NEED TO BE DEPLOYED VIA SERVERLESS DUE TO 4KB ENVIRONMENT LIMIT IN LAMBDA.
environment:

  # SQS:
  SQS_MARKETING_PRINT_LAMBDA:             'marketing-print-prd'
  SQS_MARKETING_PRINT_EC2:                'marketing-print-ec2-prd'
  SQS_MARKETING_PRINT_COMPLETED:          'marketing-print-rendered-prd'
  SQS_GYMMASTER_MEMBER_DATA_SYNC_QUEUE:   'gm-member-data-sync-queue-prd'
  SQS_DAILY_SAAS_BILLING_LOG_QUEUE:       'daily-saas-billing-log-queue-prd'
  SQS_MONTHLY_SAAS_BILLER_QUEUE:          'monthly-saas-biller-queue-prd'

  ################################################################################################

  # API GATEWAY CONFIG
  APIGW_DOMAINNAME: "sls-api.hub.gymsystems.co"
  APIGW_CERTIFICATENAME: "sls-api.hub.gymsystems.co"
  APIGW_CERTIFICATEARN: "arn:aws:acm:ap-southeast-2:148959510102:certificate/298b23b7-6ebd-48da-a3fd-f4e15621705c"

  # This is required / is the static key to access the /3cx/lookup API hosted in serverless...
  DIRECTORY_LOOKUP_API_KEY: "6fc0e7ce-23f7-41dd-95a6-1ba944636759"

  # EC2 instance that will render designs should Lambda time-out / error...
  RENDER_WORKER_INSTANCE_ID:  'i-0639c0dd4e104870f'

  # S3
  S3_CCTV_RECORDING_BUCKET: 'universal-api-cctv-recording-history-prd'

  # Wasabi
  WASABI_ACCESS_KEY_ID: '2ARSFF73LYV4OFNO1QNB'
  WASABI_SECRET_KEY: '2te4L34GLe6ohK03UUpuoHWqS4QZk5aLGScn9rkS'

  # MARKETING & PRINT CONFIG
  MARKETING_S3_ASSETS_KEY_PREFIX: 'hub/design-suite-uploads'
  DESIGNER_GENERATE_QR: 'https://sls-api.hub.gymsystems.co/marketing-print/generate-qr'

  # Lambda Function names we call/invoke directly...
  LAM_RENDER_DESIGN: 'render-engine-prd-render-design'
  LAM_CREATE_TEMP_ACCESS_TOKEN: 'universal-api-prd-createTempAccessToken'

  # Generate Design HTML URL:
  DESIGNER_GENERATE_DESIGN_HTML: 'https://sls-api.hub.gymsystems.co/marketing-print/generate-design'

  ########################

  # DynamoDB Offline Config...
  DYNAMO_CUSTOM_CONFIG:           '{}'
  
  # MMS SQL Database
  MMS_SQL_DB_HOST: 'prd-member-management.cluster-cnyqwibqdvqc.ap-southeast-2.rds.amazonaws.com'
  MMS_SQL_DB_PWD: 'L69f4TghUAW5zM8BjY2D'

  # google map key
  SLS_GOOGLE_MAPS_API_KEY:        'AIzaSyBIJgUK8WUL4Bd_KUq9zBRe_slZ3o3jJsM'

  # TWILIO
  TWILIO_ACCOUNT_SID:             '**********************************'
  TWILIO_AUTH_TOKEN:              '72acc444437aa001270f48ff641ccdfe'

  # ABSTRACT - HOLIDAYS API
  ABSTRACT_HOLIDAYS_API_KEY:      '4fe09537fbcd468a9f99933089e92ba2'

  # Stripe
  STRIPE_SECRET_TOKEN:            '******************************************'
  STRIPE_PUBLIC_TOKEN:            'pk_live_AOH5lqsR3gNKsfpRowE3Qiuq00fAHeeS2K'
  STRIPE_SAAS_BILLING_CONNECTED_ACCOUNT: 'acct_1GLgXmH1qIQcGcdQ'

  # Redis
  REDIS_HOST:                     'redis://ph-prd-replication-group.fmfc0t.ng.0001.apse2.cache.amazonaws.com:6379'

  # YEXT API
  YEXT_API_KEY:                   '2396ef41a2f8768d1fb50309e918fae6'
  YEXT_BASE_URL:                  'https://api.yext.com/v2/accounts/me'

  # s3 Buckets
  S3_CLUB_KYC_BUCKET:             'ph-club-kyc-prd'
  S3_UPLOADS_BUCKET:              'ph-uploads-prd'

  # MQTT CONFIG:
  MQTT_WS_SERVER: wss://ws.prod.gymsystems.co/
  MQTT_USERNAME: client
  MQTT_PASSWORD: HsPhyZT5EoDMKbknJVti

  # OpenAI API
  OPENAI_API_KEY: ***************************************************

  # We're using Scylladb for non-critical data such as device metadata as it's about 10x chepaer than AWS hosted DynamoDB...
  SCYLLADB_HOST: scylladb-prd.aws.gymsystems.co
  SCYLLADB_PORT: 27028

  # This is required so SLS knows what configuration to load.
  # This must also be set here so it gets exported to aws lambda as an environment variable - do not remove!
  NODE_ENV:                       'production'


################################################################################################
# DATABASE TABLES (Dynamodb)...
# Please note these are separate to "environment variables" and are actually loaded as an environment variable during the lambda/oh startup/bootstrap scripts.

databases:

  # NB - Don't FORGET TO DEFINE DB SCHEMA + SEEDS IF CREATING NEW TABLES...
  # TABLES ARE AUTOMATICALLY CREATED/UPDATED IN AWS VIA JENKINS

  # Website Tables
  DB_CLUBS_TABLE:                 'website-club-data'
  DB_CUSTOM_TRACKING_TABLE:       'website-club-tracking-codes-prd'
  DB_FAQS_TABLE:                  'website-faqs-prd'
  DB_PRESS_TABLE:                 'website-press-prd'
  DB_EMAIL_MAPPING_TABLE:         'website-email-mapping-prd'
  DB_CLUB_BUSY_HOURS_TABLE:       'website-club-busy-hours-prd'
  DB_LANDING_PAGES_TABLE:         'website-landing-pages-prd'

  # Reviews Tables are Created/Administered via Universal API
  DB_CLUB_REVIEWS_TABLE:          'club-social-reviews-prd'
  DB_CLUB_REVIEWS_SETTINGS_TABLE: 'club-social-reviews-settings-prd'

  ### Core Performance Hub Features ###
  # Marketing & Print
  DB_DESIGN_TEMPLATES_TABLE:      'ph-design-templates-prd'
  DB_DESIGN_VARIABLES_TABLE:      'ph-design-variables-prd'
  DB_DESIGN_ASSETS_TABLE:         'ph-design-assets-prd'
  DB_DESIGN_STATIC_TABLE:         'ph-design-static-templates-prd'
  DB_DESIGN_RENDERS_TABLE:        'ph-design-renders-prd'
  DB_DESIGN_INPUT_MAPPING_TABLE:  'ph-design-input-mapping-prd'
  DB_DESIGN_CUSTOM_PAGES_TABLE:    'ph-design-custom-pages-prd'

  # IoT Devices
  DB_ROUND_DISPLAY_BASE_CONFIG:   'ph-round-display-overwrites-prd'
  DB_DISPLAY_UI_OPTIONS_TABLE:    'ph-display-ui-options-prd'
  DB_DISPLAY_DATA_SOURCES_TABLE:  'ph-display-data-sources-prd'
  DB_DEVICE_AUTOMATIONS_TABLE:    'ph-device-automations'
  DB_DEVICE_REGISTRATIONS_TABLE:  'uapi-coachingscreen-registrations-prd'
  DB_DEVICES_METADATA_TABLE:      'uapi-device-metadata-prd'
  DB_AUDIO_CONTROL_CONFIG_TABLE:  'uapi-audio-control-config-prd'
  DB_CCTV_BUCKET_REGION_TABLE:    'uapi-cctv-bucket-region-availability-prd'
  DB_CCTV_REG_TABLE:              'uapi-cctv-registrations-prd'
  DB_CCTV_REC_UPLOAD_TABLE:       'uapi-cctv-record-upload-prd'
  DB_CCTV_UI_ACTIONS_TABLE:       'uapi-cctv-ui-actions-prd'
  DB_CCTV_CONFIG_TABLE:           'uapi-cctv-config-prd'
  DB_CCTV_SESSION_TOKEN_TABLE:    'uapi-cctv-session-token-prd'
  DB_CCTV_TIMELAPSE_TABLE:        'uapi-cctv-timelapse-prd'
  DB_CCTV_PEOPLE_TABLE:           'cctv-rekognition-api-people-prd'
  DB_CCTV_FACE_DETECTIONS_TABLE:  'cctv-rekognition-api-face-detections-prd'
  DB_CCTV_UNMATCHED_DETS_TABLE:   'cctv-rekognition-api-unmatched-detections-prd'
  DB_CCTV_SHADOW_PHOTOS_TABLE:    'cctv-rekognition-api-shadow-photos-prd'
  DB_CCTV_BBOX_TIMELINE_TABLE:    'cctv-rekognition-api-bbox-timeline-prd'
  DB_CCTV_PERSON_PROCESS_TABLE:   'cctv-rekognition-api-person-process-prd'
  DB_CCTV_FACILITY_REPORT_TABLE:  'ph-cctv-facility-report-prd'

  # PH General
  DB_PERMISSIONS_TABLE:           'ph-access-permissions-prd'
  DB_USER_PREFERENCES_TABLE:      'ph-user-preferences-prd'
  DB_CLUB_PREFERENCES_TABLE:      'ph-club-preferences-prd'
  DB_COUNTRY_HOLIDAYS_TABLE:      'ph-country-holidays-prd'
  DB_ANNOUNCEMENTS_TABLE:         'ph-announcements-prd'
  DB_COUNTERS_TABLE:              'ph-counters-prd'
  DB_ORGANISATIONS_TABLE:         'ph-organisations-prd'
  DB_BRANDS_TABLE:                'ph-brands-prd'
  DB_ROLES_TABLE:                 'ph-roles-prd'

  # Messaging/SMS
  DB_SMS_TABLE:                   'ph-sms-history-prd'
  DB_SMS_SCHEDULE_TABLE:          'ph-sms-schedule-prd'

  # Stripe / Billing
  DB_CHARGES_CONFIG_TABLE:        'ph-charges-config-prd'

  # TC v1 (needs to be removed)
  # DB_TRAINING_CAMP_CHALLENGES_TABLE: 'trainingcamp-challenges-dev'
  # DB_TRAINING_CAMP_USERS_TABLE: 'trainingcamp-users-dev'

  # Single Table of the Member Data for all our users - Synced from GymMaster
  DB_GM_MEMBER_DATA_TABLE:        'gm-member-data'
  DB_GM_CLUB_REPORTING:           'gm-club-reporting-prd'
  DB_GM_VISIT_HISTORY:            'universal-api-member-swipes-prd'

  DB_WORKOUT_BOOKING_LEADS_TABLE: 'workout-booking-leads-prd'

  DB_AGREEMENTS_TABLE:              'ph-agreements'
  DB_AGREEMENTS_SIGNED_TABLE:       'ph-agreements-signed'

  # DB_ACADEMY_USERS_TABLE: 'the-academy-users-dev'
  DB_CLUB_APPS_CONFIG_TABLE: 'universal-api-club-apps-config-prd'
  # DB_WEBSITE_LEADS_TABLE: 'email-leads-dev'

  DB_NOTIFICATION_MESSAGES_TABLE: 'ph-notification-messages-prd'
  DB_CONSUMED_NOTIFICATIONS_TABLE: 'ph-consumed-notifications-prd'
  DB_CLUB_KYC_TABLE: 'ph-club-kyc-prd'
  DB_SECURE_UPLOADS_TABLE: 'ph-secure-uploads-prd'
  DB_IOT_REG_TABLE: 'uapi-iot-registrations-prd'
  DB_HISTORICAL_CLUB_HOLIDAYS_TABLE: 'ph-historical-club-holidays-prd'
  DB_SAAS_BILLING_TABLE: 'ph-saas-billing-prd'
  DB_CLUB_USER_AGREEMENT:         'club-user-agreement-prd'
  DB_CLUB_USER_AGREEMENT_CONFIG:  'cua-config-prd'
  DB_SAAS_BILLING_CONFIG_TABLE: 'ph-saas-billing-config-prd'
  DB_SAAS_BILLING_ADDITIONAL_LINE_ITEMS_TABLE: 'ph-saas-billing-additional-line-items-prd'
  DB_SAAS_BILLING_PAYMENT_SETTINGS_TABLE: 'ph-saas-billing-payment-settings-prd'
  DB_SAAS_BILLING_MONTHLY_RECORDS_TABLE: 'ph-saas-billing-monthly-records-prd'

  DB_RELEASE_HISTORY:             'ph-release-history-prd'
  DB_PROGRAM_BUILDER_CALENDAR:    'builder-calendar-prd'
  CALENDAR_TYPE_TABLE:            'builder-calendar-type-prd'

  # TOD/TC
  DB_TRAIN_FREE_USERS_TABLE:      'train-v1-free-members'
  DB_TRAINING_CAMP_LEADS_TABLE:   'training-camp-leads'

  # GM Memberships
  DB_GM_MEMBERSHIP_DATA_TABLE:        'gm-membership-data-prd'
  DB_GM_MEMBERSHIP_ACCESS_TYPES_TABLE: 'gm-membership-access-types-prd'

  # Access Control
  DB_TEMP_ACCESS_TOKEN_TABLE:     'uapi-temp-access-token-prd'


  # Training Camp
  DB_TRAINING_CAMP_SEASON_DATA: 'tc-api-v2-season-data-prd'
  USER_PREFERENCES_TABLE: 'tc-api-v2-user-preferences-prd'
  LEADERBOARD_TABLE: 'leaderboards-prd'

  # MMS MySQL tables
  DB_MMS_USERS_TABLE: 'mms-api-user-prd'

################################################################################################
# These variables are loaded into the core performance hub, but not any secondary lambda functions...

performanceHub:
  # For signing and verifying JWT tokens for temporary or guest access
  PH_TEMP_ACCESS_SECRET_KEY:      '68Ev7MNHTSsXvj+0tJqDr0T+e5rdH5afS6U3X8dmdos='

  # BALENA API
  BALENA_API_KEY:                 'FH40o9zB3Ul0AjQrkpxDCfR1uGEudtuo'
  # Applications to monitor for releases.. (comma separated list is ok)
  BALENA_APPLICATIONS_TO_MONITOR: 'ubx/coachingscreens-prd,ubx/cctv-prd'

  # AWS
  # AWS_PROFILE: 12round
  AWS_REGION: ap-southeast-2

  # Performance Hub FQDN URL
  PH_PROD_URL: 'https://portal.hub.gymsystems.co'

  # Lambda Function names we call/invoke directly from within the performance hub odebase...
  LAM_SYNC_GM_MEMBERS:                  'ph-prd-syncGMMembers'
  LAM_SYNC_GM_REVENUE:                  'ph-prd-syncGMRevenueHistory'
  LAM_UPDATE_KNOWN_COUNTRY_HOLIDAYS:    'ph-prd-updateCountryHolidays'
  LAM_UPDATE_CLUB_HOLIDAYS:             'ph-prd-updateClubHolidays'
  LAM_UPDATE_SOCIAL_REVIEWS_FROM_YEXT:  'ph-prd-updateSocialReviews'
  LAM_AUTO_PUBLISH_REVIEWS:             'ph-prd-autoPublishedReviews'
  LAM_SMS_SCHEDULER:                    'ph-prd-smsSchedule'
  LAM_PAYMENT_REPORT:                   'ph-prd-dailyPaymentReport'
  LAM_LOW_BATTERY_DURESS:               'ph-prd-lowBatteryDuressNotifs'
  LAM_SAAS_BILLING_LOG:                 'ph-prd-startDailySaasBillingLogQueue'
  LAM_DELETE_OLD_NOTIFICATIONS:         'ph-prd-deleteOldNotifications'
  LAM_PEAK_TIME_INSIGHTS:               'ph-prd-clubsBusyHours'
  LAM_SAAS_MONTHLY_BILLER:              'ph-prd-startMonthlySaasBillerQueue'
  LAM_CCTV_HEATMAPS:                    'ph-prd-cctvHeatmapGeneration'
  LAM_CACHE_MMS_USERS:                  'ph-prd-cacheMMSUsers'

  # WIKI
  WIKI_ENDPOINT: 'http://openkb:4444'

  # WEKAN - KANBAN
  KANBAN_ENDPOINT: 'http://wekan-app:8080'

  # EVENTS MANAGER
  EVENTS_MANAGER_ENDPOINT: 'http://events-manager:4089'

  # Static API Key used for internal routes that do not use Google SSO headers...
  STATIC_API_KEY_AUTH: 32aa84c47da94a65b71a0269f6960859

  # Ticketing
  OSTICKET_API_URL: https://ticketing.gymsystems.co/api/tickets
  OSTICKET_API_KEY: 26F4D4850450DC99091A2EB10A9F0436

  # OSTICKET API - use your local IP Address instead of localhost
  # OSTICKET_API_URL: 'http://*************:8081/api/tickets'
  # OSTICKET_API_KEY: 'E7DE56092B1B609FBE059456D37918CA'

  # S3 Shared Config
  S3_ASSETS_BUCKET: 'content.gymsystems.co'
  S3_ASSETS_KEY_PREFIX: 'hub/announcements-uploads'
  S3_AGREEMENTS_BUCKET: 'content.gymsystems.co'
  S3_AGREEMENTS_KEY_PREFIX: 'hub/agreements'
  S3_UPLOAD_ASSETS_BUCKET: 'upload-api-assets-prd'

  # LOCAL SERVERLESS MARKETING & PRINT RENDERER
  SLS_PROXY_SERVICE_PORT: 3030
  SLS_PROXY_TO_URL: 'http://127.0.0.1:3333'

  #APPS
  #Training Camp Default DB Region
  TRAINING_CAMP_REGION: 'us-east-1'

  # MMS API
  MMS_API_URL: 'https://ztuwyy4ne0.execute-api.ap-southeast-2.amazonaws.com/prd'
  MMS_API_KEY: 'rjgiXvrB6dabjXsBSLFRA7STzKne6tE5Rn26Crlc'

  # CLUBPASS API
  CLUBPASS_API: 'https://flcv31f8m8.execute-api.ap-southeast-2.amazonaws.com/prd'
  CLUBPASS_API_KEY: '83E7qGSQA810umHzMg5zFkWiEYn6Jbh1Bul64Fr3'

  # Store
  EXPRESSCART_ENDPOINT: 'http://expresscart-core:1111/'
  EXPRESSCART_API_KEY: '287ed642-41b1-bcc9-7b26-99a087a2103a'

  # Program Builder
  PROGRAM_BUILDER_ENDPOINT: 'http://program-builder:3089'

  # Google Auth
  GOOGLE_APPLICATION_CREDENTIALS: 'credentials/service-account.json'

  # ASSET UPLOAD API
  ASSET_UPLOADS_API: 'https://uapi.gymsystems.co/upload/v1'
  ASSET_UPLOADS_API_KEY: 'SnQ5FDdcZh7bY8boaS1uc5hw3NwlkHk052jG17rV'

  # Universal API - Environment Dependent (DEV, STG, PRD)
  UAPI_URL: https://uapi.gymsystems.co 
  UAPI_KEY: lglK8vAyOm6zpTyNZRpmL7w1GKD558AUYPvI4M9a 

  # Email Config
  SES_CONFIG_REGION:              'us-east-1'
  LINKING_DELIVER_TO_ADDRESS:     '<EMAIL>'
  PH_SYSTEM_EMAILS_FROM_ADDRESS:  '<EMAIL>'

  # Google maps API
  GOOGLE_MAPS_API_KEY: AIzaSyBIJgUK8WUL4Bd_KUq9zBRe_slZ3o3jJsM
  GOOGLE_MAPS_STREETVIEW_KEY: AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys

  # Graylog
  GRAYLOG_SERVER: 'graylog.aws.gymsystems.co'
  GRAYLOG_PROTOCOL: 'http'
  GRAYLOG_PORT: 12201

  # Universal API
  UNIVERSAL_API_URL: https://uapi.gymsystems.co

  # Redash
  REDASH_PROXY_PUBLIC_DOMAIN: https://redash.hub.gymsystems.co/

  # https://api.ipdata.co
  IPDATA_API_KEY: e7a213cf0b4f83ff0e1b983b79e31272107c4c44c516017f3b8cfac3

  # EXTENDED ACCESS
  EXTENDED_ACCESS_ENDPOINT: 'http://extended-access-fe:3092'

  # CCTV FRONTEND
  CCTV_FE_ENDPOINT: 'http://cctv-fe:3092'

  # AUDIO CONTROL FRONTEND
  AUDIO_CONTROL_FE_ENDPOINT: 'http://audio-control-fe:3042'

  # COACHING SCREENS FRONTEND
  COACHINGSCREENS_FE_ENDPOINT: 'http://coaching-screens-fe:3052'

  # HEALTH CHECK FRONTEND
  HEALTHCHECK_FE_ENDPOINT: 'http://health-check-fe:3062'

  # S3 Encryption
  ENCRYPTION_PASSWORD: '39750463-25d2-4347-8076-25483a291e71'

  # Algolia
  ALGOLIA_APP_ID: 'R6LBPN4AJJ'
  ALGOLIA_API_KEY: '********************************'
  ALGOLIA_WIKI_INDEX: 'wiki-prd-kb'
  ALGOLIA_MARKETINGPRINT_INDEX: 'marketingprint-prd'

  # Rekognition
  IDENTIFIED_COLLECTION: 'cctv-identified-prd'
  UNIDENTIFIED_COLLECTION: 'cctv-unidentified-prd'
