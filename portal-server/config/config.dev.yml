# NOTE THESE CONFIGURATIONS ARE SHARED BETWEEN BOTH SERVERLESS & THE MAIN DOCKER CONTAINER / CODEBASE.
# UPDATED JAN 2022 - DN.

# Environment key is shared between Serverless Functions & Performance Hub Codebase
# PLEASE USE THE `performanceHub:` KEY FOR PERFORMANCE HUB CONFIG THAT DOES NOT NEED TO BE DEPLOYED VIA SERVERLESS DUE TO 4KB ENVIRONMENT LIMIT IN LAMBDA.
environment:

  # SQS - Queues:
  SQS_MARKETING_PRINT_LAMBDA:             'marketing-print-dev'
  SQS_MARKETING_PRINT_EC2:                'marketing-print-ec2-dev'
  SQS_MARKETING_PRINT_COMPLETED:          'marketing-print-rendered-dev'
  SQS_GYMMASTER_MEMBER_DATA_SYNC_QUEUE:   'gm-member-data-sync-queue-dev'
  SQS_DAILY_SAAS_BILLING_LOG_QUEUE:       'daily-saas-billing-log-queue-dev'
  SQS_MONTHLY_SAAS_BILLER_QUEUE:          'monthly-saas-biller-queue-dev'

  ################################################################################################

  # API GATEWAY CONFIG
  APIGW_DOMAINNAME: "sls-api.hub.stage.gymsystems.co"
  APIGW_CERTIFICATENAME: "sls-api.hub.stage.gymsystems.co"
  APIGW_CERTIFICATEARN: "arn:aws:acm:ap-southeast-2:148959510102:certificate/71a56d29-336b-4778-b026-d62adefed9c5"

  # This is required / is the static key to access the /3cx/lookup API hosted in serverless...
  DIRECTORY_LOOKUP_API_KEY: "a2d9a398-d767-4c5b-9ff6-ed6e80db97d7"

  # EC2 instance that will render designs should Lambda time-out / error...
  RENDER_WORKER_INSTANCE_ID:  'i-0639c0dd4e104870f'

  # MARKETING & PRINT CONFIG
  MARKETING_S3_ASSETS_KEY_PREFIX: 'hub/design-suite-uploads'
  DESIGNER_GENERATE_QR: 'http://portal-server-sls:3030/marketing-print/generate-qr'

  # Lambda Function names we call/invoke directly...
  LAM_RENDER_DESIGN: 'render-engine-dev-render-design'
  # Generate Design HTML URL:
  DESIGNER_GENERATE_DESIGN_HTML: 'http://portal-server-sls:3030/marketing-print/generate-design'

  # LAMBDA Endpoint (not set in prod as we default to the AWS Lambda endpoints)....
  RENDER_HOST_LAMBDA_SERVER: 'http://render-engine:3002/'
  PH_LAMBDA_SERVER: 'http://portal-server-sls:3002/'

  # DynamoDB Offline Config...
  DYNAMO_CUSTOM_CONFIG: '{ "endpoint":"http://portal-local-db:27028", "region":"localhost", "accessKeyId":"MockAccessKeyId", "secretAccessKey":"MockSecretAccessKey" }'

  # MMS SQL Database
  MMS_SQL_DB_HOST: 'stg-member-management.cluster-cnyqwibqdvqc.ap-southeast-2.rds.amazonaws.com'
  MMS_SQL_DB_PWD: 'ekdId9sOMCXVDPI2Nemv'

  # ElastiCache / Redis
  REDIS_HOST: redis://portal-local-redis:6379

  # google map key
  SLS_GOOGLE_MAPS_API_KEY: AIzaSyBIJgUK8WUL4Bd_KUq9zBRe_slZ3o3jJsM

  #TWILIO
  TWILIO_ACCOUNT_SID:         '**********************************'
  TWILIO_AUTH_TOKEN:          '72acc444437aa001270f48ff641ccdfe'

  # ABSTRACT - HOLIDAYS API
  ABSTRACT_HOLIDAYS_API_KEY: 4fe09537fbcd468a9f99933089e92ba2

  # Stripe
  # STRIPE_SECRET_TOKEN: ******************************************
  STRIPE_SECRET_TOKEN: sk_test_hKcbtTZ9cBkBcWd5z2PYBaNq00XXKCoqhW
  STRIPE_PUBLIC_TOKEN: pk_test_JxoNE205lcXVTCtd1e6qj1zW001UGAhWU7
  STRIPE_SAAS_BILLING_CONNECTED_ACCOUNT: acct_1GLhBiC12Th0Sbp7

  # MQTT CONFIG:
  MQTT_WS_SERVER: wss://ws.stage.gymsystems.co/
  MQTT_USERNAME: client
  MQTT_PASSWORD: HsPhyZT5EoDMKbknJVti

  # S3 Buckets
  S3_CLUB_KYC_BUCKET: 'ph-club-kyc-stg'
  S3_UPLOADS_BUCKET: 'ph-uploads-dev'
  S3_CCTV_RECORDING_BUCKET: 'universal-api-cctv-recording-history-stg'

  # Wasabi
  WASABI_ACCESS_KEY_ID: '2ARSFF73LYV4OFNO1QNB'
  WASABI_SECRET_KEY: '2te4L34GLe6ohK03UUpuoHWqS4QZk5aLGScn9rkS'

  # YEXT API
  YEXT_API_KEY: 4d04a82a78f6fee2d4e01dd09e1618b4
  YEXT_BASE_URL: https://sbx-api.yextapis.com/v2/accounts/me

  # OpenAI API
  OPENAI_API_KEY: ***************************************************

  # We're using Scylladb for non-critical data such as device metadata as it's about 10x chepaer than AWS hosted DynamoDB...
  SCYLLADB_HOST: scylladb-stg.aws.gymsystems.co
  SCYLLADB_PORT: 27028

  # This is required so SLS knows what configuration to load.
  # This must also be set here so it gets exported to aws lambda as an environment variable - do not remove!
  NODE_ENV:                       'development'


################################################################################################
# DATABASE TABLES (Dynamodb)...
# Please note these are separate to "environment variables" and are actually loaded as an environment variable during the lambda/oh startup/bootstrap scripts.

databases:

  # NB - Don't FORGET TO DEFINE DB SCHEMA + SEEDS IF CREATING NEW TABLES...
  # TABLES ARE AUTOMATICALLY CREATED/UPDATED IN AWS VIA JENKINS

  # Website Tables
  DB_CLUBS_TABLE:                 'website-club-data-dev'
  DB_CUSTOM_TRACKING_TABLE:       'website-club-tracking-codes-dev'
  DB_FAQS_TABLE:                  'website-faqs-dev'
  DB_PRESS_TABLE:                 'website-press-dev'
  DB_EMAIL_MAPPING_TABLE:         'website-email-zmapping-dev'
  DB_CLUB_BUSY_HOURS_TABLE:       'website-club-busy-hours-dev'
  DB_LANDING_PAGES_TABLE:         'website-landing-pages-dev'

  # Reviews Tables are Created/Administered via Universal API
  DB_CLUB_REVIEWS_TABLE:          'club-social-reviews-dev'
  DB_CLUB_REVIEWS_SETTINGS_TABLE: 'club-social-reviews-settings-dev'

  ### Core Performance Hub Features ###
  # Marketing & Print
  DB_DESIGN_TEMPLATES_TABLE:      'ph-design-templates-dev'
  DB_DESIGN_VARIABLES_TABLE:      'ph-design-variables-dev'
  DB_DESIGN_ASSETS_TABLE:         'ph-design-assets-dev'
  DB_DESIGN_STATIC_TABLE:         'ph-design-static-templates-dev'
  DB_DESIGN_RENDERS_TABLE:        'ph-design-renders-dev'
  DB_DESIGN_INPUT_MAPPING_TABLE:  'ph-design-input-mapping-dev'
  DB_DESIGN_CUSTOM_PAGES_TABLE:    'ph-design-custom-pages-dev'

  # IoT Devices
  DB_ROUND_DISPLAY_BASE_CONFIG:   'ph-round-display-overwrites-dev'
  DB_DISPLAY_UI_OPTIONS_TABLE:    'ph-display-ui-options-dev'
  DB_DISPLAY_DATA_SOURCES_TABLE:  'ph-display-data-sources-dev'
  DB_DEVICE_AUTOMATIONS_TABLE:    'ph-device-automations-dev'
  DB_DEVICE_REGISTRATIONS_TABLE:  'uapi-coachingscreen-registrations-dev'
  DB_DEVICES_METADATA_TABLE:      'uapi-device-metadata-dev'
  DB_AUDIO_CONTROL_CONFIG_TABLE:  'uapi-audio-control-config-dev'
  DB_CCTV_BUCKET_REGION_TABLE:    'uapi-cctv-bucket-region-availability-dev'
  DB_CCTV_REG_TABLE:              'uapi-cctv-registrations-dev'
  DB_CCTV_REC_UPLOAD_TABLE:       'uapi-cctv-record-upload-dev'
  DB_CCTV_REC_UPLOAD_CFG_TABLE:   'uapi-cctv-record-upload-config-dev'
  DB_CCTV_UI_ACTIONS_TABLE:       'uapi-cctv-ui-actions-dev'
  DB_CCTV_CONFIG_TABLE:           'uapi-cctv-config-dev'
  DB_CCTV_SESSION_TOKEN_TABLE:    'uapi-cctv-session-token-dev'
  DB_CCTV_TIMELAPSE_TABLE:        'uapi-cctv-timelapse-dev'
  DB_CCTV_PEOPLE_TABLE:           'cctv-rekognition-api-people-dev'
  DB_CCTV_FACE_DETECTIONS_TABLE:  'cctv-rekognition-api-face-detections-dev'
  DB_CCTV_UNMATCHED_DETS_TABLE:   'cctv-rekognition-api-unmatched-detections-dev'
  DB_CCTV_SHADOW_PHOTOS_TABLE:    'cctv-rekognition-api-shadow-photos-dev'
  DB_CCTV_BBOX_TIMELINE_TABLE:    'cctv-rekognition-api-bbox-timeline-dev'
  DB_CCTV_PERSON_PROCESS_TABLE:   'cctv-rekognition-api-person-process-dev'
  DB_CCTV_FACILITY_REPORT_TABLE:  'ph-cctv-facility-report-dev'


  # PH General
  DB_PERMISSIONS_TABLE:           'ph-access-permissions-dev'
  DB_USER_PREFERENCES_TABLE:      'ph-user-preferences-dev'
  DB_CLUB_PREFERENCES_TABLE:      'ph-club-preferences-dev'
  DB_COUNTRY_HOLIDAYS_TABLE:      'ph-country-holidays-dev'
  DB_ANNOUNCEMENTS_TABLE:         'ph-announcements-dev'
  DB_COUNTERS_TABLE:              'ph-counters-dev'
  DB_ORGANISATIONS_TABLE:         'ph-organisations-dev'
  DB_BRANDS_TABLE:                'ph-brands-dev'
  DB_ROLES_TABLE:                 'ph-roles-dev'

  # Messaging/SMS
  DB_SMS_TABLE:                   'ph-sms-history-dev'
  DB_SMS_SCHEDULE_TABLE:          'ph-sms-schedule-dev'

  # Stripe / Billing
  DB_CHARGES_CONFIG_TABLE:        'ph-charges-config-dev'

  # TOD/TC
  DB_TRAIN_FREE_USERS_TABLE:      'train-v1-free-members-dev'
  DB_TRAINING_CAMP_LEADS_TABLE:   'training-camp-leads-dev'

  # TC v1 (needs to be removed)
  DB_TRAINING_CAMP_USERS_TABLE:      'trainingcamp-users-dev'
  DB_TRAINING_CAMP_CHALLENGES_TABLE: 'trainingcamp-challenges-dev'

  # Single Table of the Member Data for all our users - Synced from GymMaster
  DB_GM_MEMBER_DATA_TABLE:        'gm-member-data-dev'
  DB_GM_CLUB_REPORTING:           'gm-club-reporting-dev'
  DB_GM_VISIT_HISTORY:            'universal-api-member-swipes-dev'

  DB_ACADEMY_USERS_TABLE:         'the-academy-users-dev'
  DB_CLUB_APPS_CONFIG_TABLE:      'universal-api-club-apps-config-dev'
  DB_WORKOUT_BOOKING_LEADS_TABLE: 'workout-booking-leads-dev'
  DB_WEBSITE_LEADS_TABLE: 'email-leads-dev'

  DB_AGREEMENTS_TABLE:              'ph-agreements-dev'
  DB_AGREEMENTS_SIGNED_TABLE:       'ph-agreements-signed-dev'

  DB_NOTIFICATION_MESSAGES_TABLE:   'ph-notification-messages-dev'
  DB_CONSUMED_NOTIFICATIONS_TABLE:  'ph-consumed-notifications-dev'
  DB_CLUB_KYC_TABLE:                'ph-club-kyc-dev'
  DB_SECURE_UPLOADS_TABLE:          'ph-secure-uploads-dev'
  DB_IOT_REG_TABLE:                 'uapi-iot-registrations-dev'
  DB_HISTORICAL_CLUB_HOLIDAYS_TABLE: 'ph-historical-club-holidays-dev'
  DB_CLUB_USER_AGREEMENT:           'club-user-agreement-dev'
  DB_CLUB_USER_AGREEMENT_CONFIG:    'cua-config-dev'

  DB_SAAS_BILLING_TABLE:                        'ph-saas-billing-dev'
  DB_SAAS_BILLING_CONFIG_TABLE:                 'ph-saas-billing-config-dev'
  DB_SAAS_BILLING_ADDITIONAL_LINE_ITEMS_TABLE:  'ph-saas-billing-additional-line-items-dev'
  DB_SAAS_BILLING_PAYMENT_SETTINGS_TABLE:       'ph-saas-billing-payment-settings-dev'
  DB_SAAS_BILLING_MONTHLY_RECORDS_TABLE:        'ph-saas-billing-monthly-records-dev'

  DB_RELEASE_HISTORY:             'ph-release-history-dev'
  DB_PROGRAM_BUILDER_CALENDAR:    'builder-calendar-stg'
  CALENDAR_TYPE_TABLE:            'builder-calendar-type-dev'

  # GM Memberships
  DB_GM_MEMBERSHIP_DATA_TABLE:        'gm-membership-data-dev'
  DB_GM_MEMBERSHIP_ACCESS_TYPES_TABLE: 'gm-membership-access-types-dev'

  # Training Camp
  DB_TRAINING_CAMP_SEASON_DATA: 'tc-api-v2-season-data-dev'
  USER_PREFERENCES_TABLE: 'tc-api-v2-user-preferences-dev'
  LEADERBOARD_TABLE: 'leaderboards-dev'

  # MMS MySQL tables
  DB_MMS_USERS_TABLE: 'mms-api-user-stg'


################################################################################################
# These variables are loaded into the core performance hub, but not any secondary lambda functions...

performanceHub:

  # BALENA API
  BALENA_API_KEY:                 'FH40o9zB3Ul0AjQrkpxDCfR1uGEudtuo'
  # Applications to monitor for releases.. (comma separated list is ok)
  BALENA_APPLICATIONS_TO_MONITOR: 'ubx/coachingscreens-stg,ubx/cctv-stg'

  # AWS
  # AWS_PROFILE: 12round
  AWS_REGION: ap-southeast-2

  # Performance Hub FQDN URL
  PH_PROD_URL: 'https://portal.hub.stage.gymsystems.co'

  # SWAGGER SHOULD BE IN DEV MODE ONLY
  SWAGGER_UI: true

  # Lambda Function names we call/invoke directly from within the performance hub codebase...
  LAM_SYNC_GM_MEMBERS:                  'ph-dev-syncGMMembers'
  LAM_SYNC_GM_REVENUE:                  'ph-dev-syncGMRevenueHistory'
  LAM_UPDATE_KNOWN_COUNTRY_HOLIDAYS:    'ph-dev-updateCountryHolidays'
  LAM_UPDATE_CLUB_HOLIDAYS:             'ph-dev-updateClubHolidays'
  LAM_UPDATE_SOCIAL_REVIEWS_FROM_YEXT:  'ph-dev-updateSocialReviews'
  LAM_AUTO_PUBLISH_REVIEWS:             'ph-dev-autoPublishedReviews'
  LAM_SMS_SCHEDULER:                    'ph-dev-smsSchedule'
  LAM_PAYMENT_REPORT:                   'ph-dev-dailyPaymentReport'
  LAM_LOW_BATTERY_DURESS:               'ph-dev-lowBatteryDuressNotifs'
  LAM_SAAS_BILLING_LOG:                 'ph-dev-startDailySaasBillingLogQueue'
  LAM_DELETE_OLD_NOTIFICATIONS:         'ph-dev-deleteOldNotifications'
  LAM_PEAK_TIME_INSIGHTS:               'ph-dev-clubsBusyHours'
  LAM_SAAS_MONTHLY_BILLER:              'ph-dev-startMonthlySaasBillerQueue'
  LAM_CCTV_HEATMAPS:                    'ph-dev-cctvHeatmapGeneration'
  LAM_CACHE_MMS_USERS:                  'ph-dev-cacheMMSUsers'

  # WIKI
  WIKI_ENDPOINT: 'http://openkb:4444'

  # WEKAN - KANBAN
  KANBAN_ENDPOINT: 'http://wekan-app:8080'

  # EVENTS MANAGER
  EVENTS_MANAGER_ENDPOINT: 'http://events-manager:4089'

  # Static API Key used for internal routes that do not use Google SSO headers...
  STATIC_API_KEY_AUTH: 32aa84c47da94a65b71a0269f6960859

  # Ticketing
  OSTICKET_API_URL: https://ticketing.gymsystems.co/api/tickets
  OSTICKET_API_KEY: 26F4D4850450DC99091A2EB10A9F0436

  # OSTICKET API - use your local IP Address instead of localhost
  # OSTICKET_API_URL: 'http://*************:8081/api/tickets'
  # OSTICKET_API_KEY: 'E7DE56092B1B609FBE059456D37918CA'

  # S3 Shared Config
  S3_ASSETS_BUCKET: 'content.gymsystems.co'
  S3_ASSETS_KEY_PREFIX: 'hub/announcements-uploads'
  S3_AGREEMENTS_BUCKET: 'content.gymsystems.co'
  S3_AGREEMENTS_KEY_PREFIX: 'hub/agreements'
  S3_UPLOAD_ASSETS_BUCKET: 'upload-api-assets-stg'

  # LOCAL SERVERLESS MARKETING & PRINT RENDERER
  SLS_PROXY_SERVICE_PORT: 3030
  SLS_PROXY_TO_URL: 'http://127.0.0.1:3333'

  #APPS
  #Training Camp Default DB Region
  TRAINING_CAMP_REGION: 'us-east-1'

  # MMS API
  MMS_API_URL: 'https://r2zop13g00.execute-api.ap-southeast-2.amazonaws.com/stg'
  MMS_API_KEY: 'UjNa0uV97f8cWRoN1v1uo7qiLbXLjNIIaNLwSwkd'

  # CLUBPASS API
  CLUBPASS_API: 'https://sky8nr6hh4.execute-api.ap-southeast-2.amazonaws.com/stg'
  CLUBPASS_API_KEY: '86d0DXCVddayHJcriYtVI1JrIIzSo0g9qvmxEP7h'

  # Store
  EXPRESSCART_ENDPOINT: 'http://expresscart-core:1111/'
  EXPRESSCART_API_KEY: '44d9b63e-f2cf-2f50-9fac-86f8b605cb8c'

  # Program Builder
  PROGRAM_BUILDER_ENDPOINT: 'http://program-builder-frontend:3001'

  # Google Auth
  GOOGLE_APPLICATION_CREDENTIALS: 'credentials/service-account.json'

  # ASSET UPLOAD API
  ASSET_UPLOADS_API: 'https://uapi.stage.gymsystems.co/upload/v1'
  ASSET_UPLOADS_API_KEY: 'SnQ5FDdcZh7bY8boaS1uc5hw3NwlkHk052jG17rV'

  # Universal API - Environment Dependent (DEV, STG, PRD)
  # For dev, it points to staging, change this when you need it
  UAPI_URL: https://uapi.stage.gymsystems.co 
  UAPI_KEY: FgF9zjHdbo91Yhz3JL5dI7n7bOohbrZO2Z8HPypB

  # Email Config
  SES_CONFIG_REGION:              'us-east-1'
  LINKING_DELIVER_TO_ADDRESS:     '<EMAIL>'
  PH_SYSTEM_EMAILS_FROM_ADDRESS:  '<EMAIL>'

  # Google maps API
  GOOGLE_MAPS_API_KEY: AIzaSyBIJgUK8WUL4Bd_KUq9zBRe_slZ3o3jJsM
  GOOGLE_MAPS_STREETVIEW_KEY: AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys

  # Graylog
  GRAYLOG_SERVER: 'graylog.aws.gymsystems.co'
  GRAYLOG_PROTOCOL: 'http'
  GRAYLOG_PORT: 12201

  # Universal API
  UNIVERSAL_API_URL: http://localhost:3100/dev

  # Redash
  REDASH_PROXY_PUBLIC_DOMAIN: https://redash.hub.gymsystems.co/

  # https://api.ipdata.co
  IPDATA_API_KEY: e7a213cf0b4f83ff0e1b983b79e31272107c4c44c516017f3b8cfac3

  # EXTENDED ACCESS
  EXTENDED_ACCESS_ENDPOINT: 'http://extended-access-fe:3092'

  # CCTV FRONTEND
  CCTV_FE_ENDPOINT: 'http://cctv-fe:3092'

  # S3 Encryption
  ENCRYPTION_PASSWORD: 'c5d27df4-b845-467c-954b-9e389fd044f1'

  # Algolia
  ALGOLIA_APP_ID: 'R6LBPN4AJJ'
  ALGOLIA_API_KEY: '********************************'
  ALGOLIA_WIKI_INDEX: 'wiki-dev-kb'
  ALGOLIA_MARKETINGPRINT_INDEX: 'marketingprint-dev'
