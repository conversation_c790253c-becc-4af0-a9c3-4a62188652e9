# NOTE THESE CONFIGURATIONS ARE SHARED BETWEEN BOTH SERVERLESS & THE MAIN DOCKER CONTAINER / CODEBASE.
# UPDATED JAN 2022 - DN.

# Environment key is shared between Serverless Functions & Performance Hub Codebase.
# PLEASE USE THE `performanceHub:` KEY FOR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> HUB CONFIG THAT DOES NOT NEED TO BE DEPLOYED VIA SERVERLESS DUE TO 4KB ENVIRONMENT LIMIT IN LAMBDA.
environment:

  # SQS:
  SQS_MARKETING_PRINT_LAMBDA:             'marketing-print-stg'
  SQS_MARKETING_PRINT_EC2:                'marketing-print-ec2-stg'
  SQS_MARKETING_PRINT_COMPLETED:          'marketing-print-rendered-stg'
  SQS_GYMMASTER_MEMBER_DATA_SYNC_QUEUE:   'gm-member-data-sync-queue-stg'
  SQS_DAILY_SAAS_BILLING_LOG_QUEUE:       'daily-saas-billing-log-queue-stg'
  SQS_MONTHLY_SAAS_BILLER_QUEUE:          'monthly-saas-biller-queue-stg'

  ################################################################################################

  # API GATEWAY CONFIG
  APIGW_DOMAINNAME: 'sls-api.hub.stage.gymsystems.co'
  APIGW_CERTIFICATENAME: 'sls-api.hub.stage.gymsystems.co'
  APIGW_CERTIFICATEARN: 'arn:aws:acm:ap-southeast-2:148959510102:certificate/71a56d29-336b-4778-b026-d62adefed9c5'

  # This is required / is the static key to access the /3cx/lookup API hosted in serverless...
  DIRECTORY_LOOKUP_API_KEY: "b63c269a-5e99-4ffa-9655-e9d9003f07f0"

  # EC2 instance that will render designs should Lambda time-out / error...
  RENDER_WORKER_INSTANCE_ID:   'i-0639c0dd4e104870f'

  # S3
  S3_CCTV_RECORDING_BUCKET: 'universal-api-cctv-recording-history-stg'

  # Wasabi
  WASABI_ACCESS_KEY_ID: '2ARSFF73LYV4OFNO1QNB'
  WASABI_SECRET_KEY: '2te4L34GLe6ohK03UUpuoHWqS4QZk5aLGScn9rkS'

  # MARKETING & PRINT CONFIG
  MARKETING_S3_ASSETS_KEY_PREFIX: 'hub/design-suite-uploads'
  DESIGNER_GENERATE_QR: 'https://sls-api.hub.stage.gymsystems.co/marketing-print/generate-qr'

  # Lambda Function names we call/invoke directly...
  LAM_RENDER_DESIGN: 'render-engine-stg-render-design'
  LAM_CREATE_TEMP_ACCESS_TOKEN: 'universal-api-stg-createTempAccessToken'

  # Generate Design HTML URL:
  DESIGNER_GENERATE_DESIGN_HTML: 'https://sls-api.hub.stage.gymsystems.co/marketing-print/generate-design'

  #PH_LAMBDA_SERVER: 'http://portal-server-sls:3002/'

  ########################

  # DynamoDB Offline Config...
  DYNAMO_CUSTOM_CONFIG:           '{}'

  # MMS SQL Database
  MMS_SQL_DB_HOST: 'stg-member-management.cluster-cnyqwibqdvqc.ap-southeast-2.rds.amazonaws.com'
  MMS_SQL_DB_PWD: 'ekdId9sOMCXVDPI2Nemv'

  # google map key
  SLS_GOOGLE_MAPS_API_KEY:        'AIzaSyBIJgUK8WUL4Bd_KUq9zBRe_slZ3o3jJsM'

  # TWILIO
  TWILIO_ACCOUNT_SID:             '**********************************'
  TWILIO_AUTH_TOKEN:              '72acc444437aa001270f48ff641ccdfe'

  # ABSTRACT - HOLIDAYS API
  ABSTRACT_HOLIDAYS_API_KEY:      '4fe09537fbcd468a9f99933089e92ba2'

  # Stripe
  # STRIPE_SECRET_TOKEN:            '******************************************'
  STRIPE_SECRET_TOKEN:            'sk_test_hKcbtTZ9cBkBcWd5z2PYBaNq00XXKCoqhW'
  STRIPE_PUBLIC_TOKEN:            'pk_test_JxoNE205lcXVTCtd1e6qj1zW001UGAhWU7'
  STRIPE_SAAS_BILLING_CONNECTED_ACCOUNT: 'acct_1GLhBiC12Th0Sbp7'

  # Redis
  REDIS_HOST:                     'redis://ph-stg-replication-group.fmfc0t.ng.0001.apse2.cache.amazonaws.com:6379'

  # YEXT API
  YEXT_API_KEY:                   '5b8613ba049b3299338954d30ba19956'
  YEXT_BASE_URL:                  'https://sbx-api.yextapis.com/v2/accounts/me'

  # s3 Buckets
  S3_CLUB_KYC_BUCKET:             'ph-club-kyc-stg'
  S3_UPLOADS_BUCKET:              'ph-uploads-stg'

  # MQTT CONFIG:
  MQTT_WS_SERVER: wss://ws.stage.gymsystems.co/
  MQTT_USERNAME: client
  MQTT_PASSWORD: HsPhyZT5EoDMKbknJVti

  # OpenAI API
  OPENAI_API_KEY: ***************************************************

  # We're using Scylladb for non-critical data such as device metadata as it's about 10x chepaer than AWS hosted DynamoDB...
  SCYLLADB_HOST: scylladb-stg.aws.gymsystems.co
  SCYLLADB_PORT: 27028

  # This is required so SLS knows what configuration to load.
  # This must also be set here so it gets exported to aws lambda as an environment variable - do not remove!
  NODE_ENV:                       'staging'

################################################################################################
# DATABASE TABLES (Dynamodb)...
# Please note these are separate to "environment variables" and are actually loaded as an environment variable during the lambda/oh startup/bootstrap scripts.

databases:

  # NB - Don't FORGET TO DEFINE DB SCHEMA + SEEDS IF CREATING NEW TABLES...
  # TABLES ARE AUTOMATICALLY CREATED/UPDATED IN AWS VIA JENKINS

  # Website Tables
  DB_CLUBS_TABLE:                 'website-club-data-stg'
  DB_CUSTOM_TRACKING_TABLE:       'website-club-tracking-codes-stg'
  DB_FAQS_TABLE:                  'website-faqs-stg'
  DB_PRESS_TABLE:                 'website-press-stg'
  DB_EMAIL_MAPPING_TABLE:         'website-email-mapping-stg'
  DB_CLUB_BUSY_HOURS_TABLE:       'website-club-busy-hours-stg'
  DB_LANDING_PAGES_TABLE:         'website-landing-pages-stg'

  # Reviews Tables are Created/Administered via Universal API
  DB_CLUB_REVIEWS_TABLE:          'club-social-reviews-stg'
  DB_CLUB_REVIEWS_SETTINGS_TABLE: 'club-social-reviews-settings-stg'

  ### Core Performance Hub Features ###
  # Marketing & Print
  DB_DESIGN_TEMPLATES_TABLE:      'ph-design-templates-stg'
  DB_DESIGN_VARIABLES_TABLE:      'ph-design-variables-stg'
  DB_DESIGN_ASSETS_TABLE:         'ph-design-assets-stg'
  DB_DESIGN_STATIC_TABLE:         'ph-design-static-templates-stg'
  DB_DESIGN_RENDERS_TABLE:        'ph-design-renders-stg'
  DB_DESIGN_INPUT_MAPPING_TABLE:  'ph-design-input-mapping-stg'
  DB_DESIGN_CUSTOM_PAGES_TABLE:    'ph-design-custom-pages-stg'

  # IoT Devices
  DB_ROUND_DISPLAY_BASE_CONFIG:   'ph-round-display-overwrites-stg'
  DB_DISPLAY_UI_OPTIONS_TABLE:    'ph-display-ui-options-stg'
  DB_DISPLAY_DATA_SOURCES_TABLE:  'ph-display-data-sources-stg'
  DB_DEVICE_AUTOMATIONS_TABLE:    'ph-device-automations-stg'
  DB_DEVICE_REGISTRATIONS_TABLE:  'uapi-coachingscreen-registrations-stg'
  DB_DEVICES_METADATA_TABLE:      'uapi-device-metadata-stg'
  DB_AUDIO_CONTROL_CONFIG_TABLE:  'uapi-audio-control-config-stg'
  DB_CCTV_BUCKET_REGION_TABLE:    'uapi-cctv-bucket-region-availability-stg'
  DB_CCTV_REG_TABLE:              'uapi-cctv-registrations-stg'
  DB_CCTV_REC_UPLOAD_TABLE:       'uapi-cctv-record-upload-stg'
  DB_CCTV_UI_ACTIONS_TABLE:       'uapi-cctv-ui-actions-stg'
  DB_CCTV_CONFIG_TABLE:           'uapi-cctv-config-stg'
  DB_CCTV_SESSION_TOKEN_TABLE:    'uapi-cctv-session-token-stg'
  DB_CCTV_TIMELAPSE_TABLE:        'uapi-cctv-timelapse-stg'
  DB_CCTV_PEOPLE_TABLE:           'cctv-rekognition-api-people-stg'
  DB_CCTV_FACE_DETECTIONS_TABLE:  'cctv-rekognition-api-face-detections-stg'
  DB_CCTV_UNMATCHED_DETS_TABLE:   'cctv-rekognition-api-unmatched-detections-stg'
  DB_CCTV_SHADOW_PHOTOS_TABLE:    'cctv-rekognition-api-shadow-photos-stg'
  DB_CCTV_BBOX_TIMELINE_TABLE:    'cctv-rekognition-api-bbox-timeline-stg'
  DB_CCTV_PERSON_PROCESS_TABLE:   'cctv-rekognition-api-person-process-stg'
  DB_CCTV_FACILITY_REPORT_TABLE:  'ph-cctv-facility-report-stg'

  # PH General
  DB_PERMISSIONS_TABLE:           'ph-access-permissions-stg'
  DB_USER_PREFERENCES_TABLE:      'ph-user-preferences-stg'
  DB_CLUB_PREFERENCES_TABLE:      'ph-club-preferences-stg'
  DB_COUNTRY_HOLIDAYS_TABLE:      'ph-country-holidays-stg'
  DB_ANNOUNCEMENTS_TABLE:         'ph-announcements-stg'
  DB_COUNTERS_TABLE:              'ph-counters-stg'
  DB_ORGANISATIONS_TABLE:         'ph-organisations-stg'
  DB_BRANDS_TABLE:                'ph-brands-stg'
  DB_ROLES_TABLE:                 'ph-roles-stg'

  # Messaging/SMS
  DB_SMS_TABLE:                   'ph-sms-history-stg'
  DB_SMS_SCHEDULE_TABLE:          'ph-sms-schedule-stg'

  # Stripe / Billing
  DB_CHARGES_CONFIG_TABLE:        'ph-charges-config-stg'

  # TC v1 (needs to be removed)
  # DB_TRAINING_CAMP_CHALLENGES_TABLE:  'trainingcamp-challenges-dev'
  # DB_TRAINING_CAMP_USERS_TABLE: 'trainingcamp-users-dev'

  # Single Table of the Member Data for all our users - Synced from GymMaster
  DB_GM_MEMBER_DATA_TABLE:        'gm-member-data-stg'
  DB_GM_CLUB_REPORTING:           'gm-club-reporting-stg'
  DB_GM_VISIT_HISTORY:            'universal-api-member-swipes-stg'


  DB_WORKOUT_BOOKING_LEADS_TABLE: 'workout-booking-leads-stg'

  DB_AGREEMENTS_TABLE:              'ph-agreements-stg'
  DB_AGREEMENTS_SIGNED_TABLE:       'ph-agreements-signed-stg'

  # DB_ACADEMY_USERS_TABLE: 'the-academy-users-dev'
  DB_CLUB_APPS_CONFIG_TABLE: 'universal-api-club-apps-config-stg'
  # DB_WEBSITE_LEADS_TABLE: 'email-leads-dev'

  DB_NOTIFICATION_MESSAGES_TABLE:   'ph-notification-messages-stg'
  DB_CONSUMED_NOTIFICATIONS_TABLE:  'ph-consumed-notifications-stg'
  DB_CLUB_KYC_TABLE:                'ph-club-kyc-stg'
  DB_SECURE_UPLOADS_TABLE:          'ph-secure-uploads-stg'
  DB_IOT_REG_TABLE:                 'uapi-iot-registrations-stg'
  DB_HISTORICAL_CLUB_HOLIDAYS_TABLE: 'ph-historical-club-holidays-stg'
  DB_SAAS_BILLING_TABLE:            'ph-saas-billing-stg'
  DB_CLUB_USER_AGREEMENT:         'club-user-agreement-stg'
  DB_CLUB_USER_AGREEMENT_CONFIG:  'cua-config-stg'
  DB_SAAS_BILLING_CONFIG_TABLE: 'ph-saas-billing-config-stg'
  DB_SAAS_BILLING_ADDITIONAL_LINE_ITEMS_TABLE: 'ph-saas-billing-additional-line-items-stg'
  DB_SAAS_BILLING_PAYMENT_SETTINGS_TABLE: 'ph-saas-billing-payment-settings-stg'
  DB_SAAS_BILLING_MONTHLY_RECORDS_TABLE: 'ph-saas-billing-monthly-records-stg'

  DB_RELEASE_HISTORY:             'ph-release-history-stg'
  DB_PROGRAM_BUILDER_CALENDAR:    'builder-calendar-stg'
  CALENDAR_TYPE_TABLE:            'builder-calendar-type-stg'

  # TOD/TC
  DB_TRAIN_FREE_USERS_TABLE:      'train-v1-free-members'
  DB_TRAINING_CAMP_LEADS_TABLE:   'training-camp-leads'

  # GM Memberships
  DB_GM_MEMBERSHIP_DATA_TABLE:        'gm-membership-data-stg'
  DB_GM_MEMBERSHIP_ACCESS_TYPES_TABLE: 'gm-membership-access-types-stg'

  # Access Control
  DB_TEMP_ACCESS_TOKEN_TABLE:     'uapi-temp-access-token-stg'


  # Training Camp
  DB_TRAINING_CAMP_SEASON_DATA: 'tc-api-v2-season-data-stg'
  USER_PREFERENCES_TABLE: 'tc-api-v2-user-preferences-stg'
  LEADERBOARD_TABLE: 'leaderboards-stg'

  # MMS MySQL tables
  DB_MMS_USERS_TABLE: 'mms-api-user-stg'

################################################################################################
# These variables are loaded into the core performance hub, but not any secondary lambda functions...

performanceHub:
  # For signing and verifying JWT tokens for temporary or guest access
  PH_TEMP_ACCESS_SECRET_KEY:      '68Ev7MNHTSsXvj+0tJqDr0T+e5rdH5afS6U3X8dmdos='

  # BALENA API
  BALENA_API_KEY:                 'FH40o9zB3Ul0AjQrkpxDCfR1uGEudtuo'
  # Applications to monitor for releases.. (comma separated list is ok)
  BALENA_APPLICATIONS_TO_MONITOR: 'ubx/coachingscreens-stg,ubx/cctv-stg'

  # AWS
  # AWS_PROFILE: 12round
  AWS_REGION: ap-southeast-2

  # Performance Hub FQDN URL
  PH_PROD_URL: 'https://portal.hub.stage.gymsystems.co'

  # Lambda Function names we call/invoke directly from within the performance hub odebase...
  LAM_SYNC_GM_MEMBERS:                  'ph-stg-syncGMMembers'
  LAM_SYNC_GM_REVENUE:                  'ph-stg-syncGMRevenueHistory'
  LAM_UPDATE_KNOWN_COUNTRY_HOLIDAYS:    'ph-stg-updateCountryHolidays'
  LAM_UPDATE_CLUB_HOLIDAYS:             'ph-stg-updateClubHolidays'
  LAM_UPDATE_SOCIAL_REVIEWS_FROM_YEXT:  'ph-stg-updateSocialReviews'
  LAM_AUTO_PUBLISH_REVIEWS:             'ph-stg-autoPublishedReviews'
  LAM_SMS_SCHEDULER:                    'ph-stg-smsSchedule'
  LAM_PAYMENT_REPORT:                   'ph-stg-dailyPaymentReport'
  LAM_LOW_BATTERY_DURESS:               'ph-stg-lowBatteryDuressNotifs'
  LAM_SAAS_BILLING_LOG:                 'ph-stg-startDailySaasBillingLogQueue'
  LAM_DELETE_OLD_NOTIFICATIONS:         'ph-stg-deleteOldNotifications'
  LAM_PEAK_TIME_INSIGHTS:               'ph-stg-clubsBusyHours'
  LAM_SAAS_MONTHLY_BILLER:              'ph-stg-startMonthlySaasBillerQueue'
  LAM_CCTV_HEATMAPS:                    'ph-stg-cctvHeatmapGeneration'
  LAM_CACHE_MMS_USERS:                  'ph-stg-cacheMMSUsers'

  # WIKI
  WIKI_ENDPOINT: 'http://openkb:4444'

  # WEKAN - KANBAN
  KANBAN_ENDPOINT: 'http://wekan-app:8080'

  # EVENTS MANAGER
  EVENTS_MANAGER_ENDPOINT: 'http://events-manager:4089'

  # Static API Key used for internal routes that do not use Google SSO headers...
  STATIC_API_KEY_AUTH: 32aa84c47da94a65b71a0269f6960859

  # Ticketing
  OSTICKET_API_URL: https://ticketing.gymsystems.co/api/tickets
  OSTICKET_API_KEY: 26F4D4850450DC99091A2EB10A9F0436

  # OSTICKET API - use your local IP Address instead of localhost
  # OSTICKET_API_URL: 'http://*************:8081/api/tickets'
  # OSTICKET_API_KEY: 'E7DE56092B1B609FBE059456D37918CA'

  ########################

  # S3 Shared Config
  S3_ASSETS_BUCKET: 'content.gymsystems.co'
  S3_ASSETS_KEY_PREFIX: 'hub/announcements-uploads'
  S3_AGREEMENTS_BUCKET: 'content.gymsystems.co'
  S3_AGREEMENTS_KEY_PREFIX: 'hub/agreements'
  S3_UPLOAD_ASSETS_BUCKET: 'upload-api-assets-prd'

  # LOCAL SERVERLESS MARKETING & PRINT RENDERER
  SLS_PROXY_SERVICE_PORT: 3030
  SLS_PROXY_TO_URL: 'http://127.0.0.1:3333'

  #APPS
  #Training Camp Default DB Region
  TRAINING_CAMP_REGION: 'us-east-1'

  # MMS API
  MMS_API_URL: 'https://r2zop13g00.execute-api.ap-southeast-2.amazonaws.com/stg'
  MMS_API_KEY: 'UjNa0uV97f8cWRoN1v1uo7qiLbXLjNIIaNLwSwkd'

  # CLUBPASS API
  CLUBPASS_API: 'https://sky8nr6hh4.execute-api.ap-southeast-2.amazonaws.com/stg'
  CLUBPASS_API_KEY: '86d0DXCVddayHJcriYtVI1JrIIzSo0g9qvmxEP7h'

  # Store
  EXPRESSCART_ENDPOINT: 'http://ec-ph-store:1111/'
  EXPRESSCART_API_KEY: '7f444c37-6404-e013-38d0-1b529c267f9a'

  # Program Builder
  PROGRAM_BUILDER_ENDPOINT: 'http://program-builder:3089'

  # Google Auth
  GOOGLE_APPLICATION_CREDENTIALS: 'credentials/service-account.json'

  # ASSET UPLOAD API
  ASSET_UPLOADS_API: 'https://uapi.gymsystems.co/upload/v1'
  ASSET_UPLOADS_API_KEY: 'SnQ5FDdcZh7bY8boaS1uc5hw3NwlkHk052jG17rV'

  # Universal API - Environment Dependent (DEV, STG, PRD)
  UAPI_URL: https://uapi.stage.gymsystems.co 
  UAPI_KEY: FgF9zjHdbo91Yhz3JL5dI7n7bOohbrZO2Z8HPypB

  # Email Config
  SES_CONFIG_REGION:              'us-east-1'
  LINKING_DELIVER_TO_ADDRESS:     '<EMAIL>'
  PH_SYSTEM_EMAILS_FROM_ADDRESS:  '<EMAIL>'

  # Google maps API
  GOOGLE_MAPS_API_KEY: AIzaSyBIJgUK8WUL4Bd_KUq9zBRe_slZ3o3jJsM
  GOOGLE_MAPS_STREETVIEW_KEY: AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys

  # Graylog
  GRAYLOG_SERVER: 'graylog.aws.gymsystems.co'
  GRAYLOG_PROTOCOL: 'http'
  GRAYLOG_PORT: 12201

  # Universal API
  UNIVERSAL_API_URL: https://uapi.gymsystems.co

  # Redash
  REDASH_PROXY_PUBLIC_DOMAIN: https://redash.hub.gymsystems.co/

  # https://api.ipdata.co
  IPDATA_API_KEY: e7a213cf0b4f83ff0e1b983b79e31272107c4c44c516017f3b8cfac3

  # EXTENDED ACCESS
  EXTENDED_ACCESS_ENDPOINT: 'http://extended-access-fe:3092'

  # CCTV FRONTEND
  CCTV_FE_ENDPOINT: 'http://cctv-fe:3092'

  # AUDIO CONTROL FRONTEND
  AUDIO_CONTROL_FE_ENDPOINT: 'http://audio-control-fe:3042'

  # COACHING SCREENS FRONTEND
  COACHINGSCREENS_FE_ENDPOINT: 'http://coaching-screens-fe:3052'

  # HEALTH CHECK FRONTEND
  HEALTHCHECK_FE_ENDPOINT: 'http://health-check-fe:3062'

  # S3 Encryption
  ENCRYPTION_PASSWORD: 'c5d27df4-b845-467c-954b-9e389fd044f1'

  # Algolia
  ALGOLIA_APP_ID: 'R6LBPN4AJJ'
  ALGOLIA_API_KEY: '********************************'
  ALGOLIA_WIKI_INDEX: 'wiki-stg-kb'
  ALGOLIA_MARKETINGPRINT_INDEX: 'marketingprint-stg'

  # Rekognition
  IDENTIFIED_COLLECTION: 'cctv-identified-stg'
  UNIDENTIFIED_COLLECTION: 'cctv-unidentified-stg'
