const uuidV4 = require('uuid').v4;
const dbService = require('./dynamodb');
const DB_BRANDS_TABLE = process.env.DB_BRANDS_TABLE;

module.exports.getBrand = async (brandId) => {
    try {
        if (!brandId) return [400, { message: 'Brand ID is required' }];

        const readParams = {
            TableName: DB_BRANDS_TABLE,
            Key: { brandId },
        };

        const { Item } = await dbService.retrieveItem(readParams);
        if (!Item) return [404, { message: 'Brand not found' }];

        return [200, Item];
    } catch (error) {
        console.error('Failed to get brand', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.listBrandsByOrganisation = async (organisationId) => {
    try {
        const params = {
            TableName: DB_BRANDS_TABLE,
            IndexName: 'organisationId-index',
            KeyConditionExpression: '#organisationId = :organisationId',
            ExpressionAttributeValues: {
                ':organisationId': organisationId,
            },
            ExpressionAttributeNames: {
                '#organisationId': 'organisationId',
            },
        };

        const { Items } = await dbService.queryItems(params);
        return [200, Items];
    } catch (error) {
        console.error('Failed to query brands by organisation', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.listBrands = async (filters = {}) => {
    try {
        const params = { TableName: DB_BRANDS_TABLE, ...dbService.queryFilterExpression(filters) };
        const { Items } = await dbService.scanItems(params);
        return [200, Items];
    } catch (error) {
        console.error('Failed to list brands', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.createBrand = async (attr = {}) => {
    try {
        if (!attr.organisationId) return [400, { message: 'Organisation ID is required' }];
        if (!attr.name) return [400, { message: 'Brand name is required' }];

        const doc = {
            brandId: attr.brandId || uuidV4(),
            organisationId: attr.organisationId,
            name: attr.name,
            redirectDashboard: attr.redirectDashboard || null,
            displayUIOptions: attr.displayUIOptions,
            displayDataSources: attr.displayDataSources,
        };

        const params = { TableName: DB_BRANDS_TABLE, Item: doc };
        await dbService.addItem(params);
        return [201, doc];
    } catch (error) {
        console.error('Failed to create brand', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.updateBrand = async (brandId, attrs = {}) => {
    try {
        if (!brandId) return [400, { message: 'Brand ID is required' }];

        const doc = {};

        if (attrs.name) doc.name = attrs.name;
        if (attrs.organisationId) doc.organisationId = attrs.organisationId;
        if (attrs.redirectDashboard !== undefined) {
            doc.redirectDashboard = attrs.redirectDashboard;
        }
        if (attrs.displayUIOptions !== undefined) {
            doc.displayUIOptions = attrs.displayUIOptions;
        }
        if (attrs.displayDataSources !== undefined) {
            doc.displayDataSources = attrs.displayDataSources;
        }

        if (Object.keys(doc).length === 0) {
            return [400, { message: 'No attributes to update' }];
        }

        const params = {
            TableName: DB_BRANDS_TABLE,
            Key: { brandId },
            ...dbService.updateExpression(doc),
        };

        await dbService.updateItem(params);
        return [200, doc];
    } catch (error) {
        console.error('Failed to update brand', error);
        return [500, { message: 'Something went wrong' }];
    }
};
