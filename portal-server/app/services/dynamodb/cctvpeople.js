const dbService = require('./dynamodb');
const { DB_CCTV_PEOPLE_TABLE } = process.env;

const get = async (personID) => {
  const { Item } = await dbService.retrieveItem({
    TableName: DB_CCTV_PEOPLE_TABLE,
    Key: { personID },
  });

  return Item;
};

const batchGet = async (personIDArray) => {
  function chunkArray(array, size) {
    const result = [];
    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size));
    }
    return result;
  }

  const chunks = chunkArray(personIDArray, 50);

  const batchParamsArray = chunks.map(chunk => ({
    RequestItems: {
      [DB_CCTV_PEOPLE_TABLE]: {
        Keys: chunk.map(personID => ({ personID }))
      }
    }
  }));

  // Execute all batch requests in parallel.
  const batchResults = await Promise.all(
    batchParamsArray.map(params => dbService.batchRetrieve(params))
  );

  const consolidatedResults = batchResults.reduce((acc, result) => {
    if (result.Responses) {
      return acc.concat(result.Responses[DB_CCTV_PEOPLE_TABLE]);
    }

    return acc;
  }, []);

  return consolidatedResults;
};

const queryByClub = async ({
  clubID,
  filters,
  lowLevelFilters,
  exStartKey,
  limit,
  recursive = true,
  sortNewestToOldest = true,
}) => {
  const fields = [
    'clubID', 'personID', 'memberID', 'firstName', 'lastName',
    'membershipStatus', 'membership', 'updated', 'collectionId', 'avatar', 'isIdentified',
    'shadowPhoto', 'age', 'gender', 'type',
  ];
  const ExpressionAttributeNames = fields.reduce((acc, cur) => {
    acc['#' + cur] = cur;
    return acc;
  }, {});

  const params = {
    TableName: DB_CCTV_PEOPLE_TABLE,
    IndexName: 'clubID-memberID',
    Limit: limit,
    ProjectionExpression: `${Object.keys(ExpressionAttributeNames).join(', ')}`,
    ScanIndexForward: !sortNewestToOldest,
    KeyConditionExpression: 'clubID = :clubID',
    ...dbService.queryFilterExpression(filters, '='),
    ...(exStartKey ? { ExclusiveStartKey: exStartKey } : {}),
    ExpressionAttributeNames,
  };

  if (lowLevelFilters) {
    params.FilterExpression += lowLevelFilters;
  }

  params.ExpressionAttributeValues = {
    ...(params.ExpressionAttributeValues || {}),
    ':clubID': clubID,
  };

  const { Items = [], LastEvaluatedKey } = await dbService.queryItems(params);
  if (!Items.length && !LastEvaluatedKey) return [[]];

  // Recursively fetch for more until limit is sufficed
  const lacking = limit - Items.length;
  if (limit && Items.length < limit && LastEvaluatedKey && recursive && lacking > 5) {
    const [additionalItems, newLEK] = await queryByClub({
      clubID,
      filters,
      lowLevelFilters,
      exStartKey: LastEvaluatedKey,
      limit: lacking,
      recursive,
      sortNewestToOldest,
    });

    return [[...Items, ...additionalItems], newLEK];
  }

  return [Items, LastEvaluatedKey];
}

const update = async (payload) => {
  const { personID, ...updatePayload } = payload;
  const params = {
    TableName: DB_CCTV_PEOPLE_TABLE,
    Key: { personID },
    ...dbService.updateExpression(updatePayload)
  };

  await dbService.updateItem(params);
};

const remove = async (personID) => {
  await dbService.removeItem({
    TableName: DB_CCTV_PEOPLE_TABLE,
    Key: { personID },
  });
};

module.exports = {
  get,
  batchGet,
  queryByClub,
  update,
  remove,
};
