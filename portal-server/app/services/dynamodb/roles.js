const uuidV4 = require('uuid').v4;
const dbService = require('./dynamodb');
const DB_ROLES_TABLE = process.env.DB_ROLES_TABLE;

module.exports.getRole = async (roleId) => {
    try {
        if (!roleId) return [400, { message: 'Role ID is required' }];

        const readParams = {
            TableName: DB_ROLES_TABLE,
            Key: { roleId },
        };

        const { Item } = await dbService.retrieveItem(readParams);
        if (!Item) return [404, { message: 'Role not found' }];

        return [200, Item];
    } catch (error) {
        console.error('Failed to get role', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.listRolesByOrganisation = async (organisationId) => {
    try {
        const params = {
            TableName: DB_ROLES_TABLE,
            IndexName: 'organisationId-index',
            KeyConditionExpression: '#organisationId = :organisationId',
            ExpressionAttributeValues: { ':organisationId': organisationId },
            ExpressionAttributeNames: { '#organisationId': 'organisationId' },
        };

        const { Items } = await dbService.queryItems(params);
        return [200, Items];
    } catch (error) {
        console.error('Failed to query roles by organisation', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.listRoles = async (filters) => {
    try {
        const params = { TableName: DB_ROLES_TABLE, ...dbService.queryFilterExpression(filters) };
        const { Items } = await dbService.scanItems(params);
        return [200, Items];
    } catch (error) {
        console.error('Failed to list roles', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.createRole = async (attr = {}) => {
    try {
        const doc = { roleId: uuidV4(), ...attr };
        const params = { TableName: DB_ROLES_TABLE, Item: doc };
        await dbService.addItem(params);
        return [201, doc];
    } catch (error) {
        console.error('Failed to create role', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.updateRole = async (roleId, attrs = {}) => {
    try {
        if (!roleId) return [400, { message: 'Role ID is required' }];

        if (Object.keys(attrs).length === 0) {
            return [400, { message: 'No attributes to update' }];
        }

        const params = {
            TableName: DB_ROLES_TABLE,
            Key: { roleId },
            ...dbService.updateExpression(attrs),
        };

        await dbService.updateItem(params);
        return [200, attrs];
    } catch (error) {
        console.error('Failed to update role', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.deleteRole = async (roleId) => {
    try {
        if (!roleId) return [400, { message: 'Role ID is required' }];

        const params = {
            TableName: DB_ROLES_TABLE,
            Key: { roleId },
        };

        await dbService.removeItem(params);
        return [204];
    } catch (error) {
        console.error('Failed to delete role', error);
        return [500, { message: 'Something went wrong' }];
    }
};
