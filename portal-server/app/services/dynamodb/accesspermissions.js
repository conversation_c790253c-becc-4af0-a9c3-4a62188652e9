const _get = require('lodash/get');
const _omit = require('lodash/omit');
const dbService = require('./dynamodb');
const DB_PERMISSIONS_TABLE = process.env.DB_PERMISSIONS_TABLE || 'ph-access-permissions-prd';

module.exports.getByEmail = async (email) => {
    try {
        const dbResults = await dbService.retrieveItem({
            TableName: DB_PERMISSIONS_TABLE,
            Key: { id: email },
        });
        if (!dbResults.Item) return [404, { message: 'Not Found' }];
        return [200, formatUser(dbResults.Item)];
    } catch (error) {
        console.error('Failed to get access permission from table', error);
        return [500, { message: 'Something went wrong' }];
    }
};
module.exports.upsert = async (params) => {
    try {
        const email = params.email || params.id;
        const getRes = await this.getByEmail(email);
        if (getRes[0] === 500) return [500, { message: 'Something went wrong' }];

        const user = getRes[0] === 200 ? getRes[1] : { roles: [], new: true };
        const updatedUser = _omit({ ...user, ...params }, ['email', 'id']);

        if (getRes[0] === 200) {
            const result = await dbService.updateItem({
                TableName: DB_PERMISSIONS_TABLE,
                Key: { id: email },
                ...dbService.updateExpression(updatedUser),
            });
            return [200, result];
        } else {
            const result = await dbService.addItem({
                TableName: DB_PERMISSIONS_TABLE,
                Item: { id: email, ...updatedUser },
            });
            return [200, result];
        }
    } catch (error) {
        console.error('Failed to add user to table', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports.list = async (filters = {}, config = {}) => {
    try {
        const dbResults = await dbService.retrieveItems({
            TableName: DB_PERMISSIONS_TABLE,
            ...dbService.queryFilterExpression(filters),
        });

        if (config.format === false) return [200, dbResults.Items];

        const formatted = dbResults.Items.map((item) => formatUser(item));
        return [200, formatted];
    } catch (error) {
        console.error('Failed to get access permission from table');
        return [500, { message: 'Something went wrong' }];
    }
};

const formatUser = (item) => ({
    email: _get(item, 'id', ''),
    isGlobalAdmin: item?.isGlobalAdmin || false,
    roles: item?.roles || [],
});

module.exports.remove = async (email) => {
    try {
        const result = await dbService.removeItem({
            TableName: DB_PERMISSIONS_TABLE,
            Key: { id: email },
        });
        return [200, result];
    } catch (error) {
        console.error('Failed to remove user from table', error);
        return [500, { message: 'Something went wrong' }];
    }
};
