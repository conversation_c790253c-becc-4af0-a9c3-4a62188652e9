const { v4: uuidv4 } = require('uuid');
const dbService = require('./dynamodb');
const DB_DESIGN_CUSTOM_PAGES_TABLE = process.env.DB_DESIGN_CUSTOM_PAGES_TABLE || 'ph-design-custom-pages-dev';

module.exports.listOrganisationCustomPages = async (organisationId) => {
    try {
        const params = {
            TableName: DB_DESIGN_CUSTOM_PAGES_TABLE,
            IndexName: 'organisationId-index', // Assuming you have a GSI on organisationId
            KeyConditionExpression: 'organisationId = :orgId AND deleted = :deleted',
            ExpressionAttributeValues: { ':orgId': organisationId, ':deleted': 0 },
        };

        const result = await dbService.queryItems(params);
        const sortedItems = result.Items.sort((a, b) => a.order - b.order);

        return [200, sortedItems];
    } catch (error) {
        console.error('Error listing organisation custom pages:', error);
        return [500, { message: 'Failed to list custom pages' }];
    }
};

module.exports.upsertCustomPage = async (attrs) => {
    try {
        if (!attrs.id) attrs.id = uuidv4();
        if (!('deleted' in attrs)) attrs.deleted = 0;

        const params = {
            TableName: DB_DESIGN_CUSTOM_PAGES_TABLE,
            Item: attrs,
        };

        await dbService.addItem(params);

        return [200, { message: 'Custom page upserted successfully' }];
    } catch (error) {
        console.error('Error upserting custom page:', error);
        return [500, { message: 'Failed to upsert custom page' }];
    }
};
