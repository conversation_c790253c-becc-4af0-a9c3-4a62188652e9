const uuid = require('uuid').v4;
const dbService = require('./dynamodb');
const MAIN_TABLE = process.env.DB_DISPLAY_UI_OPTIONS_TABLE;

// TODO: Pagination?
const list = async (filters = {}) => {
    try {
        const params = {
            TableName: MAIN_TABLE,
            ...dbService.queryFilterExpression(filters),
        };

        const { Items } = await dbService.scanItems(params);
        return [200, Items];
    } catch (error) {
        console.error('Failed to list brands', error);
        return [500, { message: 'Something went wrong' }];
    }
};

const add = async (attr = {}) => {
    try {
        if (!attr.name) return [400, { message: 'name is required' }];
        if (!attr.description) return [400, { message: 'description is required' }];
        if (!attr.url) return [400, { message: 'url is required' }];

        const doc = {
            id: uuid(),
            ...attr,
        };

        const params = { TableName: MAIN_TABLE, Item: doc };
        await dbService.addItem(params);
        return [201, doc];
    } catch (error) {
        console.error('Failed to add', error);
        return [500, { message: 'Something went wrong' }];
    }
};

const update = async (attrs = {}) => {
    try {
        const { id, ...doc } = attrs;
        if (!id) return [400, { message: 'id is required' }];

        if (Object.keys(doc).length === 0) {
            return [400, { message: 'No attributes to update' }];
        }

        const params = {
            TableName: MAIN_TABLE,
            Key: { id },
            ...dbService.updateExpression(doc),
        };

        await dbService.updateItem(params);
        return [200, doc];
    } catch (error) {
        console.error('Failed to update brand', error);
        return [500, { message: 'Something went wrong' }];
    }
};

module.exports = {
    list,
    add,
    update,
};
