const _assign = require('lodash/assign');
const _get = require('lodash/get');
const _omit = require('lodash/omit');

const dbService = require('./dynamodb');
const DB_CLUBS_TABLE = process.env.DB_CLUBS_TABLE || 'website-club-data';

const customScan = async (params) => {
    const paramsWithTable = { ...params, TableName: DB_CLUBS_TABLE };

    try {
        const dbResults = await dbService.retrieveItems(paramsWithTable);
        return [200, dbResults];
    } catch (error) {
        console.error('Failed to scan clubs table: ', error);
        return [500, { message: 'Failed to scan clubs' }];
    }
};

const describeTable = async () => {
    try {
        const data = await dbService.describeTable({
            TableName: DB_CLUBS_TABLE,
        });
        return [200, data.Table];
    } catch (error) {
        console.error(error);
        return [500, { message: 'Something went wrong' }];
    }
};

const scan = async (filters = {}, options = {}, rawFilterExpression = '') => {
    const params = _assign(
        {},
        {
            TableName: DB_CLUBS_TABLE,
        },
        dbService.queryFilterExpression(filters, _get(options, 'operator', '='))
    );

    params.FilterExpression = `${params.FilterExpression} ${rawFilterExpression}`;

    try {
        let dbResults = await dbService.retrieveItems(params);
        return [true, dbResults.Items];
    } catch (error) {
        console.error('Failed to scan clubs table', error);
        return [false, { message: 'Failed to scan clubs' }];
    }
};

const get = async (id) => {
    // console.log('dbService.dynamoDb', dbService.dynamoDb)
    let config;
    try {
        config = {
            TableName: DB_CLUBS_TABLE,
            Key: { id },
        };
        const dbResults = await dbService.retrieveItem(config);
        if (!dbResults.Item) return [false, { message: 'Not Found' }];
        return [true, dbResults.Item];
    } catch (error) {
        console.error('Failed to get club from table', error, 'config', config);
        return [false, { message: 'Something went wrong' }];
    }
};

const update = async (payload, expressions = {}) => {
    try {
        await dbService.updateItem({
            TableName: DB_CLUBS_TABLE,
            Key: {
                id: payload.id,
            },
            ...expressions,
            ...dbService.updateExpression(_omit(payload, ['id'])),
        });

        return [true, payload];
    } catch (error) {
        console.error(`Failed to update club ${payload.id}: `, error);
        return [false];
    }
};

const add = async (payload, options = {}) => {
    try {
        await dbService.addItem({ TableName: DB_CLUBS_TABLE, Item: payload }, options);

        return [true, payload];
    } catch (error) {
        console.error(`Failed to add club ${payload.id}`, error);
        return [false, { message: 'Failed to add club' }];
    }
};

const getByBrandAndSlug = async (brandId, slug) => {
    try {
        const { Items: items } = await dbService.queryItems({
            TableName: DB_CLUBS_TABLE,
            IndexName: 'brand-slug-index',
            KeyConditionExpression: 'brandId = :brandId AND slug = :slug',
            ExpressionAttributeValues: {
                ':brandId': brandId,
                ':slug': slug,
            },
        });

        if (items.length === 0) {
            return [404, { message: 'Item not found' }];
        }

        return [200, items[0]];
    } catch (error) {
        console.error('Error retrieving item by slug:', error);
        return [500, { message: 'Error retrieving item by slug' }];
    }
};

const upsertAdminNote = async (facilityId, note) => {
    try {
        const params = {
            TableName: DB_CLUBS_TABLE,
            Key: { id: facilityId },
            UpdateExpression: 'SET adminNotes = list_append(if_not_exists(adminNotes, :emptyList), :note)',
            ExpressionAttributeValues: {
                ':note': [note],
                ':emptyList': [],
            },
            ReturnValues: 'ALL_NEW',
        };

        await dbService.updateItem(params);
        return [200, note];
    } catch (error) {
        console.error('Failed to upsert admin note:', error);
        return [500, { message: 'Failed to upsert admin note' }];
    }
};

module.exports = {
    scan,
    update,
    get,
    add,
    customScan,
    describeTable,
    getByBrandAndSlug,
    upsertAdminNote,
};
