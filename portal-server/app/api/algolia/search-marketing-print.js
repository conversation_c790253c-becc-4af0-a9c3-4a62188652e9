const Router = require('express').Router;
const _get = require('lodash/get');
const algoliasearch = require('algoliasearch');
const auth = require('../../shared-functions/auth');
const countries = require('i18n-iso-countries');
const cheerio = require('cheerio');
const { returnTemplates: dynamicTemplates } = require('../marketing/designs/dynamic/list');
const { getStaticDesigns } = require('../marketing/designs/static/list');
const OpenAI = require('openai');
const { DynamoDB } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocument } = require('@aws-sdk/lib-dynamodb');
const moment = require('moment');
const { getAlgoliaConfig } = require('./get-algolia-config');
const { listOrganisations } = require('../../services/dynamodb/organisations');
const { initAlgoliaMarketingPrintIndex } = require('./init-marketing-print-index');

countries.registerLocale(require("i18n-iso-countries/langs/en.json"));

const algoliaClient = algoliasearch(process.env.ALGOLIA_APP_ID || '', process.env.ALGOLIA_API_KEY || '');
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || 'FAKE_KEY_TO_PASS_JENKINS_API_SPEC_BUILD';
const DB_DESIGN_STATIC_TABLE = process.env.DB_DESIGN_STATIC_TABLE || 'design-static-templates';

const router = Router({ mergeParams: true });

const dynamodb = DynamoDBDocument.from(new DynamoDB());
const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
});

async function extractTextFromImage(imageUrl) {
  try {
    // Ensure we have a valid URL string
    if (!imageUrl || typeof imageUrl !== 'string') {
      console.warn('Invalid image URL:', imageUrl);
      return '';
    }

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: "What text do you see in this image? Please return only the text content." },
            {
              type: "image_url",
              image_url: {
                "url": imageUrl
              }
            }
          ],
        },
      ],
      max_tokens: 300,
    });

    return response.choices[0].message.content.trim();
  } catch (error) {
    console.error('Error extracting text from image:', error);
    return '';
  }
}

async function handleSearch(req, res) {
    const searchTerms = _get(req.body, 'searchTerms', "") || _get(req.query, 'searchTerms', "");
    const designType = _get(req.body, 'designType', "") || _get(req.query, 'designType', "");
    const additionalAlgoliaArgs = _get(req.body, 'algoliaArgs', {}) || _get(req.query, 'algoliaArgs', {});
    const adminQuery = _get(req.body, 'adminQuery', false) || _get(req.query, 'adminQuery', false);
    // Add pagination parameters
    const page = parseInt(_get(req.body, 'page', 1)) || _get(req.query, 'page', 1);
    const hitsPerPage = parseInt(_get(req.body, 'hitsPerPage', 20)) || _get(req.query, 'hitsPerPage', 20);
    const facilityId = req.body.club;

    if (!facilityId) {
        return res.status(400).send({ message: "facility ID is required" });
    }

    try {
        await performSearchAndRespond(searchTerms, additionalAlgoliaArgs, designType, adminQuery, res, page, hitsPerPage, req, facilityId);
    } catch (err) {
        console.error(err);
        res.status(500).send({ message: err.message });
    }
}

/**
 * @swagger
 * /algolia/search-marketing-print:
 *   post:
 *     summary: Search for marketing print materials
 *     tags: [Algolia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               club:
 *                 type: string
 *                 description: Club ID
 *               searchTerms:
 *                 type: string
 *                 description: Search terms
 *               algoliaArgs:
 *                 type: object
 *                 description: Additional Algolia search arguments
 *     responses:
 *       200:
 *         description: Successful search
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 suggestedDesigns:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: Server error
 */
router.post('/algolia/search-marketing-print', handleSearch);

/**
 * @swagger
 * /algolia/search-marketing-print:
 *   get:
 *     summary: Search for marketing print materials
 *     tags: [Algolia]
 *     parameters:
 *       - in: query
 *         name: club
 *         schema:
 *           type: string
 *         description: Club ID
 *       - in: query
 *         name: searchTerms
 *         schema:
 *           type: string
 *         description: Search terms
 *       - in: query
 *         name: algoliaArgs
 *         schema:
 *           type: object
 *         description: Additional Algolia search arguments
 *     responses:
 *       200:
 *         description: Successful search
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 suggestedDesigns:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: Server error
 */
router.get('/algolia/search-marketing-print', handleSearch);

router.post('/algolia/update-marketing-print', async (req, res) => {
    const { objectID, data } = req.body;
    const { facilityId } = req.query;

    if (!facilityId) {
        return res.status(400).send({ message: "facilityId is required" });
    }

    if (!objectID || !data) {
        return res.status(400).send({ message: "objectID and data are required" });
    }

    try {
        const algoliaConfig = await getAlgoliaConfig(facilityId);
        if (algoliaConfig[0] !== 200) {
            return res.status(algoliaConfig[0]).send(algoliaConfig[1]);
        }

        await initAlgoliaMarketingPrintIndex(algoliaConfig[1].marketingPrintIndex);
        const index = algoliaClient.initIndex(algoliaConfig[1].marketingPrintIndex);
        const { html, ...rest } = data;

        const updateObject = {
          objectID: objectID,
          ...rest,
        }

        if(data.designType === 'dynamic' && html) {
          // get the text value from the html using cheerio
          const $ = cheerio.load(html);
          const htmlContent = $('body').text();
          updateObject.htmlContent = htmlContent;
        } else if(data.designType === 'static') {
          // Process image text for static designs
          const imageUrl = Array.isArray(data.thumbnails_large) ? data.thumbnails_large[0] : data.thumbnails_large || data.thumbnail;
          if (imageUrl && !data.imageContent) {
            const imageContent = await extractTextFromImage(imageUrl);
            updateObject.imageContent = imageContent;
          }
        }

        await index.partialUpdateObject(updateObject);
        res.status(200).send({ message: "Object updated successfully" });
    } catch (err) {
        console.error(err);
        res.status(500).send({ message: err.message });
    }
});

/**
 * @swagger
 * /algolia/bulk-update-marketing-print:
 *   post:
 *     summary: Bulk update marketing print objects for a specific club
 *     tags: [Algolia]
 *     responses:
 *       200:
 *         description: Objects updated successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */
router.post('/algolia/bulk-update-marketing-print', auth.checkGlobalAdminAccess, async (req, res) => {
    const orgsRes = await listOrganisations();
    if (orgsRes[0] !== 200) return res.status(orgsRes[0]).send(orgsRes[1]);

    for (const org of orgsRes[1]) {
        try {
            const { organisationId } = org;

            const algoliaConfig = await getAlgoliaConfig(null, organisationId);
            if (algoliaConfig[0] !== 200) return res.status(algoliaConfig[0]).send(algoliaConfig[1]);

            await initAlgoliaMarketingPrintIndex(algoliaConfig[1].marketingPrintIndex);
            const index = algoliaClient.initIndex(algoliaConfig[1].marketingPrintIndex);
            
            // Fetch both static and dynamic templates for the specific club
            const staticTemplatesPromise = new Promise((resolve) => {
                getStaticDesigns(true, organisationId, null, resolve);
            });
            const dynamicTemplatesPromise = new Promise((resolve) => {
                dynamicTemplates(true, organisationId, null, null, resolve);
            });

            const [staticTemplatesList, dynamicTemplatesList] = await Promise.all([staticTemplatesPromise, dynamicTemplatesPromise]);

            if (!Array.isArray(staticTemplatesList) || !Array.isArray(dynamicTemplatesList)) {
                throw new Error('Templates returned are not arrays');
            }

            // Process templates
            const processTemplates = async (templates, designType) => {
                try {
                    const processedTemplatesPromises = templates.map(async template => {
                        if (!template || typeof template !== 'object') {
                            console.warn(`Invalid ${designType} template object: `, template);
                            return null;
                        }

                        let htmlContent = '';
                        let imageContent = template.imageContent || ''; // Use existing imageContent if available

                        // Extract text from HTML if present
                        if (template.html) {
                            try {
                            const $ = cheerio.load(template.html);
                            htmlContent = $('body').text() || '';
                            htmlContent = htmlContent.replace(/\{\{.*?\}\}/g, '');
                            delete template.html;
                            } catch (error) {
                            console.warn(`Error parsing HTML for ${designType} template ${template.id}:`, error);
                            }
                        }

                        // Only process images for static templates and only if imageContent doesn't exist
                        if (designType === 'static' && !imageContent) {
                            try {
                                // Try large thumbnail first, fall back to regular thumbnail
                                let imageUrl = Array.isArray(template.thumbnails_large) ? template.thumbnails_large[0] : template.thumbnails_large || template.thumbnail;
                                
                                if (imageUrl) {
                                    imageContent = await extractTextFromImage(imageUrl);
                                    
                                    // Update DynamoDB record with the new imageContent
                                    if (imageContent) {
                                    await dynamodb.update({
                                        TableName: DB_DESIGN_STATIC_TABLE,
                                        Key: { id: template.id },
                                        UpdateExpression: 'SET imageContent = :imageContent',
                                        ExpressionAttributeValues: {
                                        ':imageContent': imageContent
                                        }
                                    });
                                    }
                                }
                            } catch (error) {
                                console.warn(`Error extracting text from image for template ${template.id}:`, error);
                            }
                        }

                        return {
                            objectID: template.id,
                            ...template,
                            htmlContent,
                            imageContent,
                            designType
                        };
                    });

                    const processedResults = await Promise.all(processedTemplatesPromises);
                    return processedResults.filter(Boolean);
                } catch (error) {
                    console.error('Error processing templates:', error);
                    return [];
                }
            };

            const processedStaticObjects = await processTemplates(staticTemplatesList, 'static');
            const processedDynamicObjects = await processTemplates(dynamicTemplatesList, 'dynamic');

            const processedObjects = [...processedStaticObjects, ...processedDynamicObjects];

            if (processedObjects.length === 0) {
                console.warn(`No templates sent to Algolia for organisation ${organisationId}`);
                return res.status(200).send({ message: `No valid templates found` });
            }

            // Update Algolia index
            await index.partialUpdateObjects(processedObjects, { createIfNotExists: true });
            console.info(`${processedObjects.length} templates updated in Algolia for organisation ${organisationId}`);
        } catch (err) {
            console.error(`Error processing templates`, err);
            res.status(500).send({ message: err.message });
        }
    }
    
    res.status(200).send({ message: `Templates updated successfully for all organisations` });
});

async function performSearchAndRespond(searchTerms, additionalAlgoliaArgs, designType, adminQuery = false, res, page = 1, hitsPerPage = 10, req, facilityId) {
    try {
        const algoliaConfig = await getAlgoliaConfig(facilityId);
        if (algoliaConfig[0] !== 200) {
            return res.status(algoliaConfig[0]).send(algoliaConfig[1]);
        }

        await initAlgoliaMarketingPrintIndex(algoliaConfig[1].marketingPrintIndex);
        const index = algoliaClient.initIndex(algoliaConfig[1].marketingPrintIndex);
        let defaultAlgoliaArgs = {
            attributesToRetrieve: ["*"],
            hitsPerPage: hitsPerPage,
            page: page - 1, // Algolia uses 0-based pagination
            facets: ["*"],
            facetFilters: [],
            attributesToSnippet: ["*:20"],
            attributesToHighlight: ['name', 'htmlContent', 'imageContent'],
            getRankingInfo: true,
            restrictSearchableAttributes: ['name', 'htmlContent', 'imageContent']
        };

        const authedEmail = auth.loggedUserAccount(req);

        const [adminAccess, viewerAccess] = await Promise.all([
          auth.getUserOrgAccessForModule(authedEmail, 'designer-admin'),
          auth.getUserOrgAccessForModule(authedEmail, 'designer')
        ]);

        const isAdmin = adminAccess.orgs.length > 0;
        const regionsFilter = viewerAccess.allRegions.map((region) => `designRegion:${region.iso_code}`);
        const facilitiesFilter = viewerAccess.allFacilities.map((facility) => `club:${facility.id}`);

        facilitiesFilter.push(`club:global`);

        let algoliaArgs = {
            ...defaultAlgoliaArgs,
            ...additionalAlgoliaArgs
        };

        // attach club filter
        algoliaArgs.facetFilters.push(facilitiesFilter);

        // attach designType filter
        if(designType) {
          algoliaArgs.facetFilters.push(
              [
                `designType:${designType}`,
              ]
            );
        }

        

        // Query for global templates
        algoliaArgs.facetFilters.push([
          'designVisibility:region',
          'designVisibility:global',
          'designVisibility:club',
        ]);

        if(!isAdmin || !adminQuery) {
            // Handle region filters
            if(regionsFilter) {
                algoliaArgs.facetFilters.push([
                    ...regionsFilter,
                    'designRegion:null',
                    'designRegion:-'
                ]);
            }

            algoliaArgs.facetFilters.push([
                'approvalStatus:true',
            ]);

            // Add publishing automation filters
            const currentUnix = moment().unix();

            // Handle boolean conditions with facetFilters
            algoliaArgs.facetFilters.push([
              'publishingAutomationPublishNow:true',
              `publishingAutomationNeverExpire:true`,
              'isPublished:true'
            ]);

            // Handle numeric conditions separately
            const numericFilters = [
              `publishingAutomation.autoPublish<=${currentUnix}`,
              `publishingAutomation.autoUnPublish>=${currentUnix}`
            ];

            // Set numeric filters
            algoliaArgs.numericFilters = numericFilters;
        }

        

        console.log('Final Algolia filters:', algoliaArgs.facetFilters);

        // Main search for suggestedDesigns and suggestedFolderNames
        const results = await index.search(searchTerms, algoliaArgs);

        // Additional search specifically for tags
        const tagSearchArgs = {
            ...algoliaArgs,
            page: 0,
            hitsPerPage: 20,
            restrictSearchableAttributes: ['tags'],
            attributesToRetrieve: ['tags'],
        };
        const tagResults = await index.search(searchTerms, tagSearchArgs);

        // Extract all unique matching tags
        const matchedTags = [...new Set(tagResults.hits.flatMap(hit => hit.tags || []))];

        res.status(200).send({
            designs: results.hits,
            tags: matchedTags,
            results,
            pagination: {
                currentPage: results.page + 1, // Convert back to 1-based pagination
                totalPages: results.nbPages,
                totalHits: results.nbHits,
                hitsPerPage: results.hitsPerPage,
            }, 
            config: {
              algoliaIndex: algoliaConfig[1].marketingPrintIndex,
              algoliaArgs,
            }
        });
    } catch (err) {
        console.error(err);
        res.status(500).send({ message: err.message });
    }
}

// Create reindex manually all data

        // Performance hub trigger to reindex

module.exports = router;
