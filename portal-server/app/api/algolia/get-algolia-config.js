const Router = require('express').Router;
const { get } = require('../../services/dynamodb/club');
const auth = require('../../shared-functions/auth');
const { formatClubList } = require('../clubs/listclubs');

const ALGOLIA_MARKETINGPRINT_INDEX = process.env.ALGOLIA_MARKETINGPRINT_INDEX || '';
const ALGOLIA_WIKI_INDEX = process.env.ALGOLIA_WIKI_INDEX || '';
const ALGOLIA_APP_ID = process.env.ALGOLIA_APP_ID || '';
const ALGOLIA_API_KEY = process.env.ALGOLIA_API_KEY || '';

/**
 * @swagger
 * /api/facilities/{facilityId}/algolia/config:
 *    get:
 *      description: Returns the Algolia configuration including index name, app ID, and API key.
 *      tags:
 *        - Algolia
 *      parameters:
 *        - in: query
 *          name: facilityId
 *          required: true
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of the search results including snippets and highlighted content, if requested.
 *        500:
 *          description: Server error, e.g., if there is an error during the Algolia search request.
 */

const self = (module.exports = Router({ mergeParams: true }).get(
    '/facilities/:facilityID/algolia/config',
    auth.checkFacilityAccess,
    async (req, res, next) => {
        const [status, data] = await self.getAlgoliaConfig(req.params.facilityID);
        return res.status(status).json(data);
    }
));

module.exports.getAlgoliaConfig = async (facilityId, organisationId) => {
    if (!organisationId) {
        const facilityRes = await get(facilityId);
        if (!facilityRes[0]) return [404, { message: 'Facility not found' }];

        const facility = await formatClubList(false, facilityRes[1]);
        if (!facility) return [404, { message: 'Facility not found' }];

        organisationId = facility.organisationId;
    }

    return [
        200,
        {
            wikiIndex: ALGOLIA_WIKI_INDEX + `-${organisationId}`,
            marketingPrintIndex: ALGOLIA_MARKETINGPRINT_INDEX + `-${organisationId}`,
            appId: ALGOLIA_APP_ID,
            apiKey: ALGOLIA_API_KEY,
        },
    ];
};
