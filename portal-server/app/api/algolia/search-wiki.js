const Router = require('express').Router;
const _get = require('lodash/get');
const algoliasearch = require('algoliasearch');
const auth = require('../../shared-functions/auth');
const countries = require('i18n-iso-countries');
const { getAlgoliaConfig } = require('./get-algolia-config');

countries.registerLocale(require("i18n-iso-countries/langs/en.json")); // Register English locale data

/**
 * @swagger
 * /api/algolia/search-wiki:
 *    post:
 *      description: Performs a search against a specified Algolia index based on search terms, club, and/or locale. The search can be further customized using additional Algolia arguments.
 *      tags:
 *        - Search
 *      parameters:
 *        - name: searchTerms
 *          description: The terms to search for in the Algolia index.
 *          in: body
 *          required: true
 *          example: "gymmaster"
 *          type: string
 *        - name: club
 *          description: Internal ID of the club that is making the request. If specified, the search will be filtered based on the region associated with the club.
 *          in: body
 *          required: false
 *          example: "TestClub"
 *          type: string
 *        - name: locale
 *          description: The locale to use for determining the region if a club is not specified. Derived from navigator.languages.
 *          in: body
 *          required: false
 *          example: ["en-US", "en"]
 *          type: array
 *          items:
 *            type: string
 *        - name: algoliaArgs
 *          description: Additional/custom arguments to pass through to the Algolia search request.
 *          in: body
 *          required: false
 *          example:
 *            highlightPreTag: "<mark>"
 *            highlightPostTag: "</mark>"
 *            hitsPerPage: 1
 *          type: object
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of the search results including snippets and highlighted content, if requested.
 *          schema:
 *            type: object
 *            properties:
 *              suggestedArticles:
 *                type: array
 *                items:
 *                  type: object
 *                  properties:
 *                    title:
 *                      type: string
 *                    id:
 *                      type: string
 *                    regions:
 *                      type: array
 *                      items:
 *                        type: string
 *        500:
 *          description: Server error, e.g., if there is an error during the Algolia search request.
 */


const countryNameMap = {
    "United States of America": "United States",
    // ... any other discrepancies between wiki regions and the i18n-iso-countries library here.
};

module.exports = Router({ mergeParams: true }).post(
    '/algolia/search-wiki',
    async (req, res, next) => {
        const clubId = _get(req.body, 'club', "");
        const searchTerms = _get(req.body, 'searchTerms', "");
        const additionalAlgoliaArgs = _get(req.body, 'algoliaArgs', {});

        auth.listClubRegion(clubId, async function(region) {
            const countryFilter = getMappedCountryName(region.iso_code);
            const commonFilter = [[`kb_regional_visibility:${countryFilter}`, "kb_regional_visibility:global"]];
            const [status, data] = await performSearchAndRespond(commonFilter, searchTerms, additionalAlgoliaArgs, clubId);
            return res.status(status).json(data);
        });
    }
);

function getMappedCountryName(isoCode) {
    const officialName = countries.getName(isoCode, "en", {select: "official"});
    return countryNameMap[officialName] || officialName;
}

async function performSearchAndRespond(commonFilter, searchTerms, additionalAlgoliaArgs, facilityId) {
    try {
        const algoliaConfig = await getAlgoliaConfig(facilityId);
        if (algoliaConfig[0] !== 200) return algoliaConfig;

        const algoliaClient = algoliasearch(algoliaConfig[1].appId, algoliaConfig[1].apiKey);
        const index = algoliaClient.initIndex(algoliaConfig[1].wikiIndex);

        let defaultAlgoliaArgs = {
            attributesToRetrieve: ['_id', 'kb_regional_visibility', 'kb_title', 'kb_viewcount', 'kb_seo_description'],
            hitsPerPage: 12,
            facetFilters: commonFilter,
            attributesToSnippet: ['kb_body:50'],  // snippet the first 50 words of the 'kb_body' field
            attributesToHighlight: ['kb_title']
        };

        let algoliaArgs = {
            ...defaultAlgoliaArgs,
            ...additionalAlgoliaArgs  // merge additionalAlgoliaArgs into defaultAlgoliaArgs
        };

        // console.log('searchTerms, algoliaArgs', searchTerms, algoliaArgs);

        const results = await index.search(searchTerms, algoliaArgs);
        const suggestedArticles = results.hits;

        return [200, { suggestedArticles }];
    } catch (err) {
        console.error('Failed to perform Algolia search on wiki:', err);
        return [500, { message: 'Failed to perform Algolia search on wiki.' }];
    }
}
