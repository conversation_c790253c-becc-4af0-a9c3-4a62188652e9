const Router = require('express').Router;
const algoliasearch = require('algoliasearch');
const auth = require('../../shared-functions/auth');

/**
 * @swagger
 * /api/facilities/{facilityId}/algolia/index:
 *    get:
 *      description: Initializes the Algolia marketing print index with the facility's configuration.
 *      tags:
 *        - Algolia
 *      parameters:
 *        - in: query
 *          name: facilityId
 *          required: true
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of the search results including snippets and highlighted content, if requested.
 *        500:
 *          description: Server error, e.g., if there is an error during the Algolia search request.
 */

const self = (module.exports = Router({ mergeParams: true }).get(
    '/facilities/:facilityID/algolia/config',
    auth.checkFacilityAccess,
    async (req, res, next) => {
        const [status, data] = await self.getAlgoliaConfig(req.params.facilityID);
        return res.status(status).json(data);
    }
));

module.exports.initAlgoliaMarketingPrintIndex = async (indexName) => {
    try {
        const algoliaClient = algoliasearch(process.env.ALGOLIA_APP_ID || '', process.env.ALGOLIA_API_KEY || '');
        const index = algoliaClient.initIndex(indexName);
        await index.setSettings({
            searchableAttributes: [
                'unordered(name)',
                'unordered(htmlContent)',
                'unordered(tags)',
                'unordered(designVisibility)',
                'unordered(designType)',
                'unordered(imageContent)',
            ],
            attributesForFaceting: [
                'searchable(approvalStatus)',
                'searchable(club)',
                'searchable(designRegion)',
                'searchable(designType)',
                'searchable(designVisibility)',
                'searchable(isPublished)',
                'searchable(publishingAutomationNeverExpire)',
                'searchable(publishingAutomationPublishNow)',
            ],
        });
    } catch (error) {
        console.error('Error initializing Algolia marketing print index:', error);
        return [500, { error: 'Failed to initialize Algolia marketing print index' }];
    }
};
