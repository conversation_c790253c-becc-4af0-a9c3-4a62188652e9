const Router = require('express').Router;

const { checkGlobalAdminAccess } = require('../../../shared-functions/auth');
const pbCalendarTypes = require('../../../services/dynamodb/pbcalendartypes');

/** @swagger
 * /api/programs/calendars/calendar-types:
 *    get:
 *      description: Returns list of all calendar types
 *      tags:
 *        - Brands
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all currencies.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

module.exports = Router({ mergeParams: true }).get(
    '/programs/calendars/calendar-types',
    checkGlobalAdminAccess,
    async (req, res) => {
        try {
            const data = await pbCalendarTypes.scanAll();
            return res.status(200).send(data);
        } catch (err) {
            if (err.message) {
                res.status(500).send({ message: err.message });
            } else {
                res.status(500).send({
                    error: 'An error occured while trying this operation. Please refer to the server logs for details.'
                });
            }
        }
    }
);
