const router = require('express').Router();
const auth = require('../../shared-functions/auth');

/**
 * @swagger
 * /api/user/me/modules/{moduleId}/org-access:
 *    post:
 *      description: List the organisations, facilities, and regions the user has access to for a specific module.
 *      tags:
 *        - User
 *      security:
 *        - googleAppsAccount:
 *          - authenticated
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      parameters:
 *        - name: moduleId
 *          in: path
 *          required: true
 *      responses:
 *        200:
 *          description: Returns true/false Json response if the linking request was successful.
 *        500:
 *          description: Internal error from the API - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this resource.
 */

module.exports = router.get('/user/me/modules/:moduleId/org-access', async (req, res, next) => {
    const { moduleId } = req.params;
    const email = auth.loggedUserAccount(req);
    const access = await auth.getUserOrgAccessForModule(email, moduleId);
    return res.status(200).json(access);
});