const Router = require('express').Router;
const _get = require('lodash/get');
const algoliasearch = require('algoliasearch');

const OpenAI = require('openai');
const { getAlgoliaConfig } = require('../../algolia/get-algolia-config');
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY || 'FAKE_KEY_TO_PASS_JENKINS_API_SPEC_BUILD' });

/**
 * @swagger
 * /api/ai/helpers/analyse-support-ticket:
 *    put:
 *      description: A simple AI helper to analise the contents of a support ticket and create suggestions based on available topics as well as suggested algolia search topics...
 *      tags:
 *        - "AI"
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      consumes:
 *        - application/json
 *      produces:
 *        - application/json
 *      parameters:
 *        - name: messageBody
 *          description: Body of the message to send
 *          in: body
 *          required: true
 *          type: string
 *        - name: topicData
 *          description: Json object/array of the topicData returned from osTicket (ticketing/support system)
 *          in: body
 *          required: true
 *          type: string
 *        - name: facilityId
 *          description: The internal ID of the club needed to determine the correct Algolia index to search against.
 *          in: query
 *      responses:
 *        200:
 *          description: Success message.
 *        500:
 *          description: Internal service error.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

const analyseSupportTicket = async(ticket, topicData, facilityId, res) => {
    try {

        const formattedTopicData = topicData.map(department => {
            return `${department.department_name_id}|${department.department_name}: ${department.help_topic.map(topic => `${topic.split('|')[0]}|${topic.split('|')[1]}`).join(', ')}`;
        }).join('\n');

        let gptInstructions = {
            messages: [{
                role: 'system',
                content: `You are a helpful assistant. Your task is to categorize the following support ticket into one of the provided departments and help topics. It's important that you only choose from the exact options given in the list. If a ticket might seem to fit multiple topics, select the one that aligns most closely with the content of the ticket. Here are the departments and their help topics:\n${formattedTopicData}\n\nFor example, if a ticket mentions 'billing issue', it might be categorized under '1|Finance: 1.1|Billing Problems'. Please provide your response in the following JSON format: {"Department ID": "chosen_department_id", "Department": "chosen_department_name", "Help Topic ID": "chosen_help_topic_id", "Help Topic": "chosen_help_topic_name"}.`
            },{
                role: 'user',
                content: ticket
            }],
            model: 'gpt-4o-mini',
        }

        const completion = await openai.chat.completions.create(gptInstructions);
        const responseContent = completion.choices[0].message.content;

        let parsedResponse;
        try {
            parsedResponse = JSON.parse(responseContent);
        } catch (err) {
            throw new Error(`Failed to parse model response as JSON: ${responseContent}`);
        }

        const selectedDepartmentID = parsedResponse["Department ID"];
        const selectedDepartmentName = parsedResponse.Department;
        const selectedHelpTopicID = parsedResponse["Help Topic ID"];
        const selectedHelpTopic = parsedResponse["Help Topic"];


        // const responseContent = completion.choices[0].message.content;
        // let parsedResponse;
        // try {
        //     parsedResponse = JSON.parse(responseContent);
        // } catch (err) {
        //     throw new Error(`Failed to parse model response as JSON: ${responseContent}`);
        // }

        // console.log('responseContent', responseContent)

        // const selectedDepartmentName = parsedResponse.Department;
        // const selectedHelpTopic = parsedResponse["Help Topic"];

        // if (topicMatch && topicMatch[0][0] > 0.6) {  // Adjust this threshold as needed
        //     matchedHelpTopic = topicMatch[0][1];
        // } else {
        //     throw new Error(`Help topic not found: ${selectedHelpTopic}`);
        // }

        // const selectedTopic = selectedDepartment.help_topic.find(topic => topic.includes(matchedHelpTopic));
        // if (!selectedTopic) {
        //     throw new Error(`Help topic not found after fuzzy matching: ${matchedHelpTopic}`);
        // }


        // const [help_topic_id, help_topic] = selectedTopic.split('|');

        // New API call to ChatGPT to summarize the text
        const summaryInstructions = {
            messages: [{
                role: 'system',
                content: `Please summarize the following text into a primary search term followed by a list of optional keywords/phrases for an Algolia search. Exclude general terms such as "Forwarded email". The response should be formatted as: primarySearchTerm|optionalKeyword1,optionalKeyword2,... Please ensure any phrases are wrapped in double quotes.`
            },{
                role: 'user',
                content: ticket
            }],
            model: 'gpt-4o-mini',
        }

        const summaryCompletion = await openai.chat.completions.create(summaryInstructions);
        const algoliaSearchContent = summaryCompletion.choices[0].message.content;

        if (!algoliaSearchContent.includes('|')) {
            console.error("Unexpected response format from GPT:", algoliaSearchContent);
            // Handle the error, e.g., you can set default values or return a specific error response
            algoliaPrimarySearchTerm = algoliaSearchContent; // Use the whole response as the primary term
            algoliaOptionalWords = ""; // Set optional words to an empty string
        } else {
            const [primaryTerm, optionalWords = ""] = algoliaSearchContent.split('|');
            algoliaPrimarySearchTerm = primaryTerm;
            algoliaOptionalWords = optionalWords;
        }

        console.log('algoliaPrimarySearchTerm', algoliaPrimarySearchTerm)
        console.log('algoliaOptionalWords', algoliaOptionalWords.split(','))

        // Search against Algolia
        const algoliaConfig = await getAlgoliaConfig(facilityId);
        if (algoliaConfig[0] !== 200) throw new Error(algoliaConfig[1].message);

        const algoliaClient = algoliasearch(algoliaConfig[1].appId, algoliaConfig[1].apiKey);
        const index = algoliaClient.initIndex(algoliaConfig[1].wikiIndex);

        const searchResults = await index.search(algoliaPrimarySearchTerm, {
            optionalWords: algoliaOptionalWords.split(','),
            attributesToRetrieve: ['_id', 'kb_regional_visibility', 'kb_title' ],
            hitsPerPage: 3
        })

        const suggestedArticles = searchResults.hits.map(hit => ({ title: hit.kb_title, id: hit._id, regions: hit.kb_regional_visibility }));

        // Return results
        res.status(200).send({
            department_name_id: selectedDepartmentID,
            department_name: selectedDepartmentName,
            help_topic_id: selectedHelpTopicID,
            help_topic: selectedHelpTopic,
            suggestedArticles
        });
    } catch (err) {
        console.error(err);  // Log the error
        res.status(500).send({ message: err.message });
    }
}

exports.analyseSupportTicket = analyseSupportTicket;

module.exports = Router({ mergeParams: true }).post(
  '/ai/helpers/analyse-support-ticket',
    async (req, res, next) => {
        const ticket = _get(req.body, 'messageBody', "");
        const topicData = _get(req.body, 'topicData', []);
        const facilityId = _get(req.query, 'facilityId', null);

        analyseSupportTicket(ticket, topicData, facilityId, res);
    }
);
