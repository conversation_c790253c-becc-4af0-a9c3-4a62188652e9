'use strict';
const Router = require('express').Router;
const auth = require('../../../shared-functions/auth');
const { listOrganisationCustomPages } = require('../../../services/dynamodb/designercustompages');

/**
 * @swagger
 * /api/marketing/custom-pages:
 *    get:
 *      description: Returns a list of all custom pages for an organisation.
 *      tags:
 *        - Marketing
 *      parameters:
 *        - name: organisationId
 *          description: Internal ID of the organisation to return custom pages for.
 *          in: query
 *          required: true
 *          example: 026132f1-c4bb-4baa-90e3-97c69cdfaca2
 *          type: string
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all designs for the print editor.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access the marketing/print api's.
 */
module.exports = Router({ mergeParams: true }).get(
    '/marketing/custom-pages',
    auth.checkModuleAccess('designer'),
    async (req, res, next) => {
        const [status, data] = await listOrganisationCustomPages(req.query.organisationId);
        return res.status(status).json(data);
    }
);
