'use strict';
const Router = require('express').Router;
const auth = require('../../../shared-functions/auth');
const { listOrganisationCustomPages } = require('../../../services/dynamodb/designercustompages');
const { get } = require('../../../services/dynamodb/club');
const { formatClubList } = require('../../clubs/listclubs');

/**
 * @swagger
 * /api/facilities/{facilityId}/marketing/custom-pages:
 *    get:
 *      description: Returns a list of all custom pages for a facility.
 *      tags:
 *        - Marketing
 *      parameters:
 *        - name: facilityId
 *          description: Internal ID of the facility to return custom pages for.
 *          in: query
 *          required: true
 *          example: 026132f1-c4bb-4baa-90e3-97c69cdfaca2
 *          type: string
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all designs for the print editor.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access the marketing/print api's.
 */
module.exports = Router({ mergeParams: true }).get(
    '/facilities/:facilityID/marketing/custom-pages',
    auth.checkModuleAccess('designer'),
    async (req, res, next) => {
        const { facilityID } = req.params;
        const facilityRes = await get(facilityID);
        if (!facilityRes[0]) return res.status(404).json({ message: 'Facility not found' });

        const facility = await formatClubList(false, facilityRes[1]);
        if (!facility) return res.status(404).json({ message: 'Facility not found' });

        const pagesRes = await listOrganisationCustomPages(facility.organisationId);
        if (pagesRes[0] !== 200) return res.status(pagesRes[0]).json(pagesRes[1]);

        const pages = pagesRes[1].filter((page) => {
            const assignedToRegion = !page.regions || page.regions.includes(facility.localisation.country.iso_code);
            const assignedToFacility = !page.facilities || page.facilities.includes(facilityID);
            return assignedToRegion && assignedToFacility;
        });

        return res.status(200).json(pages);
    }
);
