'use strict';
const Router = require('express').Router;
const auth = require('../../../shared-functions/auth');
const { upsertCustomPage, listOrganisationCustomPages } = require('../../../services/dynamodb/designercustompages');

/**
 * @swagger
 * /api/marketing/custom-pages:
 *    put:
 *      description: Upserts a custom page for an organisation.
 *      tags:
 *        - Marketing
 *      parameters:
 *        - name: organisationId
 *          description: Internal ID of the organisation to return custom pages for.
 *          in: query
 *          required: true
 *          example: 026132f1-c4bb-4baa-90e3-97c69cdfaca2
 *          type: string
 *        - name: body
 *          description: Custom page data to upsert.
 *          in: body
 *          required: true
 *          schema:
 *            type: object
 *            properties:
 *              id:
 *                type: string
 *              name:
 *                type: string
 *              facilities:
 *                type: array
 *                items:
 *                  type: string
 *                  description: Facility ID
 *              regions:
 *                type: array
 *                items:
 *                  type: string
 *                  description: Region ID
 *              type:
 *                type: string
 *                enum: [wysiwyg, pdf, iframe]
 *              iframeUrl:
 *                type: string
 *              wysiwygContent:
 *                type: string
 *              pdfUrl:
 *                type: string
 *              organisationId:
 *                type: string
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all designs for the print editor.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access the marketing/print api's.
 */

module.exports = Router({ mergeParams: true }).put(
    '/marketing/custom-pages',
    auth.checkModuleAccess('designer-admin'),
    async (req, res, next) => {
        const doc = req.body;

        if (!doc.slug) return res.status(400).json({ message: 'Slug is required.' });
        if (!doc.name) return res.status(400).json({ message: 'Name is required.' });
        if (!doc.type) return res.status(400).json({ message: 'Type is required.' });

        if (doc.type === 'iframe' && !doc.iframeUrl) {
            return res.status(400).json({ message: 'Iframe URL is required for iframe type.' });
        }

        if (doc.type === 'pdf' && !doc.pdfUrl) {
            return res.status(400).json({ message: 'PDF URL is required for PDF type.' });
        }

        if (doc.type === 'wysiwyg' && !doc.wysiwygContent) {
            return res.status(400).json({ message: 'WYSIWYG content is required for WYSIWYG type.' });
        }

        const pages = await listOrganisationCustomPages(req.body.organisationId);
        if (pages[0] !== 200) return res.status(pages[0]).json(pages[1]);

        const existingSlug = pages[1].find((page) => page.slug === req.body.slug && page.id !== req.body.id);
        if (existingSlug) {
            return res.status(400).json({ message: 'A page with this slug already exists for this organisation.' });
        }

        const [status, data] = await upsertCustomPage(req.body);
        return res.status(status).json(data);
    }
);
