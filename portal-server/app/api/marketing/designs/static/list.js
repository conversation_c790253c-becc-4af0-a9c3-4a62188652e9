'use strict';
const Router    = require('express').Router
const moment    = require('moment');
const _get      = require('lodash/get');

const auth      = require('../../../../shared-functions/auth');
const queryTable    = require('../../../../shared-functions/queryTable');
const { getCachedDesigns, setDesignsCache } = require('../../../../services/redis/designs');
const { getPromise: getFacility } = require('../../../clubs/clubpage/clubdata');
const scanTable = require('../../../../shared-functions/scanTable');

const DB_DESIGN_STATIC_TABLE  = process.env.DB_DESIGN_STATIC_TABLE || 'design-static-templates';

/**
 * @swagger
 * /api/marketing/designs/{club}/list:
 *    get:
 *      description: Returns list of available designs for a given club, that may be used within designs (Print Designs) created within the hub's marketing/print editor.
 *      tags:
 *        - Marketing
 *      parameters:
 *        - name: club
 *          description: Internal ID of the club that is making the request.
 *          in: path
 *          required: true
 *          example: Milton
 *          type: string
*        - name: designAdmin
 *          description: Return all design data (admin view / requires user to be administrator).
 *          in: query
 *          required: false
 *          example: true
 *          type: boolean
 *        - name: brand
 *          description: Filters designs by brand
 *          in: query
 *          required: false
 *          example: UBX
 *          type: string
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all static designs.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access the marketing/print api's.
 */ 

async function checkAdminAccessToMP (req, res, next) {
    const adminDesigns = req.query.designAdmin?.toLowerCase() === 'true';
    if (!adminDesigns) return next();

    const email = auth.loggedUserAccount(req);
    const facilityId = auth.getFacilityIdFromParams(req);
    const hasAccess = await auth.hasModuleAccess({ email, moduleId: 'designer-admin', facilityId })
    
    if (!hasAccess) {
        return res.status(403).send({ message: 'You do not have access to this function.' });
    }

    return next();
}

let self = module.exports = Router({mergeParams: true})
.get('/marketing/designs/static/:club/list', auth.checkModuleAccess('designer'), checkAdminAccessToMP, async (req, res, next) => {
    try {
        let clubId = (req.params.club === undefined) ? false : req.params.club;
        let brand = (req.query.brand === undefined) ? false : req.query.brand.toLowerCase();
        let adminDesigns = (req.query.designAdmin === undefined) ? false : (String(req.query.designAdmin).toLowerCase() == 'true') ? true : false;
        const authedEmail = auth.loggedUserAccount(req);

        const { allFacilities: userClubs, allRegions: userRegions } =
            await auth.getUserOrgAccessForModule(authedEmail, 'designer');

        const cached = await getCachedDesigns(clubId, 'static', adminDesigns);
        if (cached[0] === 200) return res.status(200).send(cached[1]);

        self.getStaticDesigns(adminDesigns, null, clubId, async (templates) => {
            const filteredTemplates = self.filterTemplates(templates, userRegions, userClubs);
            await setDesignsCache(clubId, 'static', adminDesigns, filteredTemplates);
            res.status(200).send(filteredTemplates);
        })
    } catch(error) {
        next(error)
    }
});

module.exports.checkAdminAccessToMP = checkAdminAccessToMP;

module.exports.filterTemplates = (templates, userRegions, userClubs) => {
    return templates.filter((template) => {
        const isGlobal = template.designVisibility === 'global';
        const hasAccessToRegion = template.designVisibility === 'region' && userRegions.some(r => _get(template, 'designRegion', []).includes(r.iso_code));
        const hasAccessToClub = template.designVisibility === 'club' && userClubs.some(c => _get(template, 'club', []).includes(c.id));
        return isGlobal || hasAccessToRegion || hasAccessToClub;
    });
}

module.exports.getStaticDesigns = async function(adminQuery, organisationId, clubId, callback) {
    let templates = [];

    if (!organisationId) {
        const facility = await getFacility(clubId, true);
        if (!facility) throw new Error('Unable to get details of facility: ' + clubId);
        organisationId = facility.organisationId;
    }

    async function formatStaticFileResponse(adminQuery, element) {
        // Auto publish/unpublish logic...
        let isPublished = true;
        if(
            element.publishingAutomation !== undefined && 
            element.publishingAutomationPublishNow !== undefined && 
            element.publishingAutomationNeverExpire !== undefined
        ) {
            
            let autoPublish = (element.publishingAutomation.autoPublish === undefined) ? moment().subtract(1, 'second').unix() : element.publishingAutomation.autoPublish
            let autoUnPublish = (element.publishingAutomation.autoUnPublish === undefined) ? moment().add(1, 'second').unix() : element.publishingAutomation.autoUnPublish

            // Auto-publish date is before the current time - design should not yet be published
            if(element.publishingAutomationPublishNow !== true && !moment(autoPublish).isBefore(moment().unix())) isPublished = false;

            // Auto-unpublish date is after the current time - design should also be unpublished..
            if(element.publishingAutomationNeverExpire !== true && !moment(autoUnPublish).isAfter(moment().unix())) isPublished = false;
        }

        if(!adminQuery && !isPublished) {
            return;
        } else {
            element.isPublished = isPublished;
        }

        element.tags = (element.tags === undefined) ? [] : parseJson(element.tags)

        const updatedString =
            typeof element.thumbnails_large === 'string'
                ? element.thumbnails_large.replace(/\\/g, '').replace(/\"\"/g, '').replace(/\"\[/g, '[').replace(/\]\"/g, ']')
                : null;
                
        element.thumbnails_large =  updatedString ? JSON.parse(updatedString) : element.thumbnails_large;

        const designBrand = (element.brand === undefined) ? "ALL" : element.brand
        if(element.brand === undefined) element.brand = designBrand;

        templates.push(element);
        return element;
    }

    // Return global templates...
    const globalStaticTemplates = new Promise(async function(resolve) {

        const userQueryParams = {
            TableName : DB_DESIGN_STATIC_TABLE,
            IndexName : 'designVisibilityOrgId-index',
            KeyConditionExpression : '#designVisibility = :designVisibility AND #organisationId = :orgId',
            FilterExpression: 'deleted = :deleted_val AND approvalStatus = :approvalStatus',
            ExpressionAttributeNames: {
                '#designVisibility': 'designVisibility',
                '#organisationId': 'organisationId'
            },
            ExpressionAttributeValues : {
                ':designVisibility' : 'global',
                ':deleted_val': 0,
                ':approvalStatus': true,
                ':orgId': organisationId
            },
            ScanIndexForward: false
        };

        const adminQueryParams = {
            TableName : DB_DESIGN_STATIC_TABLE,
            FilterExpression: 'deleted = :deleted_val AND #organisationId = :orgId',
            ExpressionAttributeNames: { '#organisationId': 'organisationId' },
            ExpressionAttributeValues : {
                ':deleted_val': 0,
                ':orgId': organisationId
            }
        };

        let returnedDesigns = [];

        if (adminQuery) {
            returnedDesigns = await scanTable(DB_DESIGN_STATIC_TABLE, adminQueryParams);
        } else {
            returnedDesigns = await queryTable(userQueryParams, true);
        }
        
        await Promise.all(
            returnedDesigns.map((element) => formatStaticFileResponse(adminQuery, element))
        );

        resolve();
    });

    // Also return the individual club unique designs...
    const regionalTemplates = new Promise(function(resolve, reject) {
        if (adminQuery) return resolve();

        auth.listClubRegion(clubId, async function(region) {
            if(!region) return;

            const params = {
                TableName : DB_DESIGN_STATIC_TABLE,
                IndexName : 'designVisibilityOrgId-index',
                KeyConditionExpression : '#designVisibility = :designVisibility AND #organisationId = :orgId',
                FilterExpression: 'deleted = :deleted_val AND approvalStatus = :approvalStatus  AND contains(#designRegion, :designRegion)',
                ExpressionAttributeNames: {
                    '#designRegion': 'designRegion',
                    '#designVisibility': 'designVisibility',
                    '#organisationId': 'organisationId'
                },
                ExpressionAttributeValues : {
                    ':designVisibility' : 'region',
                    ':deleted_val': 0,
                    ':approvalStatus': true,
                    ':designRegion': region.iso_code,
                    ':orgId': organisationId
                },
                ScanIndexForward: false
            };

            // Query & paginate the entire table...
            const returnedDesigns = await queryTable(params, true);
            
            await Promise.all(
                returnedDesigns.map((element) => formatStaticFileResponse(adminQuery, element))
            );
    
            resolve();
        });
    });

    // Also return the individual club unique designs...
    const clubStaticTemplates = new Promise(async function(resolve, reject) {
        if (adminQuery) return resolve();

        const params = {
            TableName : DB_DESIGN_STATIC_TABLE,
            IndexName : 'club-index',
            KeyConditionExpression : 'club = :clubID',
            FilterExpression: 'deleted = :deleted_val AND designVisibility = :designVisibility AND approvalStatus = :approvalStatus',
            ExpressionAttributeValues : {
                ':clubID' : clubId,
                ':designVisibility' : 'club',
                ':deleted_val': 0,
                ':approvalStatus': true
            },
            ScanIndexForward: false
        };

        // Query & paginate the entire table...
        const returnedDesigns = await queryTable(params, true);
        
        await Promise.all(
            returnedDesigns.map((element) => formatStaticFileResponse(adminQuery, element))
        );

        resolve();
    });

    // When all queries to DynamoDB completed...
    Promise.all([
        globalStaticTemplates,
        regionalTemplates,
        clubStaticTemplates
    ]).then(() => callback(templates));
}

function parseJson(json) {
    // Item is already json...
    if(typeof json === 'object') return json;

    try {
        return JSON.parse(json);
    } catch (e) {
        return json;
    }
}