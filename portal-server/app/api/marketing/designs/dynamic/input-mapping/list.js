'use strict';
const Router = require('express').Router;

const _ = require('lodash')

const TableName = process.env.DB_DESIGN_INPUT_MAPPING_TABLE || 'design-input-mapping-prd';

const { scanItems, queryFilterExpression } = require('../../../../../services/dynamodb/dynamodb');

const getDesigns = require('../list')

/**
 * @swagger
 * /api/marketing/designs/input-mapping/{clubId}:
 *    get:
 *      description: Returns list of input mapping in marketing and print designs and design templates
 *      tags:
 *        - Marketing
 *      parameters:
 *        - name: clubId
 *          description: Internal ID of the club that is making the request.
 *          in: path
 *          required: true
 *          example: Milton
 *          type: string
 *        - name: availableFeatures
 *          description: Return all items with specific features
 *          in: query
 *          required: false
 *          example: ReviewManagement
 *          type: boolean
 *        - name: brand
 *          description: Filters designs by brand
 *          in: query
 *          required: false
 *          example: UBX
 *          type: string
 *        - name: features
 *          description: Filter results based on features
 *          in: query
 *          required: false
 *          example: ReviewManagement
 *          type: string
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all items.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access the marketing/print api's.
 */

let self = (module.exports = Router({ mergeParams: true }).get('/marketing/designs/input-mapping/:clubId', routeHandler));

async function routeHandler(req, res, next) {
    try {
        const { clubId } = req.params || {};
        const { brand, features, availableFeatures } = req.query || {};

        self.getInputMapping(
            { 
                brand, 
                features, 
                clubId,
                availableFeatures
            }, (result) => {
            res.status(200).send(result);
        });
    } catch (error) {
        next(error);
    }
};

module.exports.getInputMapping = (queries, callback) => {
    // remove all query that has no values
    const {clubId, availableFeatures, ...queryValues} = _.pickBy(queries, _.identity);

    // convert query obj to dynamodb filter expression
    const queryFilters = queryFilterExpression(queryValues);

    // modify filters if availableFeatures is present
    if(availableFeatures) {
        queryFilters.FilterExpression += " and contains(#availableFeatures, :availableFeatures)";
        queryFilters.ExpressionAttributeNames = {
            ...queryFilters.ExpressionAttributeNames,
            '#availableFeatures': 'availableFeatures',
        },
        queryFilters.ExpressionAttributeValues = {
            ...queryFilters.ExpressionAttributeValues,
            ':availableFeatures': availableFeatures,
        };
    }

    // params for db request
    const params = {
        TableName,
        ...queryFilters
    };

    const getDesignFeatures = new Promise(async (resolve, reject) => {
        try {
            const result = await scanItems(params)
            resolve(result.Items)
        } catch (error) {
            reject(error)
        }
    })

    // get params required for /marketing/designs/:club/list
    const {brand} = queries

    // get the design templates
    const getDesignTemplates = new Promise( (resolve, reject) => {
        getDesigns.returnTemplates(false, null, clubId, brand, function(templates) {
            if(templates) {
                const templatesObj = {}
                templates.map(template => {
                    const { id, ...data } = template
                    templatesObj[id] = data
                    return template
                })
                resolve(templatesObj)
                return
            }
            reject(false)
        })
    })

    // get design templates and structure it based on what features it has
    Promise.all([getDesignTemplates, getDesignFeatures]).then(
        function([templates, designFeatures]) {
            // add the design based on the designFeatures record
            const result = designFeatures.map( item => {
                return {
                    ...item,
                    design: templates[item.designId]
                }
            }).filter(item => item.design)
            callback(result);
        }
    );

    
};
