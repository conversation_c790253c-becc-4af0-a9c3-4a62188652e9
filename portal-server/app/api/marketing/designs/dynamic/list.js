'use strict';
const Router    = require('express').Router
const fetch     = require('node-fetch');
const moment    = require('moment');

const auth                = require('../../../../shared-functions/auth');
const queryTable          = require('../../../../shared-functions/queryTable');
const { filterTemplates, checkAdminAccessToMP } = require('../static/list');
const { getCachedDesigns, setDesignsCache } = require('../../../../services/redis/designs');
const { getPromise: getClub } = require('../../../clubs/clubpage/clubdata');
const scanTable = require('../../../../shared-functions/scanTable');

const DB_DESIGN_TEMPLATES_TABLE  = process.env.DB_DESIGN_TEMPLATES_TABLE || 'design-templates';

// Where to store generated thumbnails...
const THUMBNAILS_BUCKET = process.env.THUMBNAILS_BUCKET || 'content.gymsystems.co';
const THUMBNAILS_DIRECTORY = process.env.THUMBNAILS_DIRECTORY || 'hub/design-render-thumbnails';
/**
 * @swagger
 * /api/marketing/designs/{club}/list:
 *    get:
 *      description: Returns list of available designs for a given club, that may be used within designs (Print Designs) created within the hub's marketing/print editor.
 *      tags:
 *        - Marketing
 *      parameters:
 *        - name: club
 *          description: Internal ID of the club that is making the request.
 *          in: path
 *          required: true
 *          example: Milton
 *          type: string
 *        - name: designAdmin
 *          description: Return all design data (admin view / requires user to be administrator).
 *          in: query
 *          required: false
 *          example: true
 *          type: boolean
 *        - name: brand
 *          description: Filters designs by brand
 *          in: query
 *          required: false
 *          example: UBX
 *          type: string
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all designs for the print editor.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access the marketing/print api's.
 */
var self = module.exports = Router({mergeParams: true})
.get('/marketing/designs/:club/list', auth.checkModuleAccess('designer'), checkAdminAccessToMP, async (req, res, next) => {
    try {
        let clubId = (req.params.club === undefined) ? false : req.params.club;
        let brand = (req.query.brand === undefined) ? false : req.query.brand.toLowerCase();
        let adminDesigns = (req.query.designAdmin === undefined) ? false : (String(req.query.designAdmin).toLowerCase() == 'true') ? true : false;
        const authedEmail = auth.loggedUserAccount(req);

        const { allFacilities: userClubs, allRegions: userRegions } =
            await auth.getUserOrgAccessForModule(authedEmail, 'designer');

        const cached = await getCachedDesigns(clubId, 'dynamic', adminDesigns);
        if (cached[0] === 200) return res.status(200).send(cached[1]);

        self.returnTemplates(adminDesigns, null, clubId, brand, async (templates) => {
            const filteredTemplates = filterTemplates(templates, userRegions, userClubs);
            await setDesignsCache(clubId, 'dynamic', adminDesigns, filteredTemplates);
            res.status(200).send(filteredTemplates);
        })
    } catch(error) {
        next(error)
    }
});

module.exports.returnTemplates = async function(adminQuery, organisationId, clubId, clubBrand, callback) {

    let templates = [];
    const thumbnailsS3Dir = `https://${THUMBNAILS_BUCKET}/${THUMBNAILS_DIRECTORY}`.replace(/\/?$/, '/') 

    if (!organisationId) {
        const club = await getClub(clubId, true);
        if (!club) throw new Error('Club not found');
        organisationId = club.organisationId;
    }

    // Return global templates...
    const globalTemplates = new Promise(async function(resolve) {

        const userQueryParams = {
            TableName : DB_DESIGN_TEMPLATES_TABLE,
            IndexName : 'designVisibilityOrgId-index',
            KeyConditionExpression : 'designVisibility = :designVisibility AND #orgId = :orgId',
            FilterExpression: 'deleted = :deleted_val AND approvalStatus = :approvalStatus',
            ExpressionAttributeNames: { '#orgId': 'organisationId' },
            ExpressionAttributeValues : {
                ':designVisibility' : 'global',
                ':deleted_val': 0,
                ':approvalStatus': true,
                ':orgId': organisationId,
            },
            ScanIndexForward: false
        };

        const adminQueryParams = {
            TableName : DB_DESIGN_TEMPLATES_TABLE,
            FilterExpression: 'deleted = :deleted_val AND #orgId = :orgId',
            ExpressionAttributeNames: { '#orgId': 'organisationId' },
            ExpressionAttributeValues : {
                ':deleted_val': 0,
                ':orgId': organisationId,
            }
        };

        let returnedDesigns = [];

        if (adminQuery) {
            returnedDesigns = await scanTable(DB_DESIGN_TEMPLATES_TABLE, adminQueryParams);
        } else {
            returnedDesigns = await queryTable(userQueryParams, true);
        }
        
        await Promise.all(
            returnedDesigns.map((element) => formatDesignResponse(adminQuery, element))
        );
        
        resolve();
    });

    // Are there any regional templates we should be returning for this club?
    const regionalTemplates = new Promise(async function(resolve, reject) {
        if (adminQuery) return resolve();

        auth.listClubRegion(clubId, async function(region) {
            if (!region) return;
            
            const regionalDesignsQuery = {
                TableName : DB_DESIGN_TEMPLATES_TABLE,
                IndexName : 'designVisibilityOrgId-index',
                KeyConditionExpression : 'designVisibility = :designVisibility AND #orgId = :orgId',
                FilterExpression: 'deleted = :deleted_val AND approvalStatus = :approvalStatus AND contains(#designRegion, :designRegion)',
                ExpressionAttributeNames: {
                    '#orgId': 'organisationId',
                    '#designRegion': 'designRegion'
                },
                ExpressionAttributeValues : {
                    ':designVisibility' : 'region',
                    ':deleted_val': 0,
                    ':approvalStatus': true,
                    ':designRegion': region.iso_code,
                    ':orgId': organisationId,
                },
                ScanIndexForward: false
            };

            // Query & paginate the entire table...
            const returnedDesigns = await queryTable(regionalDesignsQuery, true);
            
            await Promise.all(
                returnedDesigns.map((element) => formatDesignResponse(adminQuery, element))
            )
            
            resolve();
        })
    });


    // Also return the individual club unique designs...
    const clubTemplates = new Promise(async function(resolve, reject) {
        if (adminQuery) return resolve();
        
        const clubDesignsQuery = {
            TableName : DB_DESIGN_TEMPLATES_TABLE,
            IndexName : 'club-index',
            KeyConditionExpression : 'club = :clubID',
            FilterExpression: 'deleted = :deleted_val AND designVisibility = :designVisibility AND approvalStatus = :approvalStatus',
            ExpressionAttributeValues : {
                ':clubID' : clubId,
                ':designVisibility' : 'club',
                ':deleted_val': 0,
                ':approvalStatus': true
            },
            ScanIndexForward: false
        };

        // Query & paginate the entire table...
        const returnedDesigns = await queryTable(clubDesignsQuery, true);
        
        await Promise.all(
            returnedDesigns.map((element) => formatDesignResponse(adminQuery, element))
        )

        resolve();
    });


    // When all queries to DynamoDB completed...
    Promise.all([
        globalTemplates,
        regionalTemplates,
        clubTemplates
    ]).then(() => callback(templates));


    function parseDesign(element) {

        element.tags = (element.tags === undefined) ? [] : parseJson(element.tags)

        // Extract any text inputs/variables...
        element.variablesInHtml = self.parseVariablesInHTML(element.html)

        element.assetsInHtml = self.parseAssetsInHTML(element.html)
        
        // Extract any textarea options detected in the html...
        element.detectedTextAreas = self.parseTextAreaInHTML(element.html)

        // Extract options for any "tagline" options...
        element.detectedTaglines = self.parseTaglinesOptionInHTML(element.html)

        // Extract options for any "toggle" options...
        element.detectedToggles = self.parseTogglesInHTML(element.html)
    }

    async function formatDesignResponse(adminQuery, element, includeHTML = false) {

        // Parse tags if they exist & add any metadata to design...
        // Add thumbnail images
        const thumbnail = `${element.id}-thumbnail.jpg`;
        const draft = `${element.id}-draft.jpg`;

        const thumbnailUrl = thumbnailsS3Dir + thumbnail
        const clubThumbnailUrl = thumbnailsS3Dir + `/${thumbnail}`
        const draftlUrl = thumbnailsS3Dir + draft
        const clubDraftlUrl = thumbnailsS3Dir + `/${draft}`

        let isPublished = true;
        if(
            element.publishingAutomation !== undefined && 
            element.publishingAutomationPublishNow !== undefined && 
            element.publishingAutomationNeverExpire !== undefined
        ) {
            
            let autoPublish = (element.publishingAutomation.autoPublish === undefined) ? moment().subtract(1, 'second').unix() : element.publishingAutomation.autoPublish
            let autoUnPublish = (element.publishingAutomation.autoUnPublish === undefined) ? moment().add(1, 'second').unix() : element.publishingAutomation.autoUnPublish

            // Auto-publish date is before the current time - design should not yet be published
            if(element.publishingAutomationPublishNow !== true && !moment(autoPublish).isBefore(moment().unix())) isPublished = false;

            // Auto-unpublish date is after the current time - design should also be unpublished..
            if(element.publishingAutomationNeverExpire !== true && !moment(autoUnPublish).isAfter(moment().unix())) isPublished = false;
        }


        // Parse tags if they exist & add any metadata to design...
        parseDesign(element);

        return Promise.all([

            getDesignThumbnail(clubThumbnailUrl, thumbnailUrl), 
            getDesignDraft(clubDraftlUrl, draftlUrl)

        ]).then(values => {

            // Design should not yet be published ... Return without pushing into templates[]
            if(!adminQuery && !isPublished) {
                return;
            } else {
                element.isPublished = isPublished;
            }

            element.thumbnail = values[0]
            element.draft = values[1]

            // delete element.club;
            if(!includeHTML) delete element.html;

            var designBrand = (element.brand === undefined) ? "ALL" : element.brand
            if(element.brand === undefined) element.brand = designBrand;

            // Push if brand matches...
            if(!adminQuery) {
                switch(clubBrand) {
                    case "12rnd":
                        if(element.brand.toLowerCase() == "12rnd" || element.brand.toLowerCase() == "all") templates.push(element);
                        break;
                    case "ubx":
                        if(element.brand.toLowerCase() == "ubx" || element.brand.toLowerCase() == "all") templates.push(element);
                        break;
                    default:
                        if(element.brand.toLowerCase() == "all") templates.push(element);
                }
            } else {
                templates.push(element);
            }
            
            return element;
        })  
    }

    async function getDesignThumbnail(clubUrl, url) {
        try {
            const response = await fetch(clubUrl)
            
            if(response.status !== 200) {
                return url
            }
            return clubUrl
        } catch (error) {
            console.log('Error')
            return url;
        }
    }

    async function getDesignDraft(clubUrl, url) {
        try {
            const response = await fetch(clubUrl)
            
            if(response.status !== 200) {
                return url
            }

            return clubUrl
        } catch (error) {
            console.log('Error')
            return url;
        }
    }

}

module.exports.parseAssetsInHTML = function(element) {
    let detectedAssets
    try {
        // Matches simple tag variables such as: {{ASSET:}}
        detectedAssets = element.match(/{{ASSET:(.*?)}}/g).map(function(val){

           // Only return variable if it's not a "toggle" option...
           var variable = val.replace(/{{|}}/g,'');
           const asset = variable.split(":")
           
           return {
               id: asset[3],
               name: asset[1],
               tag: asset[2]
           }
        });
    } catch(e) {
        detectedAssets = [];
    }

    // remove null values from array...
    detectedAssets = detectedAssets.filter(Boolean);
    return detectedAssets;
}

module.exports.parseTextAreaInHTML = function(element) {
    let detectedTextAreas
    try {
        // Matches simple tag variables such as: {{ASSET:}}
        detectedTextAreas = element.match(/{{TEXTAREA:(.*?)}}/g).map(function(val){

           // Only return variable if it's not a "toggle" option...
           var variable = val.replace(/{{|}}/g,'');
           const asset = variable.split(":")

           // Returns the 'label name' of the input...
           return asset[1];
        });
    } catch(e) {
        detectedTextAreas = [];
    }

    // remove null values from array...
    detectedTextAreas = detectedTextAreas.filter(Boolean);
    return detectedTextAreas;
}



module.exports.parseTogglesInHTML = function(element) {
    try {
        // Matches toggles such as: {{#t=TagName}} CONTENT {{/#}}
        var toggles = element.match(/\{\{\#t=([^#]+)\{\{\/#\}\}/g).map(function(val){
           // Only return the "name" of the toggle .. not the actual HTML contents...
           return val.match("\{\{\#t=([^}]+)\}\}")[1];
        });
    } catch(e) {
        var toggles = [];
    }

    return toggles;
}

module.exports.parseTaglinesOptionInHTML = function(element) {
    try {
        // Matches toggles such as: {{#t=TagName}} CONTENT {{/#}}
        var taglines = element.match(/\{\{\#to([^#]+)\{\{\/#\}\}/g).map(function(val){

            // Remove all the breaks that might have inside taglines
            const inputHTML = val.match(/(?:<div>)([\s\S]*)(?:<\/div>)/)[0];
            const regexBr = new RegExp('<br>', 'gi');
            const outputHTML = inputHTML.replace(regexBr, '');

           // Return the actual HTML contents...
           return { name: val.match("\{\{\#to=([^}]+)\}\}")[1], value: outputHTML}
        });
        
    } catch(e) {
        var taglines = [];
    }
    
    return taglines;
}

module.exports.parseVariablesInHTML = function(element) {
    try {
        // Matches simple tag variables such as: {{VariableName}}
        var detectedVariables = element.match(/{{(.*?)}}/g).map(function(val){

           // Only return variable if it's not a "toggle" option and if its not an "assets" option
           var variable = val.replace(/{{|}}/g,'');
           
           if(
                (!variable.toLowerCase().startsWith("#t=") && variable.toLowerCase() !== "/#")
                && !variable.startsWith("ASSET")
                && !variable.startsWith("TEXTAREA")
                && !variable.toLowerCase().startsWith("#to")
            ) {
                return variable
            };
        });
    } catch(e) {
        var detectedVariables = [];
    }

    // remove null values from array...
    detectedVariables = detectedVariables.filter(Boolean);
    return detectedVariables;
}

function parseJson(json) {
    // Item is already json...
    if(typeof json === 'object') return json;

    try {
        return JSON.parse(json);
    } catch (e) {
        return json;
    }
}