'use strict';
const Router = require('express').Router;
const auth = require('../../shared-functions/auth');
const db = require('../../services/dynamodb/secureupload');

const S3_PRIVATE_BUCKET = process.env.S3_UPLOADS_BUCKET || 'ph-uploads-dev';
const S3_PUBLIC_BUCKET = process.env.S3_PUBLIC_BUCKET || 'content.gymsystems.co';

const PRIVATE_REGION = process.env.AWS_REGION || 'ap-southeast-2';
const PUBLIC_REGION = 'us-east-1';

/**
 * @swagger
 * /api/admin/secure-upload/files:
 *    post:
 *      description: Creates/updates a secure upload record
 *      tags:
 *        - Auth
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      parameters:
 *        - in: body
 *          name: body
 *          required: true
 *          schema:
 *            type: object
 *            properties:
 *              key:
 *                type: string
 *                description: The key of the file to convert including the directory
 *              name:
 *                type: string
 *                description: The name of the file
 *              size:
 *                type: integer
 *                description: The size of the file in bytes
 *              type:
 *                type: string
 *                description: The type of the file
 *              public:
 *                type: boolean
 *                description: Whether the file is public or not
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of backoffice users.
 */

module.exports = Router({ mergeParams: true }).post(
    '/admin/secure-upload/files',
    auth.checkModuleAccess('file-uploader'),
    async (req, res, next) => {
        const { key, name, size, type } = req.body;
        const isPublic = req.body.public === 'true';

        const bucket = isPublic ? S3_PUBLIC_BUCKET : S3_PRIVATE_BUCKET;
        const region = isPublic ? PUBLIC_REGION : PRIVATE_REGION;

        const params = { status: 'uploaded' };
        if (!key) return [400, { message: 'Missing key' }];
        const dbObjectRes = await db.get(key);
        const organisationId = auth.getOrganisationIdFromParams(req);

        if (name) params.name = name;
        if (dbObjectRes[0] !== 200) {
            params.created = new Date().getTime() / 1000;
            params.bucket = bucket;
            params.region = region;
            params.isPublic = isPublic;
            params.hideFromSearch = req.body.hideFromSearch === 'true';
            params.organisationId = organisationId || null;
            params.source = req.body.source || null;
            if (size) params.size = size;
            if (type) params.fileType = type;
            if (['video', 'audio'].includes(params.fileType)) {
                params.status = 'to process';
                params.progress = 0;
            }
        }

        const [status, response] = await db.update(key, params);
        res.status(status).send(response);
    }
);
