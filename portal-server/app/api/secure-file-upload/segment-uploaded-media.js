'use strict';
const Router = require('express').Router;
const auth = require('../../shared-functions/auth');
const { convertMediaToChunks } = require('../../services/mediaconvert/secure-upload-service');
const db = require('../../services/dynamodb/secureupload');
const s3 = require('../../services/s3/s3');

/**
 * @swagger
 * /api/admin/media-convert:
 *    post:
 *      description: Converts pending videos and audio into small chunks and places them in S3
 *      tags:
 *        - Auth
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of backoffice users.
 */

const self = (module.exports = Router({ mergeParams: true }).post(
    '/admin/media-convert',
    auth.checkGlobalAdminAccess,
    async (req, res, next) => {
        const [status, response] = await self.segmentUploadedMedia();
        res.status(status).send(response);
    }
));

module.exports.segmentUploadedMedia = async () => {
    const itemsRes = await db.query('status', { status: 'to process' });
    if (itemsRes[0] !== 200) return itemsRes;

    const updates = await Promise.all(
        itemsRes[1].map(async (object) => {
            const objectInfoRes = await s3.getObjectInfo(object.bucket, object.s3Key);
            if (objectInfoRes[0] !== 200) {
                await db.update(object.s3Key, { status: 'error', message: 'Error getting object info' });
                return objectInfoRes;
            }

            const jobRes = await convertMediaToChunks(object.s3Key, object.bucket);
            if (jobRes[0] !== 200) {
                await db.update(object.s3Key, { status: 'error', message: 'Error converting media' });
                return jobRes;
            }

            const updateRes = await db.update(object.s3Key, { status: 'processing', mediaConvertJobId: jobRes[1].Id });
            if (updateRes[0] !== 200) return updateRes;
            return jobRes;
        })
    );

    return [200, updates];
};
