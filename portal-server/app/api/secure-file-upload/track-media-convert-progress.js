'use strict';
const Router = require('express').Router;
const auth = require('../../shared-functions/auth');
const mc = require('../../services/mediaconvert/mediaconvert');
const db = require('../../services/dynamodb/secureupload');

/**
 * @swagger
 * /api/admin/media-convert/progress:
 *    put:
 *      description: Updates the progress of a file conversions
 *      tags:
 *        - Auth
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of backoffice users.
 */

const self = (module.exports = Router({ mergeParams: true }).put(
    '/admin/media-convert/progress',
    auth.checkGlobalAdminAccess,
    async (req, res, next) => {
        const [status, response] = await self.trackMediaConvertProgress();
        res.status(status).send(response);
    }
));

module.exports.trackMediaConvertProgress = async () => {
    const processingJobsRes = await db.query('status', { status: 'processing' });
    if (processingJobsRes[0] !== 200) return processingJobsRes;

    const updates = await Promise.all(
        processingJobsRes[1].map(async ({ s3Key, mediaConvertJobId }) => {
            const jobRes = await mc.checkJobStatus(mediaConvertJobId);
            if (jobRes[0] !== 200) return jobRes;

            const { Status: jobStatus, ErrorMessage: errMsg, JobPercentComplete: percentage } = jobRes[1];
            if (jobStatus === 'SUBMITTED') return [200, { message: 'Job still processing' }];

            const newStatus =
                jobStatus === 'PROGRESSING'
                    ? 'processing'
                    : jobStatus === 'COMPLETE'
                    ? 'processed'
                    : jobStatus.toLowerCase();

            const progressPercentage = jobStatus !== 'PROGRESSING' ? 100 : percentage || 0;
            const message = errMsg || '';
            return await db.update(s3Key, { status: newStatus, message, progress: progressPercentage });
        })
    );

    const hasSuccess = updates.some((update) => update[0] === 200);
    const hasError = updates.some((update) => update[0] !== 200);
    const status = hasError && hasSuccess ? 207 : hasError ? 500 : 200;
    return [status, updates];
};
