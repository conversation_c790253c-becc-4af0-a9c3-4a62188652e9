'use strict';
const Router = require('express').Router;
const auth = require('../../../shared-functions/auth');
const permissions = require('../../../services/dynamodb/accesspermissions');

/**
 * @swagger
 * /api/users/{userId}:
 *    delete:
 *      description: Remove a user from performance hub
 *      tags:
 *        - Admin
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      parameters:
 *        - name: userId
 *          description: The email of the user you wish to remove the role from.
 *          in: path
 *        - name: roleId
 *          description: The ID of the role you wish to remove from the user.
 *          in: path
 *      responses:
 *        200:
 *          description: Returns JSON response of the updated user roles.
 *        500:
 *          description: Internal error from the API - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this resource.
 */

module.exports = Router({ mergeParams: true }).delete(
    '/users/:userId',
    auth.checkGlobalAdminAccess,
    async (req, res, next) => {
        const { userId } = req.params;
        if (!userId) return res.status(400).json({ message: 'Missing email' });
        const [status, data] = await permissions.remove(userId);
        return res.status(status).json(data);
    }
);
