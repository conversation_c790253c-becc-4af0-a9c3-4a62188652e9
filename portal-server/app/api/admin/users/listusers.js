'use strict';
const Router = require('express').Router;
const auth = require('../../../shared-functions/auth');
const permissions = require('../../../services/dynamodb/accesspermissions');
const { listRoles } = require('../../../services/dynamodb/roles');
const { listOrganisations } = require('../../../services/dynamodb/organisations');
const { scan: listFacilities } = require('../../../services/dynamodb/club');

/**
 * @swagger
 * /api/admin/users/listusers:
 *    get:
 *      description: Lists all users (Google Apps Accounts) that have access to the backoffice system/api
 *      tags:
 *        - Admin
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of backoffice users.
 *        500:
 *          description: Internal error from the API - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this resource.
 */

module.exports = Router({ mergeParams: true }).get('/admin/users/listusers', async (req, res, next) => {
    const curUserEmail = auth.loggedUserAccount(req);
    const access = await auth.getUserOrgAccessForModule(curUserEmail, 'access-control');
    const orgIds = access.orgs.map((org) => org.organisationId);

    const [permissionsRes, orgsRes, rolesRes, facilityRes] = await Promise.all([
        permissions.list(),
        listOrganisations({ organisationId: orgIds }),
        listRoles(),
        listFacilities(),
    ]);

    if (permissionsRes[0] !== 200) return res.status(permissionsRes[0]).json(permissionsRes[1]);
    if (orgsRes[0] !== 200) return res.status(orgsRes[0]).json(orgsRes[1]);
    if (rolesRes[0] !== 200) return res.status(rolesRes[0]).json(rolesRes[1]);
    if (!facilityRes[0]) return res.status(500).json(facilityRes[1]);

    const users = permissionsRes[1].filter((user) => {
        const roles = user.roles || [];

        return (
            // hide users without permissions
            (user.isGlobalAdmin || roles.length > 0) &&
            (access.isGlobalAdmin ||
                roles.some(({ roleId }) => {
                    const role = rolesRes[1].find((r) => r.roleId === roleId);
                    return orgIds.includes(role?.organisationId);
                }))
        );
    });

    const formatted = users.map((user) => {
        const userRoles = user.roles.reduce((acc, userRole) => {
            const roleData = rolesRes[1].find((r) => r.roleId === userRole.roleId);
            if (!roleData) return acc;

            const orgData = orgsRes[1].find((org) => org.organisationId === roleData.organisationId);
            if (!orgData) return acc;

            const roleFacilities = facilityRes[1].filter((f) => {
                return roleData.accessScope === 'template'
                    ? userRole.facilities?.includes(f.id)
                    : roleData.accessScope === 'facilities'
                    ? roleData.facilities?.includes(f.id)
                    : false;
            });

            return [
                ...acc,
                {
                    roleId: userRole.roleId,
                    name: roleData.name,
                    organisationId: orgData.organisationId,
                    organisationName: orgData.name,
                    facilities: roleFacilities.map((f) => ({ id: f.id, name: f.name })),
                    regions:
                        roleData.accessScope === 'template'
                            ? userRole.regions
                            : roleData.accessScope === 'regions'
                            ? roleData.regions
                            : [],
                },
            ];
        }, []);

        return { ...user, roles: userRoles };
    });

    return res.status(200).json(formatted);
});
