'use strict';
const Router = require('express').Router;
const uuidv4 = require('uuid').v4;
const modules = require('../../../data/modules');
const { getBrand, createBrand } = require('../../../services/dynamodb/brands');
const { scan: listFacilities, update } = require('../../../services/dynamodb/club');
const { upsertOrganisation, getOrganisation } = require('../../../services/dynamodb/organisations');
const { list: listUsers, upsert } = require('../../../services/dynamodb/accesspermissions');
const { createRole } = require('../../../services/dynamodb/roles');
const auth = require('../../../shared-functions/auth');
const ddb = require('../../../services/dynamodb/dynamodb');
const {
    customScan: listClubUserAgreements,
    update: updateClubUserAgreement,
} = require('../../../services/dynamodb/clubuseragreement');
const { listAllUnCategorizedUploads, update: updateUpload } = require('../../../services/dynamodb/secureupload');
const { fetchClubReviewsSettings, updateClubReviewsSettings } = require('../../../services/dynamodb/reviewsettings');

const DEFAULT_ORGANISATION_ID = 'c7d7e6cc-99e5-4939-9956-bb5e32a42418';
const DEFAULT_BRAND_ID = '62104fed-edea-4b6f-8b89-d0e24a6e38fd';

/**
 * @swagger
 * /api/admin/tasks/role-migrations:
 *    get:
 *      description: Add default ids, objects, etc for the new role integration.
 *      tags:
 *        - Admin (Tasks)
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of status of task.
 *        500:
 *          description: Internal error from the API - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this resource.
 */

module.exports = Router({ mergeParams: true }).get(
    '/admin/tasks/role-migrations',
    auth.checkGlobalAdminAccess,
    async (req, res, next) => {
        console.info('Adding default organisation and modules...');
        const responses = await Promise.all([
            addDefaultOrganisation(),
            addDefaultBrand(),
            addDefaultBrandToFacilities(),
            addDefaultRoles(),
            addOrgIdToDesigns(),
            addOrgIdToAgreements(),
            addOrgIdsToSecureUploads(),
            addOrgIdsToAnnouncements(),
            addOrgIdToReviewSettings(),
            addSlugToFacilities(),
        ]);
        res.status(200).send(responses);
    }
);

async function addDefaultOrganisation() {
    const orgRes = await getOrganisation(DEFAULT_ORGANISATION_ID);
    if (orgRes[0] === 200) {
        console.info(`Organisation already exists`);
        return [200, { message: 'Organisation already exists' }];
    }

    const [status] = await upsertOrganisation({
        organisationId: DEFAULT_ORGANISATION_ID,
        name: 'UBX Global Limited',
        modules: modules.map(({ moduleId }) => moduleId),
    });

    if (status !== 200) {
        console.error(`Error creating organisation: ${status}`);
        return [status, { message: 'Error creating organisation' }];
    }

    console.info(`Organisation created successfully`);
    return [status, { message: 'Organisation created successfully' }];
}

async function addDefaultBrand() {
    const brandRes = await getBrand(DEFAULT_BRAND_ID);
    if (brandRes[0] === 200) {
        console.info(`Brand already exists`);
        return [200, { message: 'Brand already exists' }];
    }

    const [status] = await createBrand({
        organisationId: DEFAULT_ORGANISATION_ID,
        brandId: DEFAULT_BRAND_ID,
        name: 'UBX',
    });

    if (status !== 201) {
        console.error(`Error creating brand: ${status}`);
        return [status, { message: 'Error creating brand' }];
    }

    console.info(`Brand created successfully`);
    return [status, { message: 'Brand created successfully' }];
}

async function addDefaultBrandToFacilities() {
    const facilities = await listFacilities({}, {}, 'AND attribute_not_exists(brandId)');
    if (!facilities[0]) {
        console.error(`Error listing facilities`);
        return [500, facilities[1]];
    }

    if (facilities[1].length === 0) {
        console.info(`No facilities to update`);
        return [200, { message: 'No facilities to update' }];
    }

    const responses = await Promise.all(
        facilities[1].map(async (facility) => {
            const { id } = facility;
            return update({ id, brandId: DEFAULT_BRAND_ID });
        })
    );

    const failed = responses.filter((response) => !response[0]);
    if (failed.length) {
        console.error(`Error updating facilities: ${failed.map((r) => r[1].id)}`);
        return [500, failed.map((r) => r[1].id)];
    }

    console.info(`Facilities updated successfully`);
    return [200, { message: 'Facilities updated successfully' }];
}

async function addDefaultRoles() {
    const users = await listUsers({}, { format: false });
    if (!users[0]) {
        console.error(`Error listing users`);
        return [500, users[1]];
    }

    const roles = users[1].reduce((acc, user) => {
        if (user.roles || user.isGlobalAdmin) return acc;

        const role = user.permissions || 'manager';
        const clubs = parseClubs(user.clubs || []);
        const regions = role !== 'pseudo-admin' ? [] : clubs.length ? [] : user.regions || [];
        const adminPages = role === 'pseudo-admin' ? user.adminPages || [] : [];

        // managers and owners will have their access scope set to custom
        const key = ['manager', 'restricted-manager', 'owner'].includes(role)
            ? JSON.stringify({ role, clubs: [], regions: [], adminPages: [] })
            : JSON.stringify({
                  role,
                  clubs: clubs.sort((a, b) => a.localeCompare(b)),
                  regions: regions.sort((a, b) => a.localeCompare(b)),
                  adminPages: adminPages.sort((a, b) => a.localeCompare(b)),
              });

        acc[key] = acc[key] || [];
        acc[key].push(user.id);
        return acc;
    }, {});

    const adminPagesToModules = {
        monitoring: 'monitoring-page',
        ticketing: 'ticketing-system',
        'user-access': 'access-control',
        'charges-fees': 'charges-config',
        'member-management': 'membership-management',
        'secure-uploader': 'file-uploader',
        wiki: 'wiki-admin',
        store: 'store-admin',
    };

    const basicModules = [
        'store',
        'wiki',
        'project-management',
        'designer',
        'global-calendar',
        'training-camp',
        'free-trials',
        'member-payments',
        'member-agreements',
        'club-details',
        'google-analytics',
        'review-management',
        'coaching-screens',
        'cctv',
        'audio-control',
        'business-profile',
        'support',
    ];

    const names = {};
    const failedRoles = [];
    const failedUsers = [];
    await Promise.all(
        Object.entries(roles).map(async ([key, roleUsers]) => {
            const { role, clubs, regions, adminPages } = JSON.parse(key);

            const nameCount = (names[role] || 0) + 1;
            names[role] = nameCount;

            if (role === 'admin') {
                return Promise.all(
                    roleUsers.map(async (userId) => {
                        const doc = { email: userId, isGlobalAdmin: true, roles: [] };
                        const result = await upsert(doc);
                        if (result[0] === 200) return;
                        failedUsers.push(doc);
                    })
                );
            }

            const params = {
                roleId: uuidv4(),
                organisationId: DEFAULT_ORGANISATION_ID,
                name: `${role} #${nameCount}`,
                moduleMode: 'manual',
                modules:
                    role === 'manager'
                        ? basicModules
                        : role === 'restricted-manager'
                        ? [...basicModules, 'billing']
                        : role === 'owner'
                        ? [
                              ...basicModules,
                              'club-variables',
                              'external-integrations',
                              'agreements',
                              'billing',
                              'announcements',
                          ]
                        : role === 'pseudo-admin'
                        ? [...basicModules, ...adminPages.map((page) => adminPagesToModules[page] || page)]
                        : [],
                accessScope: ['manager', 'owner', 'restricted-manager'].includes(role)
                    ? 'template'
                    : clubs.length
                    ? 'facilities'
                    : 'regions',
                regions,
                facilities: clubs,
            };

            const roleRes = await createRole(params);
            if (roleRes[0] !== 201) {
                console.error(`Error creating role: ${roleRes[1]}`);
                failedRoles.push(params);
                return;
            }

            await Promise.all(
                roleUsers.map(async (userId) => {
                    const user = users[1].find((u) => u.id === userId);

                    const facilities = params.accessScope === 'template' ? parseClubs(user.clubs) : [];
                    const roleData = { roleId: params.roleId, facilities };

                    const doc = { email: userId, roles: [roleData] };
                    const [status] = await upsert(doc);
                    if (status === 200) return;
                    failedUsers.push(doc);
                })
            );
        })
    );

    if (failedRoles.length || failedUsers.length) {
        console.error(`Error creating roles or users: ${failedRoles.length} roles, ${failedUsers.length} users`);
        return [500, { failedRoles, failedUsers }];
    }

    console.info(`Roles and users created successfully`);
    return [200, { message: 'Roles and users created successfully' }];
}

const parseClubs = (clubs) => {
    if (typeof clubs === 'string') {
        try {
            return JSON.parse(clubs);
        } catch (error) {
            console.error(`Error parsing clubs: ${error}`);
            return [];
        }
    }
    return clubs;
};

async function addOrgIdToDesigns() {
    try {
        const DB_DESIGN_STATIC_TABLE = process.env.DB_DESIGN_STATIC_TABLE || 'dph-design-static-templates-dev';
        const DB_DESIGN_TEMPLATES_TABLE = process.env.DB_DESIGN_TEMPLATES_TABLE || 'ph-design-templates-dev';

        const filterParams = {
            FilterExpression: 'attribute_not_exists(organisationId)',
            ProjectionExpression: 'id',
        };

        const [{ Items: staticDesigns }, { Items: templates }] = await Promise.all([
            ddb.retrieveItems({ TableName: DB_DESIGN_STATIC_TABLE, ...filterParams }),
            ddb.retrieveItems({ TableName: DB_DESIGN_TEMPLATES_TABLE, ...filterParams }),
        ]);

        if (!staticDesigns.length && !templates.length) {
            console.info(`No designs to update`);
            return [200, { message: 'No designs to update' }];
        }

        const failedStaticDesigns = [];
        const failedTemplates = [];

        await Promise.all([
            ...staticDesigns.map(async (design) => {
                const { id } = design;
                try {
                    const updateParams = {
                        TableName: DB_DESIGN_STATIC_TABLE,
                        Key: { id: null },
                        UpdateExpression: 'SET organisationId = :orgId',
                        ExpressionAttributeValues: { ':orgId': DEFAULT_ORGANISATION_ID },
                    };

                    await ddb.updateItem({ ...updateParams, Key: { id } });
                } catch (error) {
                    console.error(`Error updating static design: ${error}`);
                    failedStaticDesigns.push(id);
                }
            }),
            ...templates.map(async (design) => {
                const { id } = design;
                try {
                    const updateParams = {
                        TableName: DB_DESIGN_TEMPLATES_TABLE,
                        Key: { id: null },
                        UpdateExpression: 'SET organisationId = :orgId',
                        ExpressionAttributeValues: { ':orgId': DEFAULT_ORGANISATION_ID },
                    };

                    await ddb.updateItem({ ...updateParams, Key: { id } });
                } catch (error) {
                    console.error(`Error updating template: ${error}`);
                    failedTemplates.push(id);
                }
            }),
        ]);

        if (failedStaticDesigns.length || failedTemplates.length) {
            console.error(
                `Error updating designs: ${failedStaticDesigns.length} static designs, ${failedTemplates.length} templates`
            );
            return [500, { failedStaticDesigns, failedTemplates }];
        }

        return [200, { message: 'Designs updated successfully' }];
    } catch (error) {
        console.error(`Error retrieving designs: ${error}`);
        return [500, { message: 'Error retrieving designs' }];
    }
}

async function addOrgIdToAgreements() {
    try {
        const DB_AGREEMENTS_TABLE = process.env.DB_AGREEMENTS_TABLE || 'ph-agreements';

        const cuQueryParams = {
            FilterExpression: 'attribute_not_exists(organisationIds)',
            ProjectionExpression: 'id',
        };

        const agQueryParams = {
            FilterExpression: 'attribute_not_exists(organisationId)',
            ProjectionExpression: 'agreementid',
        };

        const [clubUserAgreements, { Items: phAgreements }] = await Promise.all([
            listClubUserAgreements(cuQueryParams),
            ddb.retrieveItems({ TableName: DB_AGREEMENTS_TABLE, ...agQueryParams }),
        ]);

        if (!clubUserAgreements.length && !phAgreements.length) {
            console.info(`No agreements to update`);
            return [200, { message: 'No agreements to update' }];
        }

        const failedAgreements = [];
        const failedClubUserAgreements = [];
        await Promise.all([
            ...clubUserAgreements.map(async (agreement) => {
                const { id } = agreement;

                try {
                    await updateClubUserAgreement({ id, organisationIds: [DEFAULT_ORGANISATION_ID] });
                } catch (error) {
                    console.error(`Error updating club user agreement: ${error}`);
                    failedClubUserAgreements.push(id);
                }
            }),
            ...phAgreements.map(async (agreement) => {
                const { agreementid } = agreement;
                try {
                    const updateParams = {
                        TableName: DB_AGREEMENTS_TABLE,
                        Key: { agreementid: null },
                        UpdateExpression: 'SET organisationId = :orgId',
                        ExpressionAttributeValues: { ':orgId': DEFAULT_ORGANISATION_ID },
                    };

                    await ddb.updateItem({ ...updateParams, Key: { agreementid } });
                } catch (error) {
                    console.error(`Error updating PH agreement: ${error}`);
                    failedAgreements.push(agreementid);
                }
            }),
        ]);

        if (failedAgreements.length || failedClubUserAgreements.length) {
            console.error(
                `Error updating agreements: ${failedAgreements.length} PH agreements, ${failedClubUserAgreements.length} club user agreements`
            );
            return [500, { failedAgreements, failedClubUserAgreements }];
        }

        console.info(`Agreements updated successfully`);
        return [200, { message: 'Agreements updated successfully' }];
    } catch (error) {
        console.error(`Error retrieving agreements: ${error}`);
        return [500, { message: 'Error retrieving agreements' }];
    }
}

async function addOrgIdsToSecureUploads() {
    const files = await listAllUnCategorizedUploads();
    if (files[0] !== 200) {
        console.error(`Error listing files`);
        return [500, files[1]];
    }

    if (files[1].length === 0) {
        console.info(`No files to update`);
        return [200, { message: 'No files to update' }];
    }

    const failedFiles = [];
    await Promise.all(
        files[1].map(async (file) => {
            const { s3Key } = file;
            const result = await updateUpload(s3Key, { organisationId: DEFAULT_ORGANISATION_ID });
            if (result[0] !== 200) {
                console.error(`Error updating file: ${result[1]}`);
                failedFiles.push(s3Key);
            }
        })
    );

    if (failedFiles.length) {
        console.error(`Error updating files: ${failedFiles}`);
        return [500, { failedFiles }];
    }

    console.info(`Files updated successfully`);
    return [200, { message: 'Files updated successfully' }];
}

async function addOrgIdsToAnnouncements() {
    try {
        const DB_ANNOUNCEMENTS_TABLE = process.env.DB_ANNOUNCEMENTS_TABLE || 'ph-announcements';
        const filterParams = {
            FilterExpression: 'attribute_not_exists(organisationIds)',
            ProjectionExpression: 'id',
        };

        const announcements = await ddb.retrieveItems({
            TableName: DB_ANNOUNCEMENTS_TABLE,
            ...filterParams,
        });

        if (!announcements.Items || announcements.Items.length === 0) {
            console.info(`No announcements to update`);
            return [200, { message: 'No announcements to update' }];
        }

        const failedAnnouncements = [];

        await Promise.all(
            announcements.Items.map(async (announcement) => {
                const { id } = announcement;
                try {
                    const updateParams = {
                        TableName: DB_ANNOUNCEMENTS_TABLE,
                        Key: { id: null },
                        UpdateExpression: 'SET organisationIds = :orgIds',
                        ExpressionAttributeValues: { ':orgIds': [DEFAULT_ORGANISATION_ID] },
                    };

                    await ddb.updateItem({ ...updateParams, Key: { id } });
                } catch (error) {
                    console.error(`Error updating announcement: ${error}`);
                    failedAnnouncements.push(id);
                }
            })
        );

        if (failedAnnouncements.length) {
            console.error(`Error updating announcements: ${failedAnnouncements}`);
            return [500, { failedAnnouncements }];
        }

        console.info(`Announcements updated successfully`);
        return [200, { message: 'Announcements updated successfully' }];
    } catch (error) {
        console.error(`Error retrieving announcements: ${error}`);
        return [500, { message: 'Error retrieving announcements' }];
    }
}

async function addOrgIdToReviewSettings() {
    try {
        const [globalSettings, orgSettings] = await Promise.all([
            fetchClubReviewsSettings('Global'),
            fetchClubReviewsSettings(DEFAULT_ORGANISATION_ID),
        ]);

        if (orgSettings.cannedResponses?.length > 0) return [200, { message: 'Review settings already exist' }];
        if (globalSettings.cannedResponses?.length < 1) return [200, { message: 'No global canned responses' }];

        await updateClubReviewsSettings(DEFAULT_ORGANISATION_ID, {
            cannedResponses: globalSettings.cannedResponses,
        });

        return [200, { message: 'Review settings updated successfully' }];
    } catch (error) {
        console.error(`Error retrieving review settings: ${error}`);
        return [500, { message: 'Error retrieving review settings' }];
    }
}

async function addSlugToFacilities() {
    const facilities = await listFacilities({}, {}, 'AND attribute_not_exists(slug)');
    if (!facilities[0]) {
        console.error(`Error listing facilities`);
        return [500, facilities[1]];
    }

    if (facilities[1].length === 0) {
        console.info(`No slugs to update`);
        return [200, { message: 'No slugs to update' }];
    }

    const responses = await Promise.all(
        facilities[1].map(async (facility) => {
            const { id } = facility;
            const slug = (id)
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-|-$/g, '')
                .substring(0, 50);
            return update({ id, slug });
        })
    );

    const failed = responses.filter((response) => !response[0]);
    if (failed.length) {
        return [500, failed.map((r) => r[1].id)];
    }

    console.info(`Slugs updated successfully`);
    return [200, { message: 'Slugs updated successfully' }];
}
