const Router = require('express').Router;

const auth = require('../../shared-functions/auth');
const storeAPI = require('../../services/store/storeAPI');
const clubdata = require('../clubs/clubpage/clubdata');

/**
 * @swagger
 * /api/store/clubs/{clubID}/products:
 *    get:
 *      description: Returns a list of products available to the club.
 *      tags:
 *        - Store
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *      produces:
 *        - application/json
 *      parameters:
 *        - name: clubID
 *          in: path
 *          type: string
 *          required: true
 *        - name: search
 *          in: query
 *          type: string
 *        - name: limit
 *          in: query
 *          type: integer
 *      responses:
 *        200:
 *          description: Returns true/false Json response if the location was successfully created on yext.
 *        500:
 *          description: Internal error from the API - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this resource.
 */

const self = (module.exports = Router({ mergeParams: true }).get(
    '/store/clubs/:club/products',
    auth.checkFacilityAccess,
    async (req, res) => {
        const { club } = req.params;
        const { search, limit } = req.query;

        const [status, data] = await self.listStoreProducts(club, { search, limit });
        res.status(status).send(data);
    }
));

module.exports.listStoreProducts = async (clubID, query = {}) => {
    const club = await clubdata.getPromise(clubID, true);
    if (!club) return [404, { message: 'Club not found.' }];

    try {
        const country = club.placeData?.country?.iso_code || 'GLOBAL';

        let queryString = `?customerId=${clubID}&country=${country}&limit=${query.limit || 3}`;
        if (query.search) queryString += `&searchTerm=${query.search}`;

        const response = await storeAPI({ method: 'GET', url: `/products${queryString}` });
        return [200, response.data];
    } catch (error) {
        console.error('Failed to list store products: ', error?.response?.data ?? error);
        return [500, { message: 'Something went wrong.' }];
    }
};
