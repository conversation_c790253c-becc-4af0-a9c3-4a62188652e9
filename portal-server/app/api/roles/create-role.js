const Router = require('express').Router;
const { listBrandsByOrganisation } = require('../../services/dynamodb/brands');
const { getOrganisation } = require('../../services/dynamodb/organisations');
const { createRole } = require('../../services/dynamodb/roles');
const { checkModuleAccess } = require('../../shared-functions/auth');
const { returnClubs } = require('../clubs/listclubs');

/** @swagger
 * /api/roles:
 *    post:
 *      description: Creates a new role
 *      tags:
 *        - Roles
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      parameters:
 *        - name: body
 *          description: Role object
 *          in: body
 *          required: true
 *          schema:
 *            allOf:
 *              - $ref: '#/definitions/roleSchema'
 *            required:
 *              - name
 *              - organisationId
 *              - moduleMode
 *              - modules
 *              - accessScope
 *              - regions
 *              - facilities
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of role.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

/** @swagger
 * definitions:
 *   roleSchema:
 *     type: object
 *     properties:
 *       name:
 *         type: string
 *         description: Name of the role
 *       organisationId:
 *         type: string
 *         description: ID of the organisation
 *       moduleMode:
 *         type: string
 *         description: Determines if the modules added are allowed or excluded
 *         enum: [manual, allowAllExcept]
 *       modules:
 *         type: array
 *         items:
 *           type: string
 *           description: List of modules
 *       accessScope:
 *         type: string
 *         description: Access scope of the role
 *         enum: [regions, facilities, organisation]
 *       regions:
 *         type: array
 *         items:
 *           type: string
 *           description: List of country codes
 *       facilities:
 *         type: array
 *         items:
 *           type: string
 *           description: List of facility IDs
 *       isOrganisationAdmin:
 *         type: boolean
 *         description: Grants access to yext integration logs and other admin features
 *         default: false
 */

const self = (module.exports = Router({ mergeParams: true }).post(
    '/roles',
    checkModuleAccess('access-control'),
    async (req, res) => {
        const attrs = {
            name: req.body.name,
            organisationId: req.body.organisationId,
            moduleMode: req.body.moduleMode,
            modules: req.body.modules || [],
            accessScope: req.body.accessScope,
            regions: req.body.regions || [],
            facilities: req.body.facilities || [],
            isOrganisationAdmin: req.body.isOrganisationAdmin || false,
        };

        const validate = await self.validateRole(attrs);
        if (validate[0] !== 200) return res.status(validate[0]).json(validate[1]);

        // Additional validation for "Create Role"
        if (!attrs.name) return res.status(400).json({ message: 'Role name is required' });

        const [status, data] = await createRole(validate[1]);
        return res.status(status).json(data);
    }
));

module.exports.validateRole = async (attrs = {}) => {
    if (!attrs.organisationId) return [400, { message: 'Organisation ID is required' }];

    const organisationRes = await getOrganisation(attrs.organisationId);
    if (organisationRes[0] !== 200) return organisationRes;

    if (attrs.moduleMode && !['manual', 'allowAllExcept'].includes(attrs.moduleMode)) {
        return [400, { message: 'Invalid module mode' }];
    }

    // validate if the modules added, the organisation is allowed to use
    if (attrs.modules && attrs.modules.some((module) => !organisationRes[1].modules.includes(module))) {
        return [400, { message: 'Invalid modules for this organisation' }];
    }

    if (!attrs.accessScope) return [200, attrs];

    const [brandsRes, facilities] = await Promise.all([
        listBrandsByOrganisation(attrs.organisationId),
        new Promise((resolve) => returnClubs(false, resolve)),
    ]);

    if (brandsRes[0] !== 200) return brandsRes;
    if (!facilities) return res.status(500).json({ message: 'Error fetching facilities' });

    if (attrs.accessScope !== 'facilities') attrs.facilities = [];
    if (attrs.accessScope !== 'regions') attrs.regions = [];

    if (attrs.accessScope === 'facilities' && attrs.facilities) {
        const invalidFacilities = attrs.facilities.filter((facility) => !facilities.some((f) => f.id === facility));
        if (invalidFacilities.length > 0) {
            return [400, { message: `Invalid facilities for this organisation: ${invalidFacilities.join(', ')}` }];
        }
    }

    return [200, attrs];
};
