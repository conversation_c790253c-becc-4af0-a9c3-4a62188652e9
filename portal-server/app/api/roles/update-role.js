const Router = require('express').Router;
const { updateRole } = require('../../services/dynamodb/roles');
const { checkModuleAccess } = require('../../shared-functions/auth');
const { validateRole } = require('./create-role');

/** @swagger
 * /api/roles/{roleId}:
 *    put:
 *      description: Updates an role by ID
 *      tags:
 *        - Roles
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      parameters:
 *        - name: body
 *          description: Role object
 *          in: body
 *          required: true
 *          schema:
 *            allOf:
 *              - $ref: '#/definitions/roleSchema'
 *            required:
 *              - organisationId
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of role.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

module.exports = Router({ mergeParams: true }).put(
    '/roles/:roleId',
    checkModuleAccess('access-control'),
    async (req, res) => {
        const { roleId } = req.params;
        const attrs = {
            organisationId: req.body.organisationId,
            name: req.body.name,
            moduleMode: req.body.moduleMode,
            modules: req.body.modules || [],
            accessScope: req.body.accessScope,
            regions: req.body.regions || [],
            facilities: req.body.facilities || [],
            isOrganisationAdmin: req.body.isOrganisationAdmin || false,
        };

        const validate = await validateRole(attrs);
        if (validate[0] !== 200) return res.status(validate[0]).json(validate[1]);

        const [status, data] = await updateRole(roleId, validate[1]);
        return res.status(status).json(data);
    }
);
