const Router = require('express').Router;
const { list: listAllUsers } = require('../../services/dynamodb/accesspermissions');
const { deleteRole, getRole } = require('../../services/dynamodb/roles');
const { loggedUserAccount, getUserOrgAccessForModule } = require('../../shared-functions/auth');

/** @swagger
 * /api/roles/{roleId}:
 *    delete:
 *      description: Deletes an role by ID
 *      tags:
 *        - Roles
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of role.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

module.exports = Router({ mergeParams: true }).delete('/roles/:roleId', async (req, res) => {
    const { roleId } = req.params;
    const email = loggedUserAccount(req);

    const [role, access] = await Promise.all([getRole(roleId), getUserOrgAccessForModule(email, 'access-control')]);

    if (role[0] !== 200) return res.status(role[0]).json(role[1]);
    if (!access.orgs.some((org) => org.organisationId === role[1].organisationId)) {
        return res.status(403).json({ message: 'You do not have permission to delete this role.' });
    }

    const users = await listAllUsers({});
    if (users[0] !== 200) return res.status(users[0]).json(users[1]);

    const usersWithRole = users[1].filter((user) => user.roles.some((role) => role.roleId === roleId));

    if (usersWithRole.length > 0) {
        return res
            .status(400)
            .json({ message: `Cannot delete role as it is assigned to ${usersWithRole.length} user(s).` });
    }

    const [status, data] = await deleteRole(roleId);
    return res.status(status).json(data);
});
