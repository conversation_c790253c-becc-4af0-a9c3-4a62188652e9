const Router = require('express').Router;
const { updateBrand } = require('../../services/dynamodb/brands');
const { checkModuleAccess } = require('../../shared-functions/auth');

/** @swagger
 * /api/brands/{brandId}:
 *    put:
 *      description: Updates an brand by ID
 *      tags:
 *        - Brands
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      parameters:
 *        - name: brandId
 *          in: path
 *          description: The ID of the brand to update
 *        - name: name
 *          in: body
 *          description: The name of the brand
 *          required: true
 *        - name: organisationId
 *          in: body
 *          description: The ID of the organisation the brand belongs to
 *        - name: redirectDashboard
 *          in: body
 *          description: The URL to redirect to after logging in instead of the default dashboard
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all currencies.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

module.exports = Router({ mergeParams: true }).put(
    '/brands/:brandId',
    checkModuleAccess('access-control'),
    async (req, res) => {
        const { brandId } = req.params;
        const {
            name,
            organisationId,
            redirectDashboard,
            displayUIOptions,
            displayDataSources,
        } = req.body;
        const payload = {
            name,
            organisationId,
            redirectDashboard,
            displayUIOptions,
            displayDataSources,
        };
        const [status, data] = await updateBrand(brandId, payload);
        return res.status(status).json(data);
    }
);
