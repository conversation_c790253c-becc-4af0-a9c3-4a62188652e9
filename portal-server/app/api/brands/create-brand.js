const Router = require('express').Router;
const { createBrand } = require('../../services/dynamodb/brands');
const { checkModuleAccess } = require('../../shared-functions/auth');

/** @swagger
 * /api/brands:
 *    post:
 *      description: Creates a new brand
 *      tags:
 *        - Brands
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      parameters:
 *        - name: name
 *          in: body
 *          description: The name of the brand
 *          required: true
 *        - name: organisationId
 *          in: body
 *          description: The ID of the organisation the brand belongs to
 *          required: true
 *        - name: redirectDashboard
 *          in: body
 *          description: The URL to redirect to after logging in instead of the default dashboard
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all currencies.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

module.exports = Router({ mergeParams: true }).post(
    '/brands',
    checkModuleAccess('access-control'),
    async (req, res) => {
        // Destructure known fields
        const {
            name,
            organisationId,
            redirectDashboard,
            displayUIOptions = [],
            displayDataSources = [],
        } = req.body;
        const payload = {
            name,
            organisationId,
            redirectDashboard,
            displayUIOptions,
            displayDataSources,
        };
        const [status, data] = await createBrand(payload);
        return res.status(status).json(data);
    }
);
