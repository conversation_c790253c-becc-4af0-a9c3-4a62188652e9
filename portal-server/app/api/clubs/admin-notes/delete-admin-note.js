'use strict';
const Router = require('express').Router;

const auth = require('../../../shared-functions/auth');
const { get, update } = require('../../../services/dynamodb/club');

/**
 * @swagger
 * /api/facilities/{facilityId}/admin-notes/{noteId}:
 *    delete:
 *      description: Delete an admin note from a facility.
 *      tags:
 *        - Admin
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      parameters:
 *        - name: facilityId
 *          description: Internal ID of the facility that you wish to access.
 *          in: path
 *          required: true
 *        - name: noteId
 *          description: ID of the admin note to delete.
 *          in: path
 *          required: true
 *      responses:
 *        200:
 *          description: Returns JSON response of the updated user roles.
 *        500:
 *          description: Internal error from the API - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this resource.
 */

module.exports = Router({ mergeParams: true }).delete(
    '/facilities/:facilityId/admin-notes/:noteId',
    auth.checkGlobalAdminAccess,
    async (req, res, next) => {
        const { facilityId, noteId } = req.params;

        const facility = await get(facilityId);
        if (!facility[0]) {
            return [404, { message: 'Facility not found' }];
        }

        const notes = facility[1].adminNotes || [];
        const updatedNotes = notes.filter((note) => note.noteId !== noteId);

        const [ok] = await update({ id: facilityId, adminNotes: updatedNotes });
        if (!ok) return res.status(500).json({ message: 'Failed to delete admin note' });
        return res.status(200).json({ message: 'Admin note deleted successfully' });
    }
);
