'use strict';
const Router = require('express').Router;

const auth = require('../../../shared-functions/auth');
const { get: getFacility } = require('../../../services/dynamodb/club');

/**
 * @swagger
 * /api/facilities/{facilityId}/admin-notes:
 *    get:
 *      description: List all admin notes for a facility.
 *      tags:
 *        - Admin
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      parameters:
 *        - name: facilityId
 *          description: Internal ID of the facility that you wish to access.
 *          in: path
 *          required: true
 *        - name: body
 *          in: body
 *          schema:
 *            type: object
 *            properties:
 *              noteId:
 *                type: string
 *              content:
 *                type: string
 *      responses:
 *        200:
 *          description: Returns JSON response of the updated user roles.
 *        500:
 *          description: Internal error from the API - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this resource.
 */

module.exports = Router({ mergeParams: true }).get(
    '/facilities/:facilityId/admin-notes',
    auth.checkGlobalAdminAccess,
    async (req, res, next) => {
        const { facilityId } = req.params;
        const [success, data] = await getFacility(facilityId);
        return res.status(success ? 200 : 500).json(data.adminNotes || []);
    }
);
