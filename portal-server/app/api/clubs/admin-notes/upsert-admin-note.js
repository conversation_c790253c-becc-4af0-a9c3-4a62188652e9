'use strict';
const Router = require('express').Router;
const uuidV4 = require('uuid').v4;
const moment = require('moment');

const auth = require('../../../shared-functions/auth');
const { upsertAdminNote } = require('../../../services/dynamodb/club');

/**
 * @swagger
 * /api/facilities/{facilityId}/admin-notes:
 *    put:
 *      description: Upsert an admin note to a facility.
 *      tags:
 *        - Admin
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      parameters:
 *        - name: facilityId
 *          description: Internal ID of the facility that you wish to access.
 *          in: path
 *          required: true
 *        - name: body
 *          in: body
 *          schema:
 *            type: object
 *            properties:
 *              noteId:
 *                type: string
 *              content:
 *                type: string
 *      responses:
 *        200:
 *          description: Returns JSON response of the updated user roles.
 *        500:
 *          description: Internal error from the API - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this resource.
 */

module.exports = Router({ mergeParams: true }).put(
    '/facilities/:facilityId/admin-notes',
    auth.checkGlobalAdminAccess,
    async (req, res, next) => {
        const { facilityId } = req.params;
        const modifiedBy = await auth.loggedUserAccount(req);

        const attrs = {
            noteId: req.body.noteId || uuidV4(),
            content: req.body.content,
            modifiedBy,
            modifiedAt: moment().unix(),
        };

        if (!attrs.content) {
            return res.status(400).json({ message: 'Content is required for the admin note.' });
        }

        const [status, data] = await upsertAdminNote(facilityId, attrs);
        return res.status(status).json(data);
    }
);
