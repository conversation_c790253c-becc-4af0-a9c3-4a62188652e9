'use strict';
const Router = require('express').Router
const aws = require('aws-sdk');
const geoTz = require('geo-tz');
const moment = require('moment');
const _get = require('lodash/get');
const axios = require('axios');
const uuid = require('uuid').v4;
const dedent = require('dedent');

const auth = require('../../../shared-functions/auth');
const { safelyParseJSON } = require('../../../shared-functions/utils');
const lpDB = require('../../../services/dynamodb/websitelp');
const { getBrand } = require('../../../services/dynamodb/brands');

const dbConfig = safelyParseJSON(process.env.DYNAMO_CUSTOM_CONFIG) || {};
const DB_CLUBS_TABLE = process.env.DB_CLUBS_TABLE || 'website-club-data';
const NODE_ENV = process.env.NODE_ENV || 'development';
const GOOGLE_API_KEY = process.env.GOOGLE_MAPS_STREETVIEW_KEY || '';

const ddb = new aws.DynamoDB({ apiVersion: '2012-08-10', ...dbConfig });

/**
 * @swagger
 * /api/clubs/clubpage/clubdata/{club}:
 *    get:
 *      description: Returns all metadata for the Club that is displayed on the UBX websites.
 *      tags:
 *        - Clubs
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      parameters:
 *        - name: club
 *          description: Internal ID of the club that you wish to access.
 *          in: path
 *          required: true
 *          type: string
 *      responses:
 *        200:
 *          description: Returns JSON response of club metadata.
 *        404:
 *          description: Could not find the CLUB ID in the database.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

var self = module.exports = Router({mergeParams: true})
.get('/clubs/clubpage/clubdata/:club', auth.checkFacilityAccess, async (req, res, next) => {
    try {

        var clubId = (req.params.club === undefined) ? false : req.params.club;
        self.get(clubId, true, function(club) {
            if(club == false) {
                res.status(404).send({"message": "There was an error while retrieving data for this club - Please check that the ID for this club still exists, and your user is properly associated with it in the Database."});
            } else {
                res.status(200).send(club);
            }
        });

    } catch(error) {
        next(error)
    }
});

// Returns a club from the website club data table..
module.exports.get = function(clubID, formatted, callback) {
    // Call the function that gets the club data and returns it in a formatted response...
    getClubItem(clubID, formatted, function(response) {
        callback(response);
    })
}

// Returns a promise of the get function
module.exports.getPromise = (clubID, formatted) => {
  return new Promise((resolve) => {    
    getClubItem(clubID, formatted, function(response) {
      resolve(response || null);
    });
  });
}

// Function to return the club data to the front-end website in a friendly (usable) format...
async function formatClubItem(item) {

    // Format club item in the legacy json object format...
    const [brandRes, photos, landingPageOptions] = await Promise.all([
        getBrand(item.brandId?.S),
        formatPhotos(item),
        getLandingPageOptions(item)
    ]);

    const brand = brandRes[0] === 200 ? brandRes[1] : null;
    var name = (item.name === undefined) ? item.id.S : item.name.S
    var gymMasterDomain = (item.gymMasterDomain === undefined) ? '' : item.gymMasterDomain.S
    var cutoffTime = (item.cutoffTime === undefined) ? 45 : item.cutoffTime.N
    var bookingWidget = (item.bookingWidget === undefined) ? true : item.bookingWidget.BOOL

    var clubItem = {
        "name": name,
        "organisationId": brand?.organisationId,
        "brandId": brand?.brandId,
        "brand": brand?.name ?? '',
        "published": (item.published === undefined) ? true : item.published.BOOL,
        "baseURL": (item.id === undefined) ? false : item.id.S,
        "latitude": (item.latitude === undefined) ? 0 : parseFloat(item.latitude.S),
        "longitude": (item.longitude === undefined) ? 0 : parseFloat(item.longitude.S),
        "placeData": formatPlaceData(item),
        "email": (item.email === undefined) ? "" : item.email.S,
        "secondaryEmails": !item.secondaryEmails?.S ? [] : JSON.parse(item.secondaryEmails.S),
        "phone": (item.phone === undefined) ? "" : item.phone.S,
        "url": {
          "facebook": (item.urlFacebook === undefined) ? null : item.urlFacebook.S,
          "instagram": (item.urlInstagram === undefined) ? null : item.urlInstagram.S,
          "twitter": (item.urlTwitter === undefined) ? null :  item.urlTwitter.S
          //"join": (item.urlJoin === undefined) ? null : item.urlJoin.S
        },
        "hours": formatOpeningHours(item.hours),
        "extendedAccessHours": formatOpeningHours(item.extendedAccessHours),
        "hoursComments": (item.hoursComments === undefined) ? "" : item.hoursComments.S,
        "analyticsTracking": (item.analyticsTracking === undefined) ? {"script": "","fireEvent": "onLoad"} : item?.analyticsTracking?.M ? item?.analyticsTracking?.M : JSON.parse(item.analyticsTracking.S),
        "isOpen": (item.isOpen === undefined) ? false : item.isOpen.BOOL,
        "placeholderText": (item.placeholderText === undefined) ? "" : item.placeholderText.S,
        "timeZone": { name: '' },
        "specialHours": formatSpecialHours(item.specialHours),
        "defaultBlocks": formatDefaultBlocks(item.defaultBlocks),
        "synupLocationId": (item.synupLocationId === undefined) ? "" : item.synupLocationId.S,
        "yextEntityId": (item.yextEntityId === undefined) ? "" : item.yextEntityId.S,
        "description": item.description ? item.description.S : dedent(`
          We are UBX [You-Box]: world-class boxing workouts, ready when you are.

          UBX delivers a unique mix of boxing and strength training across a 12 round circuit: 6 rounds of boxing bagwork or padwork with a coach, and 6 rounds involving a combination of functional strength, cardio and conditioning movements. Each day is different and expertly programmed by a team of experts.

          We offer the ultimate flexibility so you can get the most out of your membership. Our flexible start times mean you don't have to book ahead, plus you can mix up your workouts to include 1-on-1 support during Coached Hours, independent training during Extended Access or workout anytime, anywhere with our On Demand app.

          Try us out with a FREE WORKOUT via our website`),
        "facebookAbout": item.facebookAbout ? item.facebookAbout.S : `We are UBX [You-Box]: world-class boxing workouts, ready when you are.`,
        "photos": photos,
        "paymentMethods": formatPaymentMethods(item.paymentMethods),
        "gmbPlaceId": _get(item, 'gmbPlaceId.S', null),
        "gymMasterDomain": gymMasterDomain,
        "cutoffTime": parseInt(cutoffTime),
        "bookingWidget": Boolean(bookingWidget),
        "publishToDirectories": _get(item, 'publishToDirectories.BOOL', undefined),
        "stripeConnectedAccountID": _get(item, 'stripeConnectedAccountID.S', null),
        "disabledLandingPages": _get(item, 'disabledLandingPages.L', []).map((page) => page.S),
        "lineChat": formatLineChat(item),
        "landingPageOptions": landingPageOptions,
        "failedPayments": formatFailedPayments(item),
        "previouslyUsedPhotos": formatPreviouslyUsedPhotos(item),
        "maxBookingDays": +(item.maxBookingDays?.N ?? 14),
        "slug": item.slug?.S ?? '',
        //"landingPages": clubLandingPages(item),
    }

    if(item.timeZone == undefined) {

        clubItem.timeZone.auto = true;
        if(clubItem.latitude == 0 || clubItem.longitude == 0) {
            clubItem.timeZone.name = '';
        } else {
            clubItem.timeZone.name = geoTz(clubItem.latitude, clubItem.longitude)[0];
        }

    } else {
        clubItem.timeZone.auto = false;
        clubItem.timeZone.name = item.timeZone.S;
    }

    return clubItem;
}

function formatPreviouslyUsedPhotos(item) {
  return (item.previouslyUsedPhotos?.L ?? []).map((photo) => photo.S);
}

function formatFailedPayments(item) {
  return {
    count: +_get(item, 'failedPayments.M.count.N', 0),
    lastUpdated: +_get(item, 'failedPayments.M.lastUpdated.N', 0),
  }
}

async function getLandingPageOptions(item) {
    const clubID = item.id.S;
    const domainPrefix = NODE_ENV === 'staging'
        ? 'stage.'
        : '';
    const region = item.placeData?.M?.country?.M?.iso_code.S;
    const domainSuffix = region === 'JP'
        ? '.co.jp'
        : '.com';

    // These pages are coded by a dev and commited into the website repo
    // See https://bitbucket.org/12rnd-UBX/website-v4/src/master/src/pages/gym/%5BclubId%5D/
    const hardcodedLPs = ['28-day', 'free-trial'];

    // These pages are built by admin and provisioned to specific regions or clubs
    const availableClubLPs = await queryClubLandingPages(clubID, region);

    const buildLPObject = ({ id, ...data }) => {
        return {
            url: `https://${domainPrefix}ubxtraining${domainSuffix}/gym/${clubID}/${id}`,
            id,
            ...data,
        }
    };

    return [
        ...hardcodedLPs.map((id) => buildLPObject({ id })),
        ...availableClubLPs.map((lp) => buildLPObject({ id: lp.slug, availabilityDates: lp.availabilityDates })),
    ];
}

async function queryClubLandingPages(club, region) {
    // Query both by the club's region and direct club assignment
    const regionQueryRes = await lpDB.scanByRegion(region);
    const clubQueryRes = await lpDB.scanByClub(club);

    const data = removeDuplicatesBy([...regionQueryRes, ...clubQueryRes], 'id');

    return data
        // Return published pages only
        .filter((item) => item.published)
        // Return pages that are not expired yet
        .filter((item) => {
            if (item.isAlwaysAvailable) return true;

            const currentDate = moment();
            const endDate = moment(item.availabilityDates.end, 'MM-DD-YYYY');

            if (endDate.isBefore(currentDate)) return false;

            return true;
        });
}

function removeDuplicatesBy(array, attr) {
    const seen = new Set();
    return array.filter(item => {
        const duplicate = seen.has(item[attr]);
        seen.add(item[attr]);
        return !duplicate;
    });
}

function formatLineChat(item) {
  const lineChat = _get(item, 'lineChat.M', null);
  if (!lineChat) return null;
  return {
    qrUrl: _get(lineChat, 'qrUrl.S', ''),
    btnUrl: _get(lineChat, 'btnUrl.S', ''),
    btnImg: _get(lineChat, 'btnImg.S', ''),
  };
}

function formatPlaceData(item) {
  return (item.placeData == undefined || item.placeData == {}) 
    ? {
      country: { full_name: '', iso_code: '' },
      province: { full_name: '', abbreviation: '' },
      city: { full_name: '', short_name: '' },
      google_place_id: null,
      formatted_address: _get(item, 'address.S', null),
    } : typeof item.placeData.S === 'string'
      ? JSON.parse(item.placeData.S)
      : formatPlaceDataObject(item.placeData);
}

function getClubItem(clubID, formatted, callback) {
    if (!clubID) {
        console.error('Club ID is required to fetch club data.');
        return callback(false);
    }

    if(clubID.toLowerCase() == 'global') {

        // Used in our marketing & print templates
        var clubPlaceholderData = {
            "name": "{name}",
            "organisationId": "{organisationId}",
            "brandId": "{brandId}",
            "brand": "{brand}",
            "brand": "{brand}",
            "published": true,
            "baseURL": "{baseURL}",
            "latitude": -27.4691926,
            "longitude": 153.0030471,
            "placeData": {
              "country": {
                "full_name": "{country}",
                "iso_code": "{country}"
              },
              "province": {
                "full_name": "{province}",
                "abbreviation": "{province}"
              },
              "city": {
                "full_name": "{city}",
                "short_name": "{city}"
              },
              "google_place_id": "ChIJJ33z4q9QkWsRCZ3Z5TZicXU",
              "formatted_address": "{formatted_address}"
            },
            "email": "{email}",
            "phone": "{phone}",
            "url": {
            "facebook": "{facebook}",
            "instagram": "{instagram}",
            "twitter": null
            },
            "hours": {
              "monday": [
                { "start": "5:30am", "end": "10:00am" },
                { "start": "12:30pm", "end": "1:30pm" },
                { "start": "4:00pm", "end": "7:30pm" },
              ],
              "tuesday": [
                { "start": "5:30am", "end": "10:00am" },
                { "start": "4:00pm", "end": "7:30pm" },
              ],
              "wednesday": [
                { "start": "5:30am", "end": "10:00am" },
                { "start": "12:30pm", "end": "1:30pm" },
                { "start": "4:00pm", "end": "7:30pm" },
              ],
              "thursday": [
                { "start": "5:30am", "end": "10:00am" },
                { "start": "4:00pm", "end": "7:30pm" },
              ],
              "friday": [
                { "start": "5:30am", "end": "10:00am" },
                { "start": "12:30pm", "end": "1:30pm" },
              ],
              "saturday": [
                { "start": "7:00am", "end": "10:00am" },
              ],
              "sunday": [],
            },
            "hoursComments": "",
            "analyticsTracking": {
            "script": "",
            "fireEvent": "onLoad"
            },
            "isOpen": true,
            "placeholderText": "",
            "timeZone": { "name": '{timeZone}' },
            "specialHours": [],
            "synupLocationId": "",
            "cutoffTime": 45,
            "bookingWidget": true,
            "previouslyUsedPhotos": [],
            "slug": "{slug}",
        }

        callback(clubPlaceholderData);

    } else {
        var params = {
            TableName: DB_CLUBS_TABLE,
            Key: {
                'id' : {S: clubID},
            }
        };
        var clubsArr = [];
        ddb.getItem(params, async function(err, data) {
            if (err) {
                console.error(err);
                callback(false);
            } else {
                if(Object.keys(data).length === 0 && data.constructor === Object) {
                    callback(false)
                } else {

                    if(formatted) {
                        var item = await formatClubItem(data.Item);
                        if(item !== false) {
                            // Return the 'formatted' club data to the front-end.
                            callback(item);
                        } else {
                            callback(false)
                        }
                    } else {
                        callback(data.Item);
                    }
                }
            }
        });
    }
}

function formatPaymentMethods(paymentMethods) {
  if (!paymentMethods) {
    return ['MASTERCARD', 'AMERICANEXPRESS', 'VISA', 'APPLEPAY'];
  }

  const legacyCodes = {
    AMEX: 'AMERICANEXPRESS',
    CHEQUE: 'CHECK',
    DINERS: 'DINERSCLUB',
    APPLE_PAY: 'APPLEPAY',
    ANDROID: 'ANDROIDPAY',
    TRAVELER: 'TRAVELERSCHECK',
  };

  const allMethods = (paymentMethods || { L: [] }).L.map(p => legacyCodes[p.S] || p.S);
  return Array.from(new Set(allMethods)).sort((a, b) => a.localeCompare(b));
}

async function formatPhotos(club) {
  const photos = (club.photos || { L: [] }).L.map(p => ({
    id: p.M.id?.S ?? uuid(),
    url: p.M.url.S,
    type: p.M.type.S,
    starred: p.M.starred ? !!p.M.starred.BOOL : false,
  }));

  if (!photos.some(p => p.type === 'logo')) {
    const photo = await self.getDefaultClubPhoto('logo', club);
    photos.push(photo);
  }

  if (!photos.some(p => p.type === 'cover')) {
    const photo = await self.getDefaultClubPhoto('cover', club);
    photos.push(photo);
  }

  if (!photos.some(p => p.type === 'shop-front')) {
    const photo = await self.getDefaultClubPhoto('shop-front', club);
    photo && photos.push(photo);
  }

  return photos;
}

module.exports.getDefaultClubPhoto = async (type = 'logo', club) => {
  if (type === 'logo') {
    return { id: uuid(), type: 'logo', url: 'https://content.gymsystems.co/synup/logos/ubx-bb.jpg' };
  } else if (type === 'cover') {
    return { id: uuid(), type: 'cover', url: 'https://upload-api-assets-prd.s3.ap-southeast-2.amazonaws.com/processed/image/5831d430-bf8c-11ec-9566-21c4a674b761.jpg' };
  } else if (type === 'shop-front') {
    const defaultShopFrontPhoto = await getDefaultShopFrontPhoto(club);
    if (!defaultShopFrontPhoto[0]) return null;
    return { id: uuid(), type: 'shop-front', url: defaultShopFrontPhoto[1] };
  }
}

async function getDefaultShopFrontPhoto(club) {
  const formattedAddress = club.placeData?.M?.formatted_address?.S;
  const encodedAddress = encodeURIComponent(formattedAddress);

  try {
    const { data } = await axios.get(`https://maps.googleapis.com/maps/api/streetview/metadata?location=${encodedAddress}&key=${GOOGLE_API_KEY}`);
    if (data.status !== 'OK' || !data.pano_id) throw new Error('No street view available');
    const url = `https://maps.googleapis.com/maps/api/streetview?location=${data.location.lat},${data.location.lng}&new=true&size=600x300&key=${GOOGLE_API_KEY}`;
    return [true, url];
  } catch (error) {
    return [false];
  }
}

function formatOpeningHours(openingHours) {
  const defaultHours = {
    Monday: [],
    Tuesday: [],
    Wednesday: [],
    Thursday: [],
    Friday: [],
    Saturday: [],
    Sunday: [],
  };
  
  if (!openingHours) return defaultHours;
  let parsedHours = openingHours.S ? JSON.parse(openingHours.S) : openingHours.M;

  if (Object.keys(parsedHours).some(key => /block-\d/.test(key))) {
    // format old hours
    const newHours = { ...defaultHours };
    
    Object.keys(parsedHours).forEach(key => {
      Object.keys(parsedHours[key]).forEach(day => {
        const start = parsedHours[key][day][0];
        const end = parsedHours[key][day][1];

        if (start && end) {
          newHours[day].push({ start, end });
        }
      });
    });

    parsedHours = newHours;
  } else {
    // format new hours
    const newHours = {
      Monday: formatHourBlocks(parsedHours.Monday.L),
      Tuesday: formatHourBlocks(parsedHours.Tuesday.L),
      Wednesday: formatHourBlocks(parsedHours.Wednesday.L),
      Thursday: formatHourBlocks(parsedHours.Thursday.L),
      Friday: formatHourBlocks(parsedHours.Friday.L),
      Saturday: formatHourBlocks(parsedHours.Saturday.L),
      Sunday: formatHourBlocks(parsedHours.Sunday.L),
    }

    parsedHours = newHours;
  }

  return parsedHours;
}

function formatDefaultBlocks(defaultBlocks) {
  if (!defaultBlocks) return [];
  if (defaultBlocks.S) return JSON.parse(defaultBlocks.S);

  return formatHourBlocks(defaultBlocks.L);
}

function formatSpecialHours(specialHours) {
  if (!specialHours) return [];
  if (specialHours.S) return JSON.parse(specialHours.S);

  return specialHours.L.map(specialHour => {
    const doc = {
      id: specialHour.M.id.S ?? specialHour.M.id.N,
      name: specialHour.M.name.S,
      date: specialHour.M.date.S ? +(specialHour.M.date.S) : specialHour.M.date.N,
      blocks: formatHourBlocks(specialHour.M.blocks.L),
      tags: (specialHour.M.tags || { L: [] }).L.map(tag => tag.S),
    };

    if (specialHour.M.oldName) {
      doc.oldName = specialHour.M.oldName.S;
    }

    if (specialHour.M.oldDate) {
      doc.oldDate = specialHour.M.oldDate.S;
    }

    if (specialHour.M.unreviewed) {
      doc.unreviewed = specialHour.M.unreviewed.BOOL;
    }

    return doc;
  });
}

function formatHourBlocks(blocks) {
  const formattedBlocks = blocks.map(block => ({ start: block.M.start.S, end: block.M.end.S }));
  const sortedBlocks = formattedBlocks.sort((a, b) => moment(a.start, 'h:mma').isBefore(moment(b.start, 'h:mma')) ? -1 : 1);
  return sortedBlocks;
}

function formatPlaceDataObject(placeData) {
  const obj = {
    country: {
      iso_code: placeData.M.country.M.iso_code.S,
      full_name: placeData.M.country.M.full_name.S,
      en_full_name: placeData.M.country.M.en_full_name?.S ?? null,
    },
    formatted_address: _get(placeData, 'M.formatted_address.S', ''),
    province: {
      abbreviation: placeData.M.province.M.abbreviation.S,
      full_name: placeData.M.province.M.full_name.S,
    },
    city: {
      short_name: placeData.M.city.M.short_name.S,
      full_name: placeData.M.city.M.full_name.S,
    },
    google_place_id: _get(placeData, 'M.google_place_id.S', null),
  };

  if (_get(placeData, 'M.isManual.BOOL', false)) {
    obj.isManual = true;
    obj.address_1 = placeData.M.address_1.S;
    obj.address_2 = placeData.M.address_2.S;
    obj.postcode = placeData.M.postcode.S;
  }

  return obj;
}
