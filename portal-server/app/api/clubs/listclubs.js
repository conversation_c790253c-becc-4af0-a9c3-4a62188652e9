'use strict';
const Router        = require('express').Router
const isoCurrency   = require('iso-country-currency');
const _get          = require('lodash/get');

const scanTable     = require('../../shared-functions/scanTable');
const { getClubTimezone } = require('../../shared-functions/utils');
const { getOrganisation } = require('../../services/dynamodb/organisations');
const { getBrand } = require('../../services/dynamodb/brands');
const modules = require('../../data/modules');
const DB_CLUBS_TABLE   = process.env.DB_CLUBS_TABLE || 'website-club-data';

/**
 * @swagger
 * /api/clubs/listclubs:
 *    get:
 *      description: Returns high level list of all clubs in our database.
 *      tags:
 *        - Clubs
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all clubs.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */
var self = module.exports = Router({mergeParams: true})
.get('/clubs/listclubs', async (req, res, next) => {
    try {

        var allClubs = [];

        self.returnClubs(false,function(clubs) {
            for (var item in clubs) {
                allClubs.push(clubs[item]);
            }
            res.status(200).send(allClubs);
        })

    } catch(error) {
        next(error)
    }
});

module.exports.returnClubs = function(social=false, callback) {

    (async function() {

        let params = {
            TableName: DB_CLUBS_TABLE
        };
        let clubsArr = [];

        // Scan & paginate the entire table...
        let clubs = await scanTable(DB_CLUBS_TABLE, params);

        await Promise.all(clubs.map(async (club) => {
            let c = await self.formatClubList(social, club);
            if(c) clubsArr.push(c);
        }));

        callback(clubsArr)

    })()

}

// Function to return a club item in formatting that is accepted by the front-end portal...
module.exports.formatClubList = async (social, item) => {
    const brandRes = await getBrand(item.brandId);
    const brand = brandRes[0] === 200 ? brandRes[1] : null;
    let organisation = null;
    let redirectDashboard = null;

    if (brand) {
        const orgRes = await getOrganisation(brand?.organisationId);
        organisation = orgRes[0] === 200 ? orgRes[1] : null;
    }

    if (brand?.redirectDashboard) {
        redirectDashboard = modules.find(
            (module) => module.moduleId === brand.redirectDashboard
        )?.baseUIPath || null;
    }
    
    // Format club item in the legacy json object format...
    let name = (item.name === undefined) ? item.id : item.name
    let clubPublished = (item.published === undefined) ? false : item.published
    let id = (item.id === undefined) ? false : item.id
    let gymMasterDomain = (item.gymMasterDomain === undefined) ? false : item.gymMasterDomain

    let placeData = (item.placeData === undefined) ? false : typeof item.placeData === 'string' ? JSON.parse(item.placeData) : item.placeData;
    let currency = false;

    if (_get(placeData, 'country.iso_code')) {
        try {
            currency = isoCurrency.getAllInfoByISO(placeData.country.iso_code);
        } catch(error) {
            console.error('error getting currency', error);
        }
    }

    let clubItem = {
        "name": name,
        "organisationId": organisation?.organisationId,
        "organisation": organisation?.name,
        "organisationLogo": organisation?.logo,
        "brand": brand?.name,
        "redirectDashboard": redirectDashboard,
        "id": id,
        "gymMasterDomain": gymMasterDomain,
        "localisation": {
            "country": (placeData == false) ? false : placeData.country,
            "province": (placeData == false) ? false : placeData.province,
            "currency": currency,
            "timezone": getClubTimezone(item)
        },
        "clubPublished": clubPublished,
        "email": (item.email === undefined) ? false : item.email,
    }

    if(clubItem.localisation.currency) {
        delete clubItem.localisation.currency.countryName;

        var symbol = clubItem.localisation.currency.symbol.replace(/[A-Z.]/g, '');
        delete clubItem.localisation.currency.symbol;

        clubItem.localisation.currency.symbol = symbol;
    } else {
        console.log('no currency')
    }

    // if this request is to include the social media handles, let's push them into the response...
    if(social == true) {
        clubItem.facebook = (item.urlFacebook === undefined) ? false : item.urlFacebook
        clubItem.instagram = (item.urlInstagram === undefined) ? false : item.urlInstagram
        clubItem.phone = (item.phone === undefined) ? false : item.phone
    }

    return clubItem;
}