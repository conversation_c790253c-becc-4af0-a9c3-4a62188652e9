const Router = require('express').Router

const { checkGlobalAdminAccess } = require('../../../shared-functions/auth');
const displayDataSourcesTable = require('../../../services/dynamodb/displaydatasource');

/**
 * @swagger
 * /api/ics/data-sources:
 *    get:
 *      description: List data sources for the screen display UI.
 *      tags:
 *        - "Smart Club: Coaching Screens"
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of the successful operation.
 *        500:
 *          description: Internal error - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to perform club level functionality.
 */

module.exports = Router({ mergeParams: true }).get(
    '/ics/data-sources',
    checkGlobalAdminAccess,
    async (req, res) => {
        try {
            const [status, data] = await displayDataSourcesTable.list();
            return res.status(status).send(data);
        } catch (err) {
            if (err.message) {
                res.status(500).send({ message: err.message });
            } else {
                res.status(500).send({
                    error: 'An error occured while trying this operation. Please refer to the server logs for details.'
                });
            }
        }
});
