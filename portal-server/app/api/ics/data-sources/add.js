const Router = require('express').Router

const { checkGlobalAdminAccess } = require('../../../shared-functions/auth');
const displayDataSourcesTable = require('../../../services/dynamodb/displaydatasource');

/**
 * @swagger
 * /api/ics/data-sources:
 *    post:
 *      description: Add a new data source for the screen display UI.
 *      tags:
 *        - "Smart Club: Coaching Screens"
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      parameters:
 *        - name: name
 *          in: body
 *          required: true
 *          type: string
 *          description: Name of the data source
 *        - name: type
 *          in: body
 *          required: true
 *          type: string
 *          description: Data source type
 *          example: Performance Hub Calendar, Static
 *        - name: langShort
 *          in: body
 *          required: true
 *          type: string
 *          description: Short language name
 *          example: English
 *        - name: lang
 *          in: body
 *          type: string
 *          description: Language code
 *          example: en
 *        - name: data
 *          in: body
 *          type: string
 *          description: JSON string of data for static types
 *          example: {"country": "Australia"}
 *      responses:
 *        200:
 *          description: Returns JSON response of the successful operation.
 *        500:
 *          description: Internal error - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to perform club level functionality.
 */

module.exports = Router({ mergeParams: true }).post(
    '/ics/data-sources',
    checkGlobalAdminAccess,
    async (req, res) => {
        try {
            const [status, data] = await displayDataSourcesTable.add(req.body);
            return res.status(status).send(data);
        } catch (err) {
            if (err.message) {
                res.status(500).send({ message: err.message });
            } else {
                res.status(500).send({
                    error: 'An error occured while trying this operation. Please refer to the server logs for details.'
                });
            }
        }
    }
);
