const Router = require('express').Router

const { checkGlobalAdminAccess } = require('../../../shared-functions/auth');
const displayUIOptionsTable = require('../../../services/dynamodb/displayuioptions');

/**
 * @swagger
 * /api/ics/ui-options:
 *    get:
 *      description: List UI options for the screen displays.
 *      tags:
 *        - "Smart Club: Coaching Screens"
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of the successful operation.
 *        500:
 *          description: Internal error - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to perform club level functionality.
 */

module.exports = Router({ mergeParams: true }).get(
    '/ics/ui-options',
    checkGlobalAdminAccess,
    async (req, res) => {
        try {
            const [status, data] = await displayUIOptionsTable.list();
            return res.status(status).send(data);
        } catch (err) {
            if (err.message) {
                res.status(500).send({ message: err.message });
            } else {
                res.status(500).send({
                    error: 'An error occured while trying this operation. Please refer to the server logs for details.'
                });
            }
        }
});
