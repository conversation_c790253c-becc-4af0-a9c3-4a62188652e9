const Router = require('express').Router

const { checkGlobalAdminAccess } = require('../../../shared-functions/auth');
const displayUIOptionsTable = require('../../../services/dynamodb/displayuioptions');

/**
 * @swagger
 * /api/ics/ui-options:
 *    put:
 *      description: Update UI option for the screen displays.
 *      tags:
 *        - "Smart Club: Coaching Screens"
 *      security:
 *        - googleAppsAccount:
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      parameters:
 *        - name: id
 *          in: body
 *          required: true
 *          type: string
 *          description: Unique identifier of the data source generated upon creation
 *        - name: name
 *          in: body
 *          type: string
 *          description: Name of the UI Option
 *        - name: description
 *          in: body
 *          type: string
 *          description: Short description about this UI
 *        - name: url
 *          in: body
 *          type: string
 *          description: Base URL of the UI
 *      responses:
 *        200:
 *          description: Returns JSON response of the successful operation.
 *        500:
 *          description: Internal error - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to perform club level functionality.
 */

module.exports = Router({ mergeParams: true }).put(
    '/ics/ui-options',
    checkGlobalAdminAccess,
    async (req, res) => {
        try {
            const [status, data] = await displayUIOptionsTable.update(req.body);
            return res.status(status).send(data);
        } catch (err) {
            if (err.message) {
                res.status(500).send({ message: err.message });
            } else {
                res.status(500).send({
                    error: 'An error occured while trying this operation. Please refer to the server logs for details.'
                });
            }
        }
});
