const Router = require('express').Router

const facilityData = require('../../clubs/clubpage/clubdata');
const { get: getRoundDisplayConfig } = require('../../../services/dynamodb/rounddisplay');
const brandsTable = require('../../../services/dynamodb/brands');
const displayUIOptionsTable = require('../../../services/dynamodb/displayuioptions');
const displayDataSourcesTable = require('../../../services/dynamodb/displaydatasource');

/**
 * @swagger
 * /api/ics/programs/options/{facilityID}:
 *    get:
 *      description: Retrieves the program configuration options.
 *      tags:
 *        - "Smart Club: Coaching Screens"
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      consumes:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of the get command.
 *        500:
 *          description: Internal error - Please check the logs for further details.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to perform club level functionality.
 */

module.exports = Router({ mergeParams: true }).get(
    '/ics/programs-options/:facilityID?',
    async (req, res, next) => {
        const { facilityID = '' } = req.params;

        try {
            const urlOptions = await getRoundDisplayConfig('round-display-options');
            const calendarOptions = await getRoundDisplayConfig('calendar-options');

            const response = {
                roundDisplayURLs: {
                    options: urlOptions.options || [],
                },
                calendars: {
                    options: calendarOptions.options || [],
                },
            }

            // Original flow when no facilityID provided
            if (!facilityID) return res.status(200).send(response);

            // Get brand ID
            const facility = await facilityData.getPromise(facilityID, true);
            const [, brand = {}] = await brandsTable.getBrand(facility.brandId);

            const [, displayUIOptions = []] = await displayUIOptionsTable.list();
            const [, displayDataSources = []] = await displayDataSourcesTable.list();

            // Filter by IDs assigned to brand
            response.displayUIOptions = {
                options: displayUIOptions.filter((opt) => brand.displayUIOptions?.includes(opt.id)),
            };
            response.displayDataSources = {
                options: displayDataSources.filter((src) => brand.displayDataSources?.includes(src.id)),
            };

            res.status(200).send(response);
        } catch (err) {
            if (err.message) {
                res.status(500).send({ message: err.message });
            } else {
                res.status(500).send({
                    error: 'There was an error while trying to get the program configuration options. Please refer to the server logs for details.'
                });
            }
        }
    }
);
