const Router = require('express').Router;
const { getOrganisation } = require('../../services/dynamodb/organisations');

/**
 * @swagger
 * /internal/organisations/{orgId}:
 *    get:
 *      description: Return organisation data
 *      tags:
 *        - Internal
 *      security:
 *        - internalBusAPI: []
 *      produces:
 *        - application/json
 *      parameters:
 *        - name: orgId
 *          description: ID of the organisation you wish to retrieve.
 *          in: path
 *      responses:
 *        200:
 *          description: Returns JSON response of club metadata.
 *        404:
 *          description: Could not find the CLUB ID in the database.
 *        401:
 *          description: Not authorised to access this function.
 */

module.exports = Router({ mergeParams: true }).get('/organisations/:orgId', async (req, res) => {
    const [status, data] = await getOrganisation(req.params.orgId);
    return res.status(status).send(data);
});
