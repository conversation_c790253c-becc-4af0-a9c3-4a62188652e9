const Router = require('express').Router;
const _get = require('lodash/get');
const algoliasearch = require('algoliasearch');
const FuzzySet = require('fuzzyset.js');
const analyseSupportTicket = require('../../../api/ai/helpers/analyse-support-ticket');

const OpenAI = require('openai');
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY || 'FAKE_KEY_TO_PASS_JENKINS_API_SPEC_BUILD' });
const algoliaClient = algoliasearch(process.env.ALGOLIA_APP_ID || '', process.env.ALGOLIA_API_KEY || '');

/**
 * @swagger
 * /internal/ai/helpers/analyse-support-ticket:
 *    put:
 *      description: A simple AI helper to analise the contents of a support ticket and create suggestions based on available topics as well as suggested algolia search topics...
 *      tags:
 *        - "AI"
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      consumes:
 *        - application/json
 *      produces:
 *        - application/json
 *      parameters:
 *        - name: messageBody
 *          description: Body of the message to send
 *          in: body
 *          required: true
 *          type: string
 *        - name: topicData
 *          description: Json object/array of the topicData returned from osTicket (ticketing/support system)
 *          in: body
 *          required: true
 *          type: string
 *      responses:
 *        200:
 *          description: Success message.
 *        500:
 *          description: Internal service error.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

module.exports = Router({ mergeParams: true }).post(
  '/ai/helpers/analyse-support-ticket',
    async (req, res, next) => {
        const ticket = _get(req.body, 'messageBody', "");
        const topicData = _get(req.body, 'topicData', []);

        // TODO: This internal API currently only works for UBX which is why we hardcode the facilityId.
        // In the future, we should pass the facilityId from the request body or query parameters.
        analyseSupportTicket(ticket, topicData, 'TestClub', res);
    }
);
