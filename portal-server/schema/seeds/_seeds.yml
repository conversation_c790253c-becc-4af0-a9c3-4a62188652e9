seeds:
  domain:
    sources:

      # MARKETING & PRINT TOOL:
      - table: ${self:provider.databases.DB_DESIGN_STATIC_TABLE}
        sources: [./schema/seeds/design-static-templates.json]

      - table: ${self:provider.databases.DB_DESIGN_TEMPLATES_TABLE}
        sources: [./schema/seeds/design-templates.json]

      - table: ${self:provider.databases.DB_DESIGN_VARIABLES_TABLE}
        sources: [./schema/seeds/design-variables.json]

      - table: ${self:provider.databases.DB_DESIGN_ASSETS_TABLE}
        sources: [./schema/seeds/design-assets.json]


      # PERFORMANCE HUB CORE:

      - table: ${self:provider.databases.DB_CLUB_APPS_CONFIG_TABLE}
        sources: [./schema/seeds/club-apps-config.json]

      - table: ${self:provider.databases.DB_CLUBS_TABLE}
        sources: [./schema/seeds/clubs.json]

      - table: ${self:provider.databases.DB_PERMISSIONS_TABLE}
        sources: [./schema/seeds/permissions.json]

      - table: ${self:provider.databases.DB_FAQS_TABLE}
        sources: [./schema/seeds/faqs.json]

      - table: ${self:provider.databases.DB_PRESS_TABLE}
        sources: [./schema/seeds/press.json]

      - table: ${self:provider.databases.DB_EMAIL_MAPPING_TABLE}
        sources: [./schema/seeds/email-mapping.json]

      - table: ${self:provider.databases.DB_CLUB_REVIEWS_TABLE}
        sources: [./schema/seeds/club-reviews.json]

      - table: ${self:provider.databases.DB_CHARGES_CONFIG_TABLE}
        sources: [./schema/seeds/charges-config.json]

      - table: ${self:provider.databases.DB_DESIGN_INPUT_MAPPING_TABLE}
        sources: [./schema/seeds/design-input-mapping.json]

      - table: ${self:provider.databases.DB_NOTIFICATION_MESSAGES_TABLE}
        sources: [./schema/seeds/notifications.json]

      - table: ${self:provider.databases.DB_ROUND_DISPLAY_BASE_CONFIG}
        sources: [./schema/seeds/round-display-overwrites.json ]

      - table: ${self:provider.databases.DB_SAAS_BILLING_TABLE}
        sources: [./schema/seeds/saas-billing.json]

      - table: ${self:provider.databases.DB_SAAS_BILLING_CONFIG_TABLE}
        sources: [./schema/seeds/saas-billing-config.json]

      - table: ${self:provider.databases.DB_SAAS_BILLING_ADDITIONAL_LINE_ITEMS_TABLE}
        sources: [./schema/seeds/saas-billing-additional-line-items.json]

      - table: ${self:provider.databases.DB_SAAS_BILLING_MONTHLY_RECORDS_TABLE}
        sources: [./schema/seeds/saas-billing-monthly-records.json]

      - table: ${self:provider.databases.DB_SAAS_BILLING_PAYMENT_SETTINGS_TABLE}
        sources: [./schema/seeds/saas-billing-payment-settings.json]
      
      - table: ${self:provider.databases.DB_GM_MEMBERSHIP_ACCESS_TYPES_TABLE}
        sources: [./schema/seeds/membership_access_types.json]
 
      - table: ${self:provider.databases.DB_TRAINING_CAMP_SEASON_DATA}
        sources: [./schema/seeds/tc-season-members.json]

      - table: ${self:provider.databases.CALENDAR_TYPE_TABLE}
        sources: [./schema/seeds/builder-calendar-type.json]
      
      - table: ${self:provider.databases.USER_PREFERENCES_TABLE}
        sources: [./schema/seeds/tc-user-preferences.json]
      
      - table: ${self:provider.databases.LEADERBOARD_TABLE}
        sources: [./schema/seeds/leaderboards.json]
      
      - table: ${self:provider.databases.DB_ORGANISATIONS_TABLE}
        sources: [./schema/seeds/organisations.json]

      - table: ${self:provider.databases.DB_BRANDS_TABLE}
        sources: [./schema/seeds/brands.json]

      - table: ${self:provider.databases.DB_ROLES_TABLE}
        sources: [./schema/seeds/roles.json]
