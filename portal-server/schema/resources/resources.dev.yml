# Refer to AWS Documentation (cloud formation YML template) for all params:
# https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3-bucket.html#cfn-s3-bucket-accelerateconfiguration

Resources:

  MarketingPrintRenders:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_MARKETING_PRINT_LAMBDA}
      # This timeout must match the timeout of the lambda function that processes this queue
      VisibilityTimeout: 900

      Tags:
      - Key: CostCenter
        Value: "Performance Hub: Marketing Print"

  MarketingPrintEC2Renders:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_MARKETING_PRINT_EC2}

      Tags:
      - Key: CostCenter
        Value: "Performance Hub: Marketing Print"

  MarketingPrintCompleted:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_MARKETING_PRINT_COMPLETED}

      Tags:
      - Key: CostCenter
        Value: "Performance Hub: Marketing Print"

  GMMemberDataSyncQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_GYMMASTER_MEMBER_DATA_SYNC_QUEUE}
      Tags:
      - Key: CostCenter
        Value: "Performance Hub: GymMaster"
      VisibilityTimeout: 900  # Visibility timeout set to 900 seconds to match the lambda function timeout

  # Dead Letter Queue for the SaaS Billing Log.
  # Messages that fail for more than 3 times will be sent to this queue.
  DailySaasBillingLogDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_DAILY_SAAS_BILLING_LOG_QUEUE}-dlq
      Tags:
      - Key: CostCenter
        Value: "Performance Hub: SaaS Billing and Stripe"

  DailySaasBillingLogQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_DAILY_SAAS_BILLING_LOG_QUEUE}
      Tags:
      - Key: CostCenter
        Value: "Performance Hub: SaaS Billing and Stripe"
      VisibilityTimeout: 900  # Visibility timeout set to 900 seconds to match the lambda function timeout
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt DailySaasBillingLogDLQ.Arn
        maxReceiveCount: 3

  MonthlySaasBillerQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_MONTHLY_SAAS_BILLER_QUEUE}
      Tags:
      - Key: CostCenter
        Value: "Performance Hub: SaaS Billing and Stripe"
      VisibilityTimeout: 900  # Visibility timeout set to 900 seconds to match the lambda function timeout

################################################################

  phIamDefaultAccess:
    Type: AWS::IAM::Role
    Properties:
      Path: /performancehub/sls/default-iam/
      RoleName: phIamDefaultAccess-${self:provider.stage}
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: phIamDefaultAccess-${self:provider.stage}
          PolicyDocument:
            Version: '2012-10-17'
            Statement:

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - 'Fn::Join':
                    - ':'
                    -
                      - 'arn:aws:logs'
                      - Ref: 'AWS::Region'
                      - Ref: 'AWS::AccountId'
                      - 'log-group:/aws/lambda/*:*:*'

  phIamDynamoDBfull:
    Type: AWS::IAM::Role
    Properties:
      Path: /performancehub/sls/dynamodb-full/
      RoleName: phIamDynamoDBfull-${self:provider.stage}
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: phIamDynamoDBfull-${self:provider.stage}
          PolicyDocument:
            Version: '2012-10-17'
            Statement:

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - 'Fn::Join':
                    - ':'
                    -
                      - 'arn:aws:logs'
                      - Ref: 'AWS::Region'
                      - Ref: 'AWS::AccountId'
                      - 'log-group:/aws/lambda/*:*:*'

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - dynamodb:*
                Resource:
                  - "arn:aws:dynamodb:*"

  phIamRenderWorker:
    Type: AWS::IAM::Role
    Properties:
      Path: /performancehub/sls/dynamodb-full/
      RoleName: phIamRenderWorker-${self:provider.stage}
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: phIamRenderWorker-${self:provider.stage}
          PolicyDocument:
            Version: '2012-10-17'
            Statement:

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - 'Fn::Join':
                    - ':'
                    -
                      - 'arn:aws:logs'
                      - Ref: 'AWS::Region'
                      - Ref: 'AWS::AccountId'
                      - 'log-group:/aws/lambda/*:*:*'

              # SQS
              - Effect: Allow
                Action:
                  - "sqs:DeleteMessage"
                  - "sqs:GetQueueUrl"
                  - "sqs:ChangeMessageVisibility"
                  - "sqs:ReceiveMessage"
                  - "sqs:SendMessage"
                  - "sqs:GetQueueAttributes"
                  - "sqs:SetQueueAttributes"

                Resource:
                  - "arn:aws:sqs:*"

              # Lambda * permissions
              - Effect: Allow
                Action:
                  - lambda:*
                Resource:
                  - "arn:aws:lambda:*"

              # DynamoDB * permissions
              - Effect: Allow
                Action:
                  - dynamodb:*
                Resource:
                  - "arn:aws:dynamodb:*"

              # EC2 List & Execute/start instance...
              - Effect: Allow
                Action:
                  - "ec2:DescribeInstances"
                  - "ec2:DescribeInstanceStatus"
                  - "ec2:StartInstances"
                  - "ec2:StopInstances"
                Resource:
                  - "*"

  phIamCrons:
    Type: AWS::IAM::Role
    Properties:
      Path: /performancehub/sls/crons/
      RoleName: phIamCrons-${self:provider.stage}
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: phIamCrons-${self:provider.stage}
          PolicyDocument:
            Version: '2012-10-17'
            Statement:

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - 'Fn::Join':
                    - ':'
                    -
                      - 'arn:aws:logs'
                      - Ref: 'AWS::Region'
                      - Ref: 'AWS::AccountId'
                      - 'log-group:/aws/lambda/*:*:*'

              # Lambda * permissions
              - Effect: Allow
                Action:
                  - lambda:*
                Resource:
                  - "arn:aws:lambda:*"

              # DynamoDB * permissions
              - Effect: Allow
                Action:
                  - dynamodb:*
                Resource:
                  - "arn:aws:dynamodb:*"

              # We need EC2 Create network permission/access so we can attach lambda functions to VPC
              - Effect: Allow
                Action:
                  - "ec2:DescribeNetworkInterfaces"
                  - "ec2:CreateNetworkInterface"
                  - "ec2:DeleteNetworkInterface"
                  - "ec2:DescribeInstances"
                  - "ec2:AttachNetworkInterface"
                Resource:
                  - "*"
              
              # IAM Role Get and Pass
              - Effect: Allow
                Action:
                  - iam:GetRole
                  - iam:PassRole
                Resource:
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/service-role/MediaConvert_Default_Role'
              
              # MediaConvert permissions
              - Effect: Allow
                Action:
                  - mediaconvert:DescribeEndpoints
                  - mediaconvert:CreateJob
                  - mediaconvert:GetJob
                Resource: '*'

              # S3 permissions
              - Effect: Allow
                Action:
                  - s3:GetObject
                Resource:
                  - !Sub 'arn:aws:s3:::${self:provider.environment.S3_UPLOADS_BUCKET}/*'

              # S3 permissions
              - Effect: Allow
                Action:
                  - s3:*
                Resource:
                  - !Sub 'arn:aws:s3:::${self:provider.environment.S3_CCTV_RECORDING_BUCKET}'

              # Allow Access to M&P SQS Queues and also the GymMaster Member Data Sync Queue...
              - Effect: Allow
                Action:
                  - sqs:*
                Resource:
                  - "arn:aws:sqs:*"

              # Allow access to SES
              - Effect: Allow
                Action:
                  - ses:SendEmail
                  - ses:SendRawEmail
                Resource: "*"

################################################################

  releaseHistory:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Performance Hub: Core"

      # Note we are using references from YML here as we've run out of environment variables in lambda config
      TableName: ${self:provider.databases.DB_RELEASE_HISTORY}

      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: service
          AttributeType: S
        - AttributeName: date
          AttributeType: N

      KeySchema:
        - AttributeName: service
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE

  roundDisplayConfigTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Smart Club: Coaching Screens"

      TableName: ${self:provider.databases.DB_ROUND_DISPLAY_BASE_CONFIG}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
      KeySchema:
        - AttributeName: club
          KeyType: HASH

  GmClubReportingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Performance Hub: GymMaster"
      TableName: ${self:provider.databases.DB_GM_CLUB_REPORTING}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: date
          AttributeType: N

      # Because we're using a SQS queue to write the records into the table, and we only process the queue one item at a time - we will never have collisions using a unixtime on the sort key.
      KeySchema:
        - AttributeName: club
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE

      GlobalSecondaryIndexes:
        - IndexName: club
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  CountrySettingsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_COUNTRY_HOLIDAYS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  SMSScheduleTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_SMS_SCHEDULE_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  WorkoutBookingLeadsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_WORKOUT_BOOKING_LEADS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: club
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  ClubSocialReviewsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_CLUB_REVIEWS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: club
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  ClubSocialReviewsSettingsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_CLUB_REVIEWS_SETTINGS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
      KeySchema:
        - AttributeName: club
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  UserPreferencesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_USER_PREFERENCES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  FaqsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_FAQS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: faqId
          AttributeType: S
      KeySchema:
        - AttributeName: faqId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"

  PressTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_PRESS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: pressId
          AttributeType: S
      KeySchema:
        - AttributeName: pressId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"

  EmailMappingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_EMAIL_MAPPING_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"

  CustomTrackingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_CUSTOM_TRACKING_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: "clubID"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "clubID"
          KeyType: "HASH"
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"

  ClubAgreementDynamoDbTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "name"
          AttributeType: "S"
        - AttributeName: "clubId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "NameIndex"
          KeySchema:
          - AttributeName: "name"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "ClubIndex"
          KeySchema:
          - AttributeName: "clubId"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_CLUB_USER_AGREEMENT}

  ClubAgreementConfigTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "clubId"
          AttributeType: "S"
        - AttributeName: "clubUserAgreementId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "ClubIndex"
          KeySchema:
          - AttributeName: "clubId"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "ClubUserAgrIndex"
          KeySchema:
          - AttributeName: "clubUserAgreementId"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_CLUB_USER_AGREEMENT_CONFIG}

  DesignRendersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Performance Hub: Marketing Print"
      TableName: ${self:provider.databases.DB_DESIGN_RENDERS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: renderedAt
          AttributeType: N
        - AttributeName: userID
          AttributeType: S

      # Because we're using a SQS queue to write the records into the table, and we only process the queue one item at a time - we will never have collisions using a unixtime on the sort key.
      KeySchema:
        - AttributeName: club
          KeyType: HASH
        - AttributeName: renderedAt
          KeyType: RANGE

      GlobalSecondaryIndexes:
        - IndexName: club
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: userID
          KeySchema:
            - AttributeName: userID
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  DesignTemplatesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Performance Hub: Marketing Print"
      TableName: ${self:provider.databases.DB_DESIGN_TEMPLATES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: id
          AttributeType: S
        - AttributeName: designVisibility
          AttributeType: S
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: designVisibility
          KeySchema:
            - AttributeName: designVisibility
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: designVisibilityOrgId-index
          KeySchema:
            - AttributeName: designVisibility
              KeyType: HASH
            - AttributeName: organisationId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  GmMembershipDataTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_GM_MEMBERSHIP_DATA_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: clubID
          AttributeType: S
        - AttributeName: ClubIDMembershipTypeID
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: clubID-index
          KeySchema:
            - AttributeName: clubID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: ClubIDMembershipTypeID-index
          KeySchema:
            - AttributeName: ClubIDMembershipTypeID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"
          
  GmMembershipAccessTypesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_GM_MEMBERSHIP_ACCESS_TYPES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"
  
  TrainingCampSeasonDataTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_TRAINING_CAMP_SEASON_DATA}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: "userId"
          AttributeType: S
        - AttributeName: "season"
          AttributeType: N
        - AttributeName: "clubId"
          AttributeType: S
      KeySchema:
        - AttributeName: "userId"
          KeyType: "HASH"
        - AttributeName: "season"
          KeyType: "RANGE"
      GlobalSecondaryIndexes: 
        - IndexName: "SeasonAllAttrIndex"
          KeySchema:
          - AttributeName: "season"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL" 
        - IndexName: "ClubIndex"
          KeySchema:
          - AttributeName: "clubId"
            KeyType: "HASH"
          - AttributeName: "season"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "ALL"
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"
  
  LeaderboardTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Delete
    Properties:
      AttributeDefinitions:
        - AttributeName: "lbId"
          AttributeType: "S"
        - AttributeName: "UserIdClub"
          AttributeType: "S"
        - AttributeName: "RankingUserIdClub"
          AttributeType: "S"
        - AttributeName: "scopeClub"
          AttributeType: "S"
        - AttributeName: "ClubRankingUserId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "lbId"
          KeyType: "HASH"
        - AttributeName: "UserIdClub"
          KeyType: "RANGE"
      GlobalSecondaryIndexes:
        - IndexName: "GlobalRanking"
          KeySchema:
          - AttributeName: "lbId"
            KeyType: "HASH"
          - AttributeName: "RankingUserIdClub"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "INCLUDE"
            NonKeyAttributes: ['rating', 'ranking', 'clubRanking', 'userData']
        - IndexName: "ClubRanking"
          KeySchema:
          - AttributeName: "scopeClub"
            KeyType: "HASH"
          - AttributeName: "ClubRankingUserId"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "INCLUDE"
            NonKeyAttributes: ['rating', 'clubRanking', 'ranking', 'userData']
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"
      # use "on-demand" scalable model for training camp tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.LEADERBOARD_TABLE}

  CalendarTypeDataTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      AttributeDefinitions:
        - AttributeName: "name"
          AttributeType: "S"
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "name"
          KeySchema:
          - AttributeName: "name"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.CALENDAR_TYPE_TABLE}

  UserPreferencesDataTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.USER_PREFERENCES_TABLE}
  
  DesignStaticTemplatesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_DESIGN_STATIC_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: id
          AttributeType: S
        - AttributeName: designVisibility
          AttributeType: S
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: designVisibility
          KeySchema:
            - AttributeName: designVisibility
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: designVisibilityOrgId-index
          KeySchema:
            - AttributeName: designVisibility
              KeyType: HASH
            - AttributeName: organisationId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  DesignVariablesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_DESIGN_VARIABLES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: type
          AttributeType: S
        - AttributeName: variable
          AttributeType: S
      KeySchema:
        - AttributeName: variable
          KeyType: HASH
        - AttributeName: club
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: type-index
          KeySchema:
            - AttributeName: type
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  DesignAssetsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_DESIGN_ASSETS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: deleted
          AttributeType: 'N'
      KeySchema:
        - AttributeName: id
          KeyType: HASH 
      GlobalSecondaryIndexes:
        - IndexName: deleted-index
          KeySchema:
            - AttributeName: deleted
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  DesignCustomPagesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_DESIGN_CUSTOM_PAGES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: deleted
          AttributeType: 'N'
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH 
      GlobalSecondaryIndexes:
        - IndexName: organisationId-index
          KeySchema:
            - AttributeName: organisationId
              KeyType: HASH
            - AttributeName: deleted
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  DesignInputMappingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_DESIGN_INPUT_MAPPING_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: designId
          AttributeType: S
      KeySchema:
        - AttributeName: designId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  ChargesConfigTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_CHARGES_CONFIG_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: restrictedTo
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: restricted-to-index
          KeySchema:
            - AttributeName: restrictedTo
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"

  PerfHubClubPreferencesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_CLUB_PREFERENCES_TABLE}

  PermissionsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_PERMISSIONS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  OrganisationsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_ORGANISATIONS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: organisationId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  BrandsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_BRANDS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: brandId
          AttributeType: S
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: brandId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      GlobalSecondaryIndexes:
        - IndexName: organisationId-index
          KeySchema:
            - AttributeName: organisationId
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  RolesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_ROLES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: roleId
          AttributeType: S
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: roleId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      GlobalSecondaryIndexes:
        - IndexName: organisationId-index
          KeySchema:
            - AttributeName: organisationId
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  ClubsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_CLUBS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: lowercaseID
          AttributeType: S
        - AttributeName: gymMasterDomain
          AttributeType: S
        - AttributeName: slug
          AttributeType: S
        - AttributeName: brandId
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      GlobalSecondaryIndexes:
        - IndexName: LowercaseID
          KeySchema:
            - AttributeName: "lowercaseID"
              KeyType: "HASH"
          Projection:
            ProjectionType: ALL
        - IndexName: GMDomain
          KeySchema:
            - AttributeName: "gymMasterDomain"
              KeyType: "HASH"
          Projection:
            ProjectionType: KEYS_ONLY
        - IndexName: brand-slug-index
          KeySchema:
            - AttributeName: "brandId"
              KeyType: "HASH"
            - AttributeName: "slug"
              KeyType: "RANGE"
          Projection:
            ProjectionType: ALL

  CCTVFacilityReportTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Smart Club: Core"

      TableName: ${self:provider.databases.DB_CCTV_FACILITY_REPORT_TABLE}

      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: clubID
          AttributeType: S
        - AttributeName: date
          AttributeType: N
        # - AttributeName: reportType
        #   AttributeType: S

      KeySchema:
        - AttributeName: clubID
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE


  SMSTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_SMS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: "twilio-id"
          AttributeType: "S"
        - AttributeName: "sendDate"
          AttributeType: "N"
        - AttributeName: "status"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "twilio-id"
          KeyType: "HASH"
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      GlobalSecondaryIndexes:
        - IndexName: StatusSendDateIndex
          KeySchema:
            - AttributeName: "status"
              KeyType: "HASH"
            - AttributeName: sendDate
              KeyType: "RANGE"
          Projection:
            ProjectionType: ALL

  DeviceAutomationsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_DEVICE_AUTOMATIONS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
      KeySchema:
        - AttributeName: club
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Smart Club: Core"

  AnnouncementTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_ANNOUNCEMENTS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  GMMemberDataTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_GM_MEMBER_DATA_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: clubID
          AttributeType: S
        - AttributeName: created
          AttributeType: S
        - AttributeName: email
          AttributeType: S
        - AttributeName: gmID
          AttributeType: 'N'
        - AttributeName: phonecell
          AttributeType: S
      KeySchema:
        - AttributeName: gmID
          KeyType: HASH
        - AttributeName: clubID
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: clubID-index
          KeySchema:
            - AttributeName: clubID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: created
          KeySchema:
            - AttributeName: created
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: phonecell-index
          KeySchema:
            - AttributeName: phonecell
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: email-index
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"

  ClubBusyHoursTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "clubId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "clubId"
          KeyType: "HASH"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_CLUB_BUSY_HOURS_TABLE}

  PerfHubNotificationMessagesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "target"
          AttributeType: "S"
        - AttributeName: "category"
          AttributeType: "S"
        - AttributeName: "created"
          AttributeType: "N"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "TargetIndex"
          KeySchema:
          - AttributeName: "target"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "CategoryIndex"
          KeySchema:
          - AttributeName: "category"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "TargetDateIndex"
          KeySchema:
          - AttributeName: "target"
            KeyType: "HASH"
          - AttributeName: "created"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "ALL"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_NOTIFICATION_MESSAGES_TABLE}

  PerfHubConsumedNotificationsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "clubId"
          AttributeType: "S"
        - AttributeName: "email"
          AttributeType: "S"
        - AttributeName: "notificationId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "EmailIndex"
          KeySchema:
          - AttributeName: "email"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "NotificationIndex"
          KeySchema:
          - AttributeName: "notificationId"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "ClubEmailIndex"
          KeySchema:
          - AttributeName: "clubId"
            KeyType: "HASH"
          - AttributeName: "email"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "INCLUDE"
            NonKeyAttributes: ['clubId', 'email', 'deleted', 'target', 'created']
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_CONSUMED_NOTIFICATIONS_TABLE}

  ClubKYCTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "clubId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "clubId"
          KeyType: "HASH"
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_CLUB_KYC_TABLE}

  PHSecureUploadsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "s3Key"
          AttributeType: "S"
        - AttributeName: "status"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "s3Key"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "status"
          KeySchema:
          - AttributeName: "status"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_SECURE_UPLOADS_TABLE}

  PHHistoricalClubHolidaysTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "clubID"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "clubID"
          KeySchema:
          - AttributeName: "clubID"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_HISTORICAL_CLUB_HOLIDAYS_TABLE}

  PHSaaSBillingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "clubID"
          AttributeType: "S"
        - AttributeName: "date"
          AttributeType: "N"
        - AttributeName: "imported"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "clubAndDate"
          KeySchema:
          - AttributeName: "clubID"
            KeyType: "HASH"
          - AttributeName: "date"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "clubID"
          KeySchema:
          - AttributeName: "clubID"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "imported"
          KeySchema:
          - AttributeName: "imported"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_SAAS_BILLING_TABLE}

  PHSaaSBillingConfigTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"
      AttributeDefinitions:
        - AttributeName: "target"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "target"
          KeyType: "HASH"
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_SAAS_BILLING_CONFIG_TABLE}

  PHSaaSBillingAdditionalLineItemsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "archived"
          AttributeType: "S"
        - AttributeName: "dateEffective"
          AttributeType: "N"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "archived-index"
          KeySchema:
          - AttributeName: "archived"
            KeyType: "HASH"
          - AttributeName: "dateEffective"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "ALL"
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_SAAS_BILLING_ADDITIONAL_LINE_ITEMS_TABLE}

  PHSaaSBillingMonthlyRecordsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "clubID"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: clubID-index
          KeySchema:
            - AttributeName: clubID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_SAAS_BILLING_MONTHLY_RECORDS_TABLE}

  agreementsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_AGREEMENTS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: agreementid
          AttributeType: S
      KeySchema:
        - AttributeName: agreementid
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  agreementsSignedTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_AGREEMENTS_SIGNED_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: agreementid
          AttributeType: 'S'
      KeySchema:
        - AttributeName: club
          KeyType: HASH
        - AttributeName: agreementid
          KeyType: RANGE
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"


  ###########################################################################

  # This is/has moved to ScyllaDB in prod
  DevicesMetadataTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_DEVICES_METADATA_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: clubID
          AttributeType: S
        - AttributeName: deviceID
          AttributeType: S
        - AttributeName: updated
          AttributeType: 'N'
        - AttributeName: uuid
          AttributeType: S
      KeySchema:
        - AttributeName: uuid
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: clubID
          KeySchema:
            - AttributeName: clubID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: deviceID
          KeySchema:
            - AttributeName: deviceID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: updated
          KeySchema:
            - AttributeName: updated
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Smart Club: Core"

  # Schema for this is actually managed in Universal API API.
  ClubAppsConfigDynamoDbTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      AttributeDefinitions:
        - AttributeName: "clubID"
          AttributeType: "S"
        - AttributeName: "appID"
          AttributeType: "S"

      KeySchema:
        - AttributeName: "clubID"
          KeyType: "HASH"
        - AttributeName: "appID"
          KeyType: "RANGE"

      # use "on-demand" scalable model for training camp tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST

      TableName: ${self:provider.databases.DB_CLUB_APPS_CONFIG_TABLE}
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  CoachingScreensDevicesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      TableName: ${self:provider.databases.DB_DEVICE_REGISTRATIONS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: balenaUUID
          AttributeType: S
        - AttributeName: clubID
          AttributeType: S
        - AttributeName: deviceID
          AttributeType: S
        - AttributeName: lastIP
          AttributeType: S
        - AttributeName: updated
          AttributeType: 'N'
      KeySchema:
        - AttributeName: deviceID
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: balenaUUID
          KeySchema:
            - AttributeName: balenaUUID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: lastIP
          KeySchema:
            - AttributeName: lastIP
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: clubID
          KeySchema:
            - AttributeName: clubID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: updated
          KeySchema:
            - AttributeName: updated
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  SaaSBillingPaymentSettingsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      TableName: ${self:provider.databases.DB_SAAS_BILLING_PAYMENT_SETTINGS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: clubID
          AttributeType: S
      KeySchema:
        - AttributeName: clubID
          KeyType: HASH

  ############################################################################
  # THESE SERVICES ARE NOT CURRENTLY DEPLOYED INTO PROD
  ############################################################################

  AcademyTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_ACADEMY_USERS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  # DEPRECATED
  TrainFreeUsersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_TRAIN_FREE_USERS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: dateCreated
          AttributeType: 'N'
        - AttributeName: email
          AttributeType: S
        - AttributeName: mobile
          AttributeType: S
        - AttributeName: suburb
          AttributeType: S
      KeySchema:
        - AttributeName: mobile
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: dateCreated-index
          KeySchema:
            - AttributeName: dateCreated
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: email-index
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: suburb-index
          KeySchema:
            - AttributeName: suburb
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Train App"

  # DEPRECATED
  TrainingCampChallengesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_TRAINING_CAMP_CHALLENGES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: goals
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
        - AttributeName: goals
          KeyType: RANGE
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Training Camp"

  # DEPRECATED
  TrainingCampLeadsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_TRAINING_CAMP_LEADS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: date
          AttributeType: 'N'
        - AttributeName: email
          AttributeType: S
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: date-index
          KeySchema:
            - AttributeName: date
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: email-index
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Training Camp"

  # DEPRECATED
  TrainingCampUsersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_TRAINING_CAMP_USERS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: email
          AttributeType: S
        - AttributeName: home_club_id
          AttributeType: S
        - AttributeName: season
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
        - AttributeName: season
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: home_club_id
          KeySchema:
            - AttributeName: home_club_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: email
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Training Camp"

  # OLD LOGIC THAT NEEDS TO BE MIGRATED
  WebsiteLeadsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_WEBSITE_LEADS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: campaign
          AttributeType: S
        - AttributeName: club
          AttributeType: S
        - AttributeName: email
          AttributeType: S
      KeySchema:
        - AttributeName: email
          KeyType: HASH
        - AttributeName: campaign
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: campaign-index
          KeySchema:
            - AttributeName: campaign
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"

  CountersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_COUNTERS_TABLE}

  LandingPagesDynamoDbTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "slug"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "SlugIndex"
          KeySchema:
            - AttributeName: "slug"
              KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_LANDING_PAGES_TABLE}

  DisplayUIOptionsDynamoDbTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_DISPLAY_UI_OPTIONS_TABLE}

  DisplayDataSourcesDynamoDbTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_DISPLAY_DATA_SOURCES_TABLE}

################################################################
# ElastiCache / REDIS

  PerfHubServerlessCacheSubnetGroup:
    Type: AWS::ElastiCache::SubnetGroup
    Properties:
      Description: "Cache Subnet Group"
      SubnetIds:
      - ${self:custom.vpc_${opt:stage, self:provider.stage}.subnetIds}

  PerfHubECCluster:
    Type: AWS::ElastiCache::CacheCluster
    Properties:
      AutoMinorVersionUpgrade: true
      Engine: redis
      CacheNodeType: cache.t2.micro
      NumCacheNodes: 1
      VpcSecurityGroupIds:
      - ${self:custom.vpc_${opt:stage, self:provider.stage}.securityGroupIds}
      CacheSubnetGroupName:
        Ref: ServerlessCacheSubnetGroup
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

##################################################################
# S3 Buckets
  PHClubKYCBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: ${self:provider.environment.S3_CLUB_KYC_BUCKET}
      AccessControl: Private
      Tags:
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  PHUploadsBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    Properties:
      BucketName: ${self:provider.environment.S3_UPLOADS_BUCKET}
      AccessControl: Private
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - HEAD
              - DELETE
            AllowedOrigins:
              - "*"
      Tags:
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
