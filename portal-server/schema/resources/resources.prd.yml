# Refer to AWS Documentation (cloud formation YML template) for all params:
# https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3-bucket.html#cfn-s3-bucket-accelerateconfiguration
#
# DO NOT DELETE THIS RESOURCE FILE
# IT IS NEEDED EVEN IF IT'S EMPTY TO START SERVERLESS WITH THE 'prd' ENVIRONMENT RESOURCE FILE...

Resources:

  ################################################################
  # ALB for Performance Hub Core

  PhCoreALB:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: ph-core-${self:provider.stage}
      Scheme: internet-facing  # Change to 'internal' if needed
      Type: application
      SecurityGroups: ${self:custom.vpc_${opt:stage, self:provider.stage}.securityGroupIds_loadbalancer}
      Subnets: ${self:custom.vpc_${opt:stage, self:provider.stage}.subnetIds}

  # ALB Listener (HTTPS with Custom Certificate)
  PhCoreALBListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      LoadBalancerArn: !Ref PhCoreALB
      Protocol: HTTPS
      Port: 443
      SslPolicy: ELBSecurityPolicy-2016-08  # AWS recommended TLS policy as minimum
      Certificates:
        - CertificateArn: ${self:custom.certificates.ph_lb_${opt:stage, self:provider.stage}.certificateArn}
      DefaultActions:
        - Type: fixed-response
          FixedResponseConfig:
            StatusCode: 404
            ContentType: text/plain
            MessageBody: "Not Found"

################################################################

  MarketingPrintRenders:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_MARKETING_PRINT_LAMBDA}
      # This timeout must match the timeout of the lambda function that processes this queue
      VisibilityTimeout: 900

      Tags:
      - Key: CostCenter
        Value: "Performance Hub: Marketing Print"

  MarketingPrintEC2Renders:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_MARKETING_PRINT_EC2}

      Tags:
      - Key: CostCenter
        Value: "Performance Hub: Marketing Print"

  MarketingPrintCompleted:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_MARKETING_PRINT_COMPLETED}

      Tags:
      - Key: CostCenter
        Value: "Performance Hub: Marketing Print"

  GMMemberDataSyncQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_GYMMASTER_MEMBER_DATA_SYNC_QUEUE}
      Tags:
      - Key: CostCenter
        Value: "Performance Hub: GymMaster"
      VisibilityTimeout: 900  # Visibility timeout set to 900 seconds to match the lambda function timeout

  # Dead Letter Queue for the SaaS Billing Log.
  # Messages that fail for more than 3 times will be sent to this queue.
  DailySaasBillingLogDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_DAILY_SAAS_BILLING_LOG_QUEUE}-dlq
      Tags:
      - Key: CostCenter
        Value: "Performance Hub: SaaS Billing and Stripe"

  DailySaasBillingLogQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_DAILY_SAAS_BILLING_LOG_QUEUE}
      Tags:
      - Key: CostCenter
        Value: "Performance Hub: SaaS Billing and Stripe"
      VisibilityTimeout: 900  # Visibility timeout set to 900 seconds to match the lambda function timeout
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt DailySaasBillingLogDLQ.Arn
        maxReceiveCount: 3

  MonthlySaasBillerQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:provider.environment.SQS_MONTHLY_SAAS_BILLER_QUEUE}
      Tags:
      - Key: CostCenter
        Value: "Performance Hub: SaaS Billing and Stripe"
      VisibilityTimeout: 900  # Visibility timeout set to 900 seconds to match the lambda function timeout

################################################################

  phIamDefaultAccess:
    Type: AWS::IAM::Role
    Properties:
      Path: /performancehub/sls/default-iam/
      RoleName: phIamDefaultAccess-${self:provider.stage}
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: phIamDefaultAccess-${self:provider.stage}
          PolicyDocument:
            Version: '2012-10-17'
            Statement:

              # xray permissions (required)
              - Effect: "Allow"
                Action:
                  - "xray:PutTraceSegments"
                  - "xray:PutTelemetryRecords"
                Resource:
                  - "*"

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - 'Fn::Join':
                    - ':'
                    -
                      - 'arn:aws:logs'
                      - Ref: 'AWS::Region'
                      - Ref: 'AWS::AccountId'
                      - 'log-group:/aws/lambda/*:*:*'

  phIamDynamoDBfull:
    Type: AWS::IAM::Role
    Properties:
      Path: /performancehub/sls/dynamodb-full/
      RoleName: phIamDynamoDBfull-${self:provider.stage}
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: phIamDynamoDBfull-${self:provider.stage}
          PolicyDocument:
            Version: '2012-10-17'
            Statement:

              # xray permissions (required)
              - Effect: "Allow"
                Action:
                  - "xray:PutTraceSegments"
                  - "xray:PutTelemetryRecords"
                Resource:
                  - "*"

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - 'Fn::Join':
                    - ':'
                    -
                      - 'arn:aws:logs'
                      - Ref: 'AWS::Region'
                      - Ref: 'AWS::AccountId'
                      - 'log-group:/aws/lambda/*:*:*'

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - dynamodb:*
                Resource:
                  - "arn:aws:dynamodb:*"

  phIamRenderWorker:
    Type: AWS::IAM::Role
    Properties:
      Path: /performancehub/sls/dynamodb-full/
      RoleName: phIamRenderWorker-${self:provider.stage}
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: phIamRenderWorker-${self:provider.stage}
          PolicyDocument:
            Version: '2012-10-17'
            Statement:

              # xray permissions (required)
              - Effect: "Allow"
                Action:
                  - "xray:PutTraceSegments"
                  - "xray:PutTelemetryRecords"
                Resource:
                  - "*"

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - 'Fn::Join':
                    - ':'
                    -
                      - 'arn:aws:logs'
                      - Ref: 'AWS::Region'
                      - Ref: 'AWS::AccountId'
                      - 'log-group:/aws/lambda/*:*:*'

              # SQS
              - Effect: Allow
                Action:
                  - "sqs:DeleteMessage"
                  - "sqs:GetQueueUrl"
                  - "sqs:ChangeMessageVisibility"
                  - "sqs:ReceiveMessage"
                  - "sqs:SendMessage"
                  - "sqs:GetQueueAttributes"
                  - "sqs:SetQueueAttributes"

                Resource:
                  - "arn:aws:sqs:*"

              # Lambda * permissions
              - Effect: Allow
                Action:
                  - lambda:*
                Resource:
                  - "arn:aws:lambda:*"

              # DynamoDB * permissions
              - Effect: Allow
                Action:
                  - dynamodb:*
                Resource:
                  - "arn:aws:dynamodb:*"

              # EC2 List & Execute/start instance...
              - Effect: Allow
                Action:
                  - "ec2:DescribeInstances"
                  - "ec2:DescribeInstanceStatus"
                  - "ec2:StartInstances"
                  - "ec2:StopInstances"
                Resource:
                  - "*"

  phIamCrons:
    Type: AWS::IAM::Role
    Properties:
      Path: /performancehub/sls/crons/
      RoleName: phIamCrons-${self:provider.stage}
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: phIamCrons-${self:provider.stage}
          PolicyDocument:
            Version: '2012-10-17'
            Statement:

              # xray permissions (required)
              - Effect: "Allow"
                Action:
                  - "xray:PutTraceSegments"
                  - "xray:PutTelemetryRecords"
                Resource:
                  - "*"

              # Cloudwatch Logs...
              - Effect: Allow # note that these rights are given in the default policy and are required if you want logs out of your lambda(s)
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - 'Fn::Join':
                    - ':'
                    -
                      - 'arn:aws:logs'
                      - Ref: 'AWS::Region'
                      - Ref: 'AWS::AccountId'
                      - 'log-group:/aws/lambda/*:*:*'

              # Lambda * permissions
              - Effect: Allow
                Action:
                  - lambda:*
                Resource:
                  - "arn:aws:lambda:*"

              # DynamoDB * permissions
              - Effect: Allow
                Action:
                  - dynamodb:*
                Resource:
                  - "arn:aws:dynamodb:*"

              # We need EC2 Create network permission/access so we can attach lambda functions to VPC
              - Effect: Allow
                Action:
                  - "ec2:DescribeNetworkInterfaces"
                  - "ec2:CreateNetworkInterface"
                  - "ec2:DeleteNetworkInterface"
                  - "ec2:DescribeInstances"
                  - "ec2:AttachNetworkInterface"
                Resource:
                  - "*"
              
              # IAM Role Get and Pass
              - Effect: Allow
                Action:
                  - iam:GetRole
                  - iam:PassRole
                Resource:
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/service-role/MediaConvert_Default_Role'
              
              # MediaConvert permissions
              - Effect: Allow
                Action:
                  - mediaconvert:DescribeEndpoints
                  - mediaconvert:CreateJob
                  - mediaconvert:GetJob
                Resource: '*'

              # S3 permissions
              - Effect: Allow
                Action:
                  - s3:GetObject
                Resource:
                  - !Sub 'arn:aws:s3:::${self:provider.environment.S3_UPLOADS_BUCKET}/*'

              # S3 permissions
              - Effect: Allow
                Action:
                  - s3:*
                Resource:
                  - !Sub 'arn:aws:s3:::${self:provider.environment.S3_CCTV_RECORDING_BUCKET}'

              # Allow Access to M&P SQS Queues and also the GymMaster Member Data Sync Queue...
              - Effect: Allow
                Action:
                  - sqs:*
                Resource:
                  - "arn:aws:sqs:*"

              # Allow access to SES
              - Effect: Allow
                Action:
                  - ses:SendEmail
                  - ses:SendRawEmail
                Resource: "*"

################################################################

  releaseHistory:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Performance Hub: Core"

      TableName: ${self:provider.databases.DB_RELEASE_HISTORY}

      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: service
          AttributeType: S
        - AttributeName: date
          AttributeType: N

      KeySchema:
        - AttributeName: service
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE

  roundDisplayConfigTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Smart Club: Coaching Screens"

      TableName:  ${self:provider.databases.DB_ROUND_DISPLAY_BASE_CONFIG}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
      KeySchema:
        - AttributeName: club
          KeyType: HASH

  GmClubReportingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Performance Hub: GymMaster"
      TableName:  ${self:provider.databases.DB_GM_CLUB_REPORTING}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: date
          AttributeType: N

      # Because we're using a SQS queue to write the records into the table, and we only process the queue one item at a time - we will never have collisions using a unixtime on the sort key.
      KeySchema:
        - AttributeName: club
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE

      GlobalSecondaryIndexes:
        - IndexName: club
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  CountrySettingsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_COUNTRY_HOLIDAYS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  SMSScheduleTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_SMS_SCHEDULE_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  WorkoutBookingLeadsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_WORKOUT_BOOKING_LEADS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: club
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  ClubSocialReviewsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_CLUB_REVIEWS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  ClubSocialReviewsSettingsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_CLUB_REVIEWS_SETTINGS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  UserPreferencesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_USER_PREFERENCES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  FaqsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_FAQS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: faqId
          AttributeType: S
      KeySchema:
        - AttributeName: faqId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"

  PressTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_PRESS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: pressId
          AttributeType: S
      KeySchema:
        - AttributeName: pressId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"

  EmailMappingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_EMAIL_MAPPING_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"

  CustomTrackingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_CUSTOM_TRACKING_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: 'clubID'
          AttributeType: 'S'
      KeySchema:
        - AttributeName: 'clubID'
          KeyType: 'HASH'
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: 'Performance Hub: SLS'
        - Key: CostCenter
          Value: "Websites: B2C"

  ClubAgreementDynamoDbTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "name"
          AttributeType: "S"
        - AttributeName: "clubId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "NameIndex"
          KeySchema:
          - AttributeName: "name"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "ClubIndex"
          KeySchema:
          - AttributeName: "clubId"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_CLUB_USER_AGREEMENT}

  ClubAgreementConfigTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "clubId"
          AttributeType: "S"
        - AttributeName: "clubUserAgreementId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "ClubIndex"
          KeySchema:
          - AttributeName: "clubId"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "ClubUserAgrIndex"
          KeySchema:
          - AttributeName: "clubUserAgreementId"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_CLUB_USER_AGREEMENT_CONFIG}

  DesignRendersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"
      TableName:  ${self:provider.databases.DB_DESIGN_RENDERS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: renderedAt
          AttributeType: N
        - AttributeName: userID
          AttributeType: S

      # Because we're using a SQS queue to write the records into the table, and we only process the queue one item at a time - we will never have collisions using a unixtime on the sort key.
      KeySchema:
        - AttributeName: club
          KeyType: HASH
        - AttributeName: renderedAt
          KeyType: RANGE

      GlobalSecondaryIndexes:
        - IndexName: club
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: userID
          KeySchema:
            - AttributeName: userID
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  DesignTemplatesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Performance Hub: Marketing Print"
      TableName:  ${self:provider.databases.DB_DESIGN_TEMPLATES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: id
          AttributeType: S
        - AttributeName: designVisibility
          AttributeType: S
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: designVisibility
          KeySchema:
            - AttributeName: designVisibility
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: designVisibilityOrgId-index
          KeySchema:
            - AttributeName: designVisibility
              KeyType: HASH
            - AttributeName: organisationId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  DesignStaticTemplatesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_DESIGN_STATIC_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: id
          AttributeType: S
        - AttributeName: designVisibility
          AttributeType: S
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: club-index
          KeySchema:
            - AttributeName: club
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: designVisibility
          KeySchema:
            - AttributeName: designVisibility
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: designVisibilityOrgId-index
          KeySchema:
            - AttributeName: designVisibility
              KeyType: HASH
            - AttributeName: organisationId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  DesignVariablesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_DESIGN_VARIABLES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: type
          AttributeType: S
        - AttributeName: variable
          AttributeType: S
      KeySchema:
        - AttributeName: variable
          KeyType: HASH
        - AttributeName: club
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: type-index
          KeySchema:
            - AttributeName: type
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  DesignAssetsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_DESIGN_ASSETS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: deleted
          AttributeType: 'N'
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: deleted-index
          KeySchema:
            - AttributeName: deleted
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  DesignCustomPagesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_DESIGN_CUSTOM_PAGES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: deleted
          AttributeType: 'N'
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH 
      GlobalSecondaryIndexes:
        - IndexName: organisationId-index
          KeySchema:
            - AttributeName: organisationId
              KeyType: HASH
            - AttributeName: deleted
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  DesignInputMappingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_DESIGN_INPUT_MAPPING_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: designId
          AttributeType: S
      KeySchema:
        - AttributeName: designId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Marketing Print"

  ChargesConfigTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_CHARGES_CONFIG_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: restrictedTo
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: restricted-to-index
          KeySchema:
            - AttributeName: restrictedTo
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"

  PerfHubClubPreferencesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_CLUB_PREFERENCES_TABLE}

  PermissionsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_PERMISSIONS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  OrganisationsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_ORGANISATIONS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: organisationId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  BrandsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_BRANDS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: brandId
          AttributeType: S
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: brandId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      GlobalSecondaryIndexes:
        - IndexName: organisationId-index
          KeySchema:
            - AttributeName: organisationId
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  RolesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_ROLES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: roleId
          AttributeType: S
        - AttributeName: organisationId
          AttributeType: S
      KeySchema:
        - AttributeName: roleId
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      GlobalSecondaryIndexes:
        - IndexName: organisationId-index
          KeySchema:
            - AttributeName: organisationId
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  ClubsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_CLUBS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: lowercaseID
          AttributeType: S
        - AttributeName: gymMasterDomain
          AttributeType: S
        - AttributeName: slug
          AttributeType: S
        - AttributeName: brandId
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      GlobalSecondaryIndexes:
        - IndexName: LowercaseID
          KeySchema:
            - AttributeName: "lowercaseID"
              KeyType: "HASH"
          Projection:
            ProjectionType: ALL
        - IndexName: GMDomain
          KeySchema:
            - AttributeName: "gymMasterDomain"
              KeyType: "HASH"
          Projection:
            ProjectionType: KEYS_ONLY
        - IndexName: brand-slug-index
          KeySchema:
            - AttributeName: "brandId"
              KeyType: "HASH"
            - AttributeName: "slug"
              KeyType: "RANGE"
          Projection:
            ProjectionType: ALL

  CCTVFacilityReportTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
      - Key: Backup
        Value: true
      - Key: SchemaSource
        Value: "Performance Hub: SLS"
      - Key: CostCenter
        Value: "Smart Club: Core"

      TableName: ${self:provider.databases.DB_CCTV_FACILITY_REPORT_TABLE}

      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: clubID
          AttributeType: S
        - AttributeName: date
          AttributeType: N
        # - AttributeName: reportType
        #   AttributeType: S

      KeySchema:
        - AttributeName: clubID
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE

  SMSTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_SMS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: "twilio-id"
          AttributeType: "S"
        - AttributeName: "sendDate"
          AttributeType: "N"
        - AttributeName: "status"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "twilio-id"
          KeyType: "HASH"
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      GlobalSecondaryIndexes:
        - IndexName: StatusSendDateIndex
          KeySchema:
            - AttributeName: "status"
              KeyType: "HASH"
            - AttributeName: sendDate
              KeyType: "RANGE"
          Projection:
            ProjectionType: ALL

  DeviceAutomationsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_DEVICE_AUTOMATIONS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
      KeySchema:
        - AttributeName: club
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Smart Club: Core"

  AnnouncementTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_ANNOUNCEMENTS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  GMMemberDataTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName:  ${self:provider.databases.DB_GM_MEMBER_DATA_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: clubID
          AttributeType: S
        - AttributeName: created
          AttributeType: S
        - AttributeName: email
          AttributeType: S
        - AttributeName: gmID
          AttributeType: 'N'
        - AttributeName: phonecell
          AttributeType: S
      KeySchema:
        - AttributeName: gmID
          KeyType: HASH
        - AttributeName: clubID
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: clubID-index
          KeySchema:
            - AttributeName: clubID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: created
          KeySchema:
            - AttributeName: created
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: phonecell-index
          KeySchema:
            - AttributeName: phonecell
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: email-index
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"

  ClubBusyHoursTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "clubId"
          AttributeType: "S"
        - AttributeName: "target"
          AttributeType: "S"
            #- AttributeName: "category"
            #  AttributeType: "S"
            #- AttributeName: "created"
            #  AttributeType: "N"
      KeySchema:
        - AttributeName: "clubId"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "TargetIndex"
          KeySchema:
          - AttributeName: "target"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
              #- IndexName: "CategoryIndex"
              #  KeySchema:
              #  - AttributeName: "category"
              #    KeyType: "HASH"
              #  Projection:
              #    ProjectionType: "ALL"
              #- IndexName: "TargetDateIndex"
              #  KeySchema:
              #  - AttributeName: "target"
              #    KeyType: "HASH"
              #  - AttributeName: "created"
              #    KeyType: "RANGE"
              #  Projection:
              #    ProjectionType: "ALL"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_CLUB_BUSY_HOURS_TABLE}

  PerfHubNotificationMessagesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "target"
          AttributeType: "S"
        - AttributeName: "category"
          AttributeType: "S"
        - AttributeName: "created"
          AttributeType: "N"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "TargetIndex"
          KeySchema:
          - AttributeName: "target"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "CategoryIndex"
          KeySchema:
          - AttributeName: "category"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "TargetDateIndex"
          KeySchema:
          - AttributeName: "target"
            KeyType: "HASH"
          - AttributeName: "created"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "ALL"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_NOTIFICATION_MESSAGES_TABLE}

  PerfHubConsumedNotificationsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "email"
          AttributeType: "S"
        - AttributeName: "notificationId"
          AttributeType: "S"
        - AttributeName: "clubId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "EmailIndex"
          KeySchema:
          - AttributeName: "email"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "NotificationIndex"
          KeySchema:
          - AttributeName: "notificationId"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "ClubEmailIndex"
          KeySchema:
          - AttributeName: "clubId"
            KeyType: "HASH"
          - AttributeName: "email"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "INCLUDE"
            NonKeyAttributes: ['clubId', 'email', 'deleted', 'target', 'created']
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_CONSUMED_NOTIFICATIONS_TABLE}

  ClubKYCTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "clubId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "clubId"
          KeyType: "HASH"
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_CLUB_KYC_TABLE}

  PHSecureUploadsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "s3Key"
          AttributeType: "S"
        - AttributeName: "status"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "s3Key"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "status"
          KeySchema:
          - AttributeName: "status"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_SECURE_UPLOADS_TABLE}

  PHHistoricalClubHolidaysTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "clubID"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "clubID"
          KeySchema:
          - AttributeName: "clubID"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_HISTORICAL_CLUB_HOLIDAYS_TABLE}

  PHSaaSBillingTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "clubID"
          AttributeType: "S"
        - AttributeName: "date"
          AttributeType: "N"
        - AttributeName: "imported"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "clubAndDate"
          KeySchema:
          - AttributeName: "clubID"
            KeyType: "HASH"
          - AttributeName: "date"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "clubID"
          KeySchema:
          - AttributeName: "clubID"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
        - IndexName: "imported"
          KeySchema:
          - AttributeName: "imported"
            KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_SAAS_BILLING_TABLE}

  PHSaaSBillingConfigTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"
      AttributeDefinitions:
        - AttributeName: "target"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "target"
          KeyType: "HASH"
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_SAAS_BILLING_CONFIG_TABLE}

  PHSaaSBillingAdditionalLineItemsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "archived"
          AttributeType: "S"
        - AttributeName: "dateEffective"
          AttributeType: "N"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "archived-index"
          KeySchema:
          - AttributeName: "archived"
            KeyType: "HASH"
          - AttributeName: "dateEffective"
            KeyType: "RANGE"
          Projection:
            ProjectionType: "ALL"
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_SAAS_BILLING_ADDITIONAL_LINE_ITEMS_TABLE}

  PHSaaSBillingMonthlyRecordsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: SaaS Billing and Stripe"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "clubID"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: clubID-index
          KeySchema:
            - AttributeName: clubID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST
      TableName: ${self:provider.databases.DB_SAAS_BILLING_MONTHLY_RECORDS_TABLE}

  GmMembershipDataTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_GM_MEMBERSHIP_DATA_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: clubID
          AttributeType: S
        - AttributeName: ClubIDMembershipTypeID
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: clubID-index
          KeySchema:
            - AttributeName: clubID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: ClubIDMembershipTypeID-index
          KeySchema:
            - AttributeName: ClubIDMembershipTypeID
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"

  GmMembershipAccessTypesTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_GM_MEMBERSHIP_ACCESS_TYPES_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: GymMaster"
  
  agreementsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_AGREEMENTS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: agreementid
          AttributeType: S
      KeySchema:
        - AttributeName: agreementid
          KeyType: HASH
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  agreementsSignedTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:provider.databases.DB_AGREEMENTS_SIGNED_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: club
          AttributeType: S
        - AttributeName: agreementid
          AttributeType: 'S'
      KeySchema:
        - AttributeName: club
          KeyType: HASH
        - AttributeName: agreementid
          KeyType: RANGE
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  SaaSBillingPaymentSettingsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
      TableName: ${self:provider.databases.DB_SAAS_BILLING_PAYMENT_SETTINGS_TABLE}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: clubID
          AttributeType: S
      KeySchema:
        - AttributeName: clubID
          KeyType: HASH

  CountersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_COUNTERS_TABLE}

  LandingPagesDynamoDbTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "slug"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: "SlugIndex"
          KeySchema:
            - AttributeName: "slug"
              KeyType: "HASH"
          Projection:
            ProjectionType: "ALL"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_LANDING_PAGES_TABLE}

  DisplayUIOptionsDynamoDbTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_DISPLAY_UI_OPTIONS_TABLE}

  DisplayDataSourcesDynamoDbTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Websites: B2C"
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      # use "on-demand" scalable model for train tables so we don't need to worry about calculating throughputs...
      BillingMode: PAY_PER_REQUEST
      TableName:  ${self:provider.databases.DB_DISPLAY_DATA_SOURCES_TABLE}

  # This has moved to ScyllaDB in prod - there is no schema for this table at the moment
  # DevicesMetadataTable:

  # Schema for these tables are actually managed in Universal API API.
  # ClubAppsConfigDynamoDbTable:
  # CoachingScreensDevicesTable:

################################################################
# ElastiCache / REDIS

  PerfHubServerlessCacheSubnetGroup:
    Type: AWS::ElastiCache::SubnetGroup
    Properties:
      Description: "Cache Subnet Group"
      SubnetIds: ${self:custom.vpc_${opt:stage, self:provider.stage}.subnetIds}

  PerfHubECReplicationGrp:
    Type: AWS::ElastiCache::ReplicationGroup
    Properties:
      ReplicationGroupId: ph-prd-replication-group
      ReplicationGroupDescription: PH Redis Cluster (Prod)
      NumCacheClusters: 2
      AutomaticFailoverEnabled: true
      MultiAZEnabled: true
      AutoMinorVersionUpgrade: true
      Engine: redis
      CacheNodeType: cache.t4g.medium
      SecurityGroupIds: ${self:custom.vpc_${opt:stage, self:provider.stage}.securityGroupIds}
      CacheSubnetGroupName:
        Ref: PerfHubServerlessCacheSubnetGroup
      Tags:
        - Key: Backup
          Value: true
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

##################################################################
# S3 Buckets
  PHClubKYCBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: ${self:provider.environment.S3_CLUB_KYC_BUCKET}
      AccessControl: Private
      Tags:
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"

  PHUploadsBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    Properties:
      BucketName: ${self:provider.environment.S3_UPLOADS_BUCKET}
      AccessControl: Private
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - HEAD
              - DELETE
            AllowedOrigins:
              - "*"
      Tags:
        - Key: SchemaSource
          Value: "Performance Hub: SLS"
        - Key: CostCenter
          Value: "Performance Hub: Core"
