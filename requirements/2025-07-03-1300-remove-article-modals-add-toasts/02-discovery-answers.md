# Discovery Answers - Remove Article Modals & Add Toasts

## Q1: Should toast notifications appear for all article operations (save, insert, duplicate)?
**Answer:** Save/insert and delete for now because duplicate is found outside the wiki editor so that would be a job for another day

## Q2: Should users be able to configure toast notification preferences (show/hide, duration)?
**Answer:** No

## Q3: Should we use the existing show_notification system for article save/insert/delete operations, or migrate to SweetAlert 2 toast and update all article operations?
**Answer:** Use SweetAlert 2 toast system and migrate legacy show_notification to SweetAlert 2 toast. The legacy UI is not preferred.

## Q4: Should the existing changelog modal user preference be removed since we're moving to toasts?
**Answer:** No, we should retain the changelog modal and its user preference option. It's a modal with inputs and not a notification so it should not be affected by this change, as well as other existing modals out there - only notifications.

## Q5: Should the 500ms minimum duration apply to both successful and failed operations?
**Answer:** Yes (consistent loading states regardless of outcome)

## Additional Requirements:
- **Gradual Rollout:** Create a system or logic in legacy show_notification usages outside wiki editor to easily switch between legacy notif or swal toast
- **Test First:** Want to test the swal toast notification in wiki editor first, then decide if rolling it out globally
- **Modal Distinction:** Only notifications should be converted to toasts, not modals with inputs or complex interactions

## Key Decisions:
- Use SweetAlert 2 toast system for new notifications
- Keep changelog modal functionality unchanged
- Implement gradual rollout mechanism
- Focus on wiki editor testing first
- Remove regional visibility modal (defaults to "Global")
- Add button state management with 500ms minimum duration