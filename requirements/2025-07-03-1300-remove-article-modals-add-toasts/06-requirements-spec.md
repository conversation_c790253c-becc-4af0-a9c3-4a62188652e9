# Requirements Specification - Remove Article Modals & Add Toasts

## Problem Statement
The current article editing workflow has several UX issues:
1. **Unnecessary Regional Visibility Modal**: Shows even when not set (defaults to "Global" in backend)
2. **Inconsistent Notification Systems**: Mix of legacy bottom notifications and modern SweetAlert 2 toasts
3. **Poor Button State Management**: No disabled states or loading feedback during save operations
4. **UI Flashing**: Fast operations can cause jarring UI state changes

## Solution Overview
Replace modals with modern toast notifications and implement comprehensive button state management with gradual rollout capability.

## Functional Requirements

### FR1: Remove Regional Visibility Modal
- **FR1.1**: Remove regional visibility modal from new article creation (`openKB.js:598-621`)
- **FR1.2**: Remove regional visibility modal from article editing (`openKB.js:1281-1310`)
- **FR1.3**: No changes to existing regional visibility logic (already defaults to "Global" in backend)
- **FR1.4**: Modal was just a notice/confirmation dialog - removing modal only

### FR2: Implement Modern Toast System
- **FR2.1**: Create global Toast mixin similar to `appSwal` configuration
- **FR2.2**: Use SweetAlert 2 toast mode with consistent configuration:
  - Position: "top-end"
  - Timer: 2000ms
  - No confirm button
  - Hover pause/resume enabled
  - No backdrop animation
- **FR2.3**: Replace `show_notification` calls in article save/insert/delete operations
- **FR2.4**: Maintain internationalization support using `t()` function

### FR3: Button State Management
- **FR3.1**: Disable save/insert/delete buttons during operations
- **FR3.2**: Change button text during operations:
  - "Save" → "Saving..."
  - "Insert" → "Inserting..."
  - "Delete" → "Deleting..."
- **FR3.3**: Implement 500ms minimum duration for all button states
- **FR3.4**: Prevent double-click/multiple submissions
- **FR3.5**: Revert to normal state on operation failure to allow retry

### FR4: Gradual Rollout System
- **FR4.1**: Add configuration object to `config.json`:
  ```json
  {
    "use_modern_toasts": {
      "wiki_editor": true,
      "settings": false,
      "articles": false,
      "files": false,
      "user_management": false
    }
  }
  ```
- **FR4.2**: Modify `show_notification` function to check scope configuration
- **FR4.3**: Support scope-based switching between legacy and modern toasts
- **FR4.4**: Default to `wiki_editor: true` for initial testing phase

### FR5: Preserve Existing Functionality
- **FR5.1**: Keep changelog modal system unchanged (modal with inputs, not just notification)
- **FR5.2**: Maintain user preference for `skip_changelog_modal`
- **FR5.3**: Keep AI-powered changelog generation functionality
- **FR5.4**: Preserve all existing modals except regional visibility modal

## Technical Requirements

### TR1: File Modifications
- **TR1.1**: Update `src/public/javascripts/openKB.js`:
  - Remove regional visibility modal check/display logic only (no business logic changes)
  - Replace `show_notification` calls in article operations
  - Add button state management functions
  - Implement minimum duration logic
- **TR1.2**: Create global Toast mixin in `src/public/javascripts/app-toast.js`
- **TR1.3**: Update `src/public/javascripts/app-swal.js` to include Toast mixin
- **TR1.4**: Modify `show_notification` function for gradual rollout support

### TR2: Button Elements
- **TR2.1**: Target buttons: `#frm_edit_kb_save`, `#frm_create_kb_save`
- **TR2.2**: Add data attributes for original button text if needed
- **TR2.3**: Implement button state helper functions

### TR3: Configuration Management
- **TR3.1**: Add configuration reading logic in `src/routes/common.js`
- **TR3.2**: Make configuration available to frontend JavaScript
- **TR3.3**: Implement scope checking utility function

### TR4: Error Handling
- **TR4.1**: Maintain existing error handling patterns
- **TR4.2**: Show error toasts using same system as success toasts
- **TR4.3**: Ensure button state reset on all error conditions

## Implementation Hints

### Toast Mixin Pattern
```javascript
// src/public/javascripts/app-toast.js
const appToast = Swal.mixin({
    toast: true,
    position: "top-end",
    showConfirmButton: false,
    timer: 2000,
    timerProgressBar: false,
    didOpen: (toast) => {
        toast.addEventListener("mouseenter", Swal.stopTimer);
        toast.addEventListener("mouseleave", Swal.resumeTimer);
    },
    showClass: {
        backdrop: "swal2-noanimation",
        popup: "",
    },
});
```

### Button State Management Pattern
```javascript
// Utility functions
function setButtonState(buttonId, state, text) {
    const button = $(buttonId);
    button.prop('disabled', state === 'disabled');
    if (text) button.text(text);
}

function withMinimumDuration(asyncOperation, minDuration = 500) {
    const startTime = Date.now();
    return asyncOperation.then(result => {
        const elapsed = Date.now() - startTime;
        if (elapsed < minDuration) {
            return new Promise(resolve => 
                setTimeout(() => resolve(result), minDuration - elapsed)
            );
        }
        return result;
    });
}
```

### Scope-based Toast Usage
```javascript
function showNotification(message, type, scope = 'default') {
    const config = getConfig();
    const useModernToasts = config.use_modern_toasts && config.use_modern_toasts[scope];
    
    if (useModernToasts) {
        appToast.fire({
            icon: type === 'danger' ? 'error' : type,
            title: message
        });
    } else {
        show_notification(message, type);
    }
}
```

## Acceptance Criteria

### AC1: Regional Visibility Modal Removal
- [ ] No regional visibility modal appears during article save/insert operations
- [ ] Existing regional visibility logic remains unchanged (already defaults to "Global")
- [ ] Regional visibility functionality works when explicitly set

### AC2: Toast Notifications
- [ ] Success toasts appear for successful save/insert/delete operations
- [ ] Error toasts appear for failed operations
- [ ] Toasts use consistent positioning and timing
- [ ] Toasts support internationalization

### AC3: Button States
- [ ] Buttons show disabled state during operations
- [ ] Button text changes to "Saving..."/"Inserting..."/"Deleting..." during operations
- [ ] Minimum 500ms duration for all button states
- [ ] Buttons revert to normal state on failure
- [ ] No double-click issues

### AC4: Gradual Rollout
- [ ] Configuration controls toast system usage by scope
- [ ] Wiki editor uses modern toasts when enabled
- [ ] Other areas use legacy notifications when disabled
- [ ] Easy switching between systems via configuration

### AC5: Preserved Functionality
- [ ] Changelog modal system works unchanged
- [ ] User preferences for changelog modal respected
- [ ] AI-powered changelog generation functional
- [ ] All existing modals (except regional visibility) preserved

## Assumptions
- SweetAlert 2 library remains available and current version is compatible
- Configuration changes don't require application restart
- Existing `t()` internationalization function supports all required messages
- Backend APIs (`/api/save_kb`, `/api/insert_kb`) remain unchanged
- Current error response formats are maintained