# Detail Questions - Remove Article Modals & Add Toasts

## Phase 4: Expert Requirements Questions

Based on deep codebase analysis, I've identified the specific implementation details. Now I need to clarify exact system behavior expectations.

## Q6: Should the new SweetAlert 2 toast system use the same configuration as the existing URL copy toast (top-end position, 2s timer)?

**Default if unknown:** Yes (maintain consistency with existing SweetAlert 2 toast at `wiki-themes/12rnd/js/edit-article.js:395-415`)

Current URL copy toast config:
- Position: "top-end"
- Timer: 2000ms
- No confirm button
- Hover pause/resume enabled

## Q7: Should the gradual rollout system use a configuration flag in config.json or a feature flag in the database?

**Default if unknown:** Configuration flag in config.json (easier to manage and doesn't require database changes)

This would allow switching between legacy `show_notification` and SweetAlert 2 toast globally outside the wiki editor.

## Q8: When saving fails, should the button state remain disabled or revert to normal to allow retry?

**Default if unknown:** Revert to normal (allow users to retry failed saves)

Currently there's no button state management, so this is a new behavior decision.

## Q9: Should article delete operations use the same toast notification system as save/insert?

**Default if unknown:** Yes (consistent user experience across all article operations)

You mentioned "save/insert and delete" operations should use toasts.

## Q10: Should the minimum 500ms duration apply even when the server responds faster than 500ms?

**Default if unknown:** Yes (prevents UI flashing and provides consistent user experience)

This would ensure users always see the "Saving..." state for at least 500ms even on very fast connections.