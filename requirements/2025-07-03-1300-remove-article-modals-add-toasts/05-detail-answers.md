# Detail Answers - Remove Article Modals & Add Toasts

## Q6: Should the new SweetAlert 2 toast system use the same configuration as the existing URL copy toast (top-end position, 2s timer)?
**Answer:** Yes, in fact prefer if we have a global Toast mixin like we do in appSwal so we don't have redundant Toast config in every file that uses it

## Q7: Should the gradual rollout system use a configuration flag in config.json or a feature flag in the database?
**Answer:** Configuration flag in config.json but instead of boolean use an object with {scope_name: boolean} like {wiki_editor: true, settings: false} etc...

## Q8: When saving fails, should the button state remain disabled or revert to normal to allow retry?
**Answer:** Revert to normal

## Q9: Should article delete operations use the same toast notification system as save/insert?
**Answer:** Yes

## Q10: Should the minimum 500ms duration apply even when the server responds faster than 500ms?
**Answer:** Yes

## Final Configuration Design:
```json
{
  "use_modern_toasts": {
    "wiki_editor": true,
    "settings": false,
    "articles": false,
    "files": false,
    "user_management": false
  }
}
```

## Key Implementation Details:
- **Global Toast Mixin**: Create similar to appSwal configuration
- **Scope-based Rollout**: Granular control over which areas use modern toasts
- **Button State Management**: Disabled during operation, revert on failure
- **Minimum Duration**: 500ms minimum for all button states
- **Consistent Experience**: Same toast config and behavior across all operations