# Discovery Questions - Remove Article Modals & Add Toasts

## Phase 2: Context Discovery Questions

Based on the codebase analysis, I've identified key files and systems:
- Regional visibility modal in `wiki-themes/12rnd/js/edit-article.js`
- Changelog modal system with AI in `src/public/javascripts/openKB.js`
- User preference system in `wiki-themes/12rnd/js/settings.js`
- Article save/insert APIs in `src/routes/api.js`

## Q1: Should toast notifications appear for all article operations (save, insert, duplicate)?
**Default if unknown:** Yes (consistent user experience across all article operations)

## Q2: Should users be able to configure toast notification preferences (show/hide, duration)?
**Default if unknown:** No (keep it simple with sensible defaults)

## Q3: Should the toast notifications include additional actions (like "View Article" or "Undo")?
**Default if unknown:** No (simple success/error notifications are sufficient)

## Q4: Should the existing changelog modal user preference be removed since we're moving to toasts?
**Default if unknown:** No (users who still want changelog modals should keep that option)

## Q5: Should the 500ms minimum duration apply to both successful and failed operations?
**Default if unknown:** Yes (consistent loading states regardless of outcome)

## Notes:
- Regional visibility modal removal is straightforward since it defaults to "Global"
- Existing user preference system can be leveraged for any new settings
- Toast implementation will need to integrate with existing error handling
- Button state management needs to work with both new and existing articles