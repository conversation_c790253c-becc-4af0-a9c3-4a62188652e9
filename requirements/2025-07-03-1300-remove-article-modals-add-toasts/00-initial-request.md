# Initial Request - Remove Article Modals & Add Toasts

## User Request
Can we remove the article regional visibility showing up if it's not set? In frontend and backend we treat not set regional visibility as "Global", so I think we may just remove that modal entirely.

Also while we're talking about modals, we have newly added user preference option for showing/not showing changelog summary modal (with AI). If user has opt to hide the changelog summary modal when saving an article, it means (after we remove the article regional visibility modal) we might not even need to show modals after saving an article.

A toast notification in the bottom right would suffice I think? A toast notification for saving article and when it has been successfully saved.

Also on save/insert button click, we should disable those buttons and maybe set the button text into "Saving..."/"Inserting..." based on the status, and maybe can we add a minimum duration for the saving/inserting state to be like 500ms because we don't want flashes of UI if articles is saved too quickly.

## Key Components Identified
- Article regional visibility modal
- Changelog summary modal with AI
- User preference system
- Save/Insert button states
- Toast notification system
- Article save/insert workflow

## Requested Changes
1. Remove article regional visibility modal (defaults to "Global")
2. Replace modals with toast notifications after article save
3. Add button state management during save/insert operations
4. Implement minimum 500ms duration for saving states
5. Disable save/insert buttons during operations
6. Change button text to "Saving..."/"Inserting..." during operations

## Date Created
2025-07-03 13:00 UTC