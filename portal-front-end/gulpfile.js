//'use strict';

const plugins       = require('gulp-load-plugins');
const yargs         = require('yargs');
const browser       = require('browser-sync');
const gulp          = require('gulp');
const panini        = require('panini');
const rimraf        = require('rimraf');
const yaml          = require('js-yaml');
const fs            = require('fs');
const webpackStream = require('webpack-stream');
const webpack2      = require('webpack');
const htmlmin       = require('gulp-htmlmin');
const gutil         = require('gulp-util');
const cacheBuster   = require('gulp-cachebust');
const concat        = require('gulp-concat');
const sourcemaps    = require('gulp-sourcemaps');
// const minify        = require('gulp-minify');
const terser        = require('gulp-terser');
const git           = require('git-rev-sync');
const replace       = require('gulp-replace');
const merge         = require('merge-stream');
const sass          = require('gulp-sass')(require('node-sass'));
const run           = require('gulp-run');

let cachebust       = new cacheBuster();

// Load all Gulp plugins into one variable
const $ = plugins();

// Check for --production flag
const PRODUCTION = !!(yargs.argv.production);

// Load settings from settings.yml
let { COMPATIBILITY, PORT, UNCSS_OPTIONS, PATHS } = loadConfig();
const { languages } = loadLanguageConfig();

function loadConfig() {
    let ymlFile = fs.readFileSync('config.yml', 'utf8');
    return yaml.load(ymlFile);
}

function loadLanguageConfig() {
    const ymlFile = fs.readFileSync('language.yml', 'utf8');
   
    return yaml.load(ymlFile);
}

// Build the "dist" folder by running all of the below tasks
gulp.task('build',
    gulp.series(
        clean,
        gulp.parallel(
            sassVendor, sassCore, copy, images, jsLibraries, jsCode, jsNoBundles, fontsAndSVGs, pageRoutes, metadataFiles
        ),
        copyLanguages,
        pages,
        buildTranslations
    )
);

// Build the site, run the server, and watch for file changes
gulp.task('default',
    gulp.series('build', server, watch));

// Delete the "dist" folder
// This happens every time a build starts
function clean(done) {
    rimraf('{' + PATHS.dist + '}', done);
}

async function copyLanguages() {
    const json = JSON.stringify({ languages }, null, 2);
    return await fs.writeFileSync('./dist/assets/data/languages.json', json);
}

// Copy in Fonts ...
function fontsAndSVGs() {

    let grapejs = gulp.src('node_modules/grapesjs/dist/fonts/**/*')
        .pipe(gulp.dest(PATHS.dist + '/assets/fonts'));

    let fa = gulp.src('node_modules/@fortawesome/fontawesome-free/webfonts/**/*')
        .pipe(gulp.dest(PATHS.dist + '/assets/fonts'));

    let tuiIcons = gulp.src('node_modules/tui-image-editor/dist/svg/**/*')
        .pipe(gulp.dest(PATHS.dist + '/assets/images/svg'));

    let lightgalleryjs = gulp.src('node_modules/lightgallery/fonts/**/*')
        .pipe(gulp.dest(PATHS.dist + '/assets/fonts'));

    let countryFlags = gulp.src('node_modules/country-flag-icons/flags/3x2/**/*')
            .pipe(gulp.dest(PATHS.dist + '/assets/images/flags'));

    return merge(grapejs, fa, tuiIcons, lightgalleryjs, countryFlags);

}

// Copy files out of the assets folder
// This task skips over the "images", "js", and "scss" folders, which are parsed separately
function copy() {
    return gulp.src(PATHS.assets)
        .pipe(gulp.dest(PATHS.dist + '/assets'));
}

function pages() {
    return gulp.src([
            'src/pages/**/*.{html,hbs,handlebars}',
            '!src/pages/routes/**/*.{html,hbs,handlebars}'
        ])
        .pipe(panini({
            root: 'src/pages/',
            layouts: 'src/layouts/',
            partials: 'src/partials/',
            data: 'src/data/',
            helpers: 'src/helpers/'
        }))
        // Inject version revision into template...
        .pipe(replace('%%GIT_REV_VERSION%%', (git.branch() + "/" + git.short()).replace("Detached:", "") ))
        .pipe(htmlmin({
            collapseWhitespace: true,
            removeComments: true
        }))
        .pipe($.if(PRODUCTION, cachebust.references() ))
        .pipe(gulp.dest(PATHS.dist));
}

function metadataFiles() {
    return gulp.src([
            'src/assets/metadata/**/*'
        ])
        // Inject version revision into any files with the reference...
        .pipe(replace('%%GIT_REV_VERSION%%', '"' + (git.branch() + "/" + git.short()).replace("Detached:", "") + '"' ))
        .pipe(gulp.dest(PATHS.dist + '/assets/metadata'));
}

function pageRoutes() {
    return gulp.src([
            'src/pages/routes/**/*.{html,hbs,handlebars}'
        ])
        .pipe(htmlmin({
            collapseWhitespace: true,
            removeComments: true
        }))
        .pipe(gulp.dest(PATHS.dist + '/routes'));
}


// Load updated HTML templates and partials into Panini
function resetPages(done) {
    panini.refresh();
    done();
}

// Compile Sass into CSS
// In production, the CSS is compressed
function sassCore() {
    return gulp.src('src/scss/styles/styles.scss')
        .pipe($.sourcemaps.init())
        .pipe(sass({
            includePaths: PATHS.sass,
            // outputStyle: 'compressed'
        })
        .on('error', sass.logError))
        .pipe($.autoprefixer({
            browsers: COMPATIBILITY
        }))
        // Comment in the pipe below to run UnCSS in production
        //.pipe($.if(PRODUCTION, $.uncss(UNCSS_OPTIONS)))
        .pipe($.if(PRODUCTION, $.cleanCss({ compatibility: 'ie9' })))
        .pipe($.if(!PRODUCTION, $.sourcemaps.write()))
        .pipe($.if(PRODUCTION, cachebust.resources() ))
        .pipe(gulp.dest(PATHS.dist + '/assets/css'))
        .pipe(browser.reload({ stream: true }));
}

function sassVendor() {
    return gulp.src('src/scss/vendor/vendor.scss')
        .pipe($.sourcemaps.init())
        .pipe(sass({
            includePaths: PATHS.sass,
            // outputStyle: 'compressed'
        })
        .on('error', sass.logError))
        .pipe($.autoprefixer({
            browsers: COMPATIBILITY
        }))
        // Comment in the pipe below to run UnCSS in production
        //.pipe($.if(PRODUCTION, $.uncss(UNCSS_OPTIONS)))
        .pipe($.if(PRODUCTION, $.cleanCss({ compatibility: 'ie9' })))
        .pipe($.if(!PRODUCTION, $.sourcemaps.write()))
        .pipe($.if(PRODUCTION, cachebust.resources() ))
        .pipe(gulp.dest(PATHS.dist + '/assets/css'))
        .pipe(browser.reload({ stream: true }));
}

function jsLibraries() {
    // JS Libraries from github etc (configured via config.yml)...
    return gulp.src(PATHS.jsLibs)
        .pipe(sourcemaps.init())
        .pipe(concat('libraries.js'))
        .pipe(terser())
        .pipe($.if(PRODUCTION, cachebust.resources() ))
        .pipe(gulp.dest(PATHS.dist + '/assets/js'));
}


// function jsLibraries() {
//     // JS Libraries from github etc (configured via config.yml)...
//     return gulp.src(PATHS.jsLibs)
//         .pipe(sourcemaps.init())
//         .pipe(concat('libraries.js'))
//         .pipe(minify({}))
//         .pipe($.if(PRODUCTION, cachebust.resources() ))
//         .pipe(gulp.dest(PATHS.dist + '/assets/js'));
// }


function jsCode() {
    // Main JS Application
    return gulp.src('src/js/**/*.js')
        .pipe(sourcemaps.init())
        .pipe(concat('app.js'))
        .pipe($.if(!PRODUCTION, $.sourcemaps.write()))
        .pipe($.if(PRODUCTION, terser()))
        .pipe($.if(PRODUCTION, cachebust.resources() ))
        .pipe(gulp.dest(PATHS.dist + '/assets/js'));
}


// function jsCode() {
//     // Main JS Application
//     return gulp.src('src/js/**/*.js')
//         .pipe(sourcemaps.init())
//         .pipe(concat('app.js'))
//         .pipe($.if(!PRODUCTION, $.sourcemaps.write()))
//         .pipe($.if(PRODUCTION, $.minify({})))
//         .pipe($.if(PRODUCTION, cachebust.resources() ))
//         .pipe(gulp.dest(PATHS.dist + '/assets/js'));
// }

function jsNoBundles() {
    // Main JS Application
    return gulp.src(['src/js.no-bundling/**/*.js', ...PATHS.jsNoBundles])
        .pipe(sourcemaps.init())
        .pipe($.if(!PRODUCTION, $.sourcemaps.write()))
        .pipe($.if(PRODUCTION, terser()))
        .pipe($.if(PRODUCTION, cachebust.resources() ))
        .pipe(gulp.dest(PATHS.dist + '/assets/js'));
}


// Copy images to the "dist" folder
// In production, the images are compressed
function images() {
    return gulp.src('src/assets/images/**/*')
        //.pipe($.if(PRODUCTION, $.imagemin({
        //    progressive: true
        //})))
        .pipe(gulp.dest(PATHS.dist + '/assets/images'));
}

// Start a server with BrowserSync to preview the site in
function server(done) {

    var bsOptions = {
        open: false,
        // hide "connected to browsersync" notification every time a page loads...
        notify: false,
        socket: {
            path: "/browsersync/socketio",
            clientPath: "/browsersync",
            namespace: "/browsersync",
            clients: {
                heartbeatTimeout: 1000
            }
        }
    };

    if(process.env.BS_PROXY) {
        bsOptions.proxy = {
            target: process.env.BS_PROXY,
            // Proxy websockets to BS_PROXY host also...
            ws: true
        }
    } else {
        bsOptions.server = PATHS.dist
    }

    browser.init(bsOptions);
    done();
}

// Reload the browser with BrowserSync
function reload(done) {
    browser.reload();
    done();
}

// Stream changes (CSS) into the browser
function streamChanges(done) {
    browser.reload({ stream: true});
    done();
}

function reloadConfig() {
    let { COMPATIBILITY, PORT, UNCSS_OPTIONS, PATHS } = loadConfig();
}

function buildTranslations() {
    return run('node node_modules/application-translator/dist/cli.js').exec();
}

// Watch for changes to static assets, pages, Sass
function watch() {
    gulp.watch(PATHS.assets, copy);
    gulp.watch('src/pages/**/*.html').on('all', gulp.series(pages, pageRoutes, reload));
    gulp.watch('src/{layouts,partials}/**/*.html').on('all', gulp.series(resetPages, pages, pageRoutes, reload));
    gulp.watch('src/scss/**/*.scss').on('all', gulp.series(sassCore, sassVendor, pages, pageRoutes, streamChanges));
    gulp.watch('src/js/**/*.js').on('all', gulp.series(jsCode, pages, pageRoutes, reload));
    gulp.watch('src/js.no-bundling/**/*.js').on('all', gulp.series(jsNoBundles, pages, pageRoutes, reload));
    gulp.watch('src/assets/images/**/*').on('all', gulp.series(images, reload));
    gulp.watch('config.yml').on('all', gulp.series(reloadConfig));
    gulp.watch(['language.yml','csv/*.csv']).on('all', gulp.series(buildTranslations, reload));
}