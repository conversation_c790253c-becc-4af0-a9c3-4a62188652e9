@import '_variables';
@import '_mixins';
@import '_utils';

// Pre-requisite theme styles that is used by some of the components
@import 'themes/theme-ph.scss';

// Various components used throughout the application...
@import 'components/_materialicons';
@import 'components/_navbar';
@import 'components/_helpers.scss';
@import 'components/_customanimate.scss';
@import 'components/_media.scss';
@import 'components/_general.scss';
@import 'components/_buttons.scss';
@import 'components/_tagsinput.scss';
@import 'components/_noUISlider.scss';
@import 'components/_multiselect.scss';
@import 'components/_card.scss';
@import 'components/_infobox.scss';
@import 'components/_alerts.scss';
@import 'components/_dialogs.scss'; // Sweetalert
@import 'components/_checkboxradio.scss';
@import 'components/_switch.scss';
@import 'components/_datetimepicker.scss';
@import 'components/_bootstrapselect.scss';
@import 'components/_tooltippopovers.scss';
@import 'components/_navtabs.scss';
@import 'components/_thumbnails.scss';
@import 'components/_modals.scss';
@import 'components/_labels.scss';
@import 'components/_collapse.scss';
@import 'components/_tables.scss';
@import 'components/_panels.scss';
@import 'components/_progressbars.scss';
@import 'components/_ionrangeslider.scss';
@import 'components/_inputformgroup.scss';
@import 'components/_breadcrumbs.scss';
@import 'components/_badgelistgroupitem.scss';
@import 'components/_pagination.scss';
@import 'components/_mediaobject.scss';
@import 'components/_waveseffect.scss';
@import 'components/_navbar.scss';
@import 'components/_dropdownmenu.scss';
@import 'components/_bootstrapnotify.scss';
@import 'components/_leftsidebaroverlay.scss';
@import 'components/_jquerynestable.scss';
@import 'components/_charts.scss';
@import 'components/_datatables.scss';
@import 'components/_datatables-fa.scss';
@import 'components/_select2.scss';
@import 'components/_tour.scss';
@import 'components/_pinned-footer.scss';
@import 'components/_bootstraptags.scss';
@import 'components/font-inspector.scss';
@import 'components/_typeahead.scss';
@import 'components/_dropzone.scss';
@import 'components/_signal-strength.scss';
@import 'components/_daterangepicker.scss';
@import 'components/_searchbarheader.scss';
@import 'components/_algolia.scss';

// Components we are not currently using but don't want to delete from project...
/*
@import '_colorpicker.scss';
@import '_formwizard.scss';
@import '_pageloader.scss';
@import '_preloaders.scss';
@import '_searchbar.scss';
@import '_lightgallery.scss';
@import '_demo.scss';
*/

// Pages: ======================================
// Error Pages:
@import 'pages/_404.scss';
@import 'pages/_500.scss';

// Core Portal Pages
@import 'pages/dashboard.scss';
@import 'pages/club-details.scss';
@import 'pages/club-analytics.scss';
@import 'pages/club-integrations.scss';
@import 'pages/shop.scss';
@import 'pages/admin-portal.scss';
@import 'pages/designer.scss';
@import 'pages/workouts.scss';
@import 'pages/all-members.scss';
@import 'pages/announcements.scss';
@import 'pages/screens.scss';
@import 'pages/program-builder-store-iframes.scss';
@import 'pages/bulk-sms.scss';
@import 'pages/agreements-admin.scss';
@import 'pages/agreements.scss';
@import 'pages/training-camp.scss';
@import 'pages/holiday-management.scss';
@import 'pages/workout-booking-leads.scss';
@import 'pages/review-management.scss';
@import 'pages/support-ticketing.scss';
@import 'pages/website-management.scss';
@import 'pages/club-charges-and-fees.scss';
@import 'pages/member-payments.scss';
@import 'pages/wiki.scss';
@import 'pages/kanban.scss';
@import 'pages/notifications.scss';
@import 'pages/notifications-side-menu.scss';
@import 'pages/cctv-ui.scss';
@import 'pages/coaching-screens.scss';
@import 'pages/health-check.scss';
@import 'pages/facility-variables.scss';
@import 'pages/club-kyc.scss';
@import 'pages/kyc-contact.scss';
@import 'pages/device-management-screen.scss';
@import 'pages/secure-uploader.scss';
@import 'pages/release-history.scss';
@import 'pages/saas-billing.scss';
@import 'pages/audio-ui.scss';
@import 'pages/store-v2.scss';
@import 'pages/saas-billing-admin.scss';
@import 'pages/member-agreements.scss';
@import 'pages/designer-admin.scss';
@import 'pages/display-admin.scss';

// Legacy Browser Support...
@import 'browser-support/_ie10.scss';
@import 'browser-support/_ie11.scss';

// LASTLY SELECT THE THEME WE WANT TO RUN WITH:
// * AdminBSB Themes. You can choose a theme from css/themes instead of get all themes
@import 'themes/_all-themes.scss';

// Custom overflow styles for the main 'body' to ensure no horizontal scrollbars are shown...
body {
    overflow-x: hidden;
    // Sweetalert2 adds "padding right: 15px" when ever it opens...
    // https://github.com/sweetalert2/sweetalert2/issues/678
    padding-right: 0 !important;
}

// html {
//     font-size: 16px;
// }

@import '_custom';
