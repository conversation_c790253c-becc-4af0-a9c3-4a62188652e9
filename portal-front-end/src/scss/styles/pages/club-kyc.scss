#club-kyc-page {
    .kyc-containers {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .contact-card {
        background-color: #f7fafc;
        width: auto;

        @include respond-below(sm) {
            max-width: unset;
        }
    }

    .stripe-details-card {
        .col-sm-2,
        .col-sm-4 {
            margin-bottom: 10px;
            letter-spacing: 0.2px;
        }
        .col-sm-2 {
            color: #687385;
        }
        .col-sm-4 {
            color: #414552;
        }
        .bank-account-container {
            border: 2px solid #e4e4e4;
            border-radius: 5px;
            padding: 10px;
            margin: 5px;

            .col-xs-3,
            .col-xs-12,
            .col-xs-6 {
                margin-bottom: 10px;
                letter-spacing: 0.2px;
            }
            .col-xs-3 {
                min-width: 150px;
            }
        }
    }

    .details-body {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 2rem;
        justify-content: space-between;
        align-items: start;

        @include respond-below(sm) {
            flex-direction: column;
        }
    }

    .details-row {
        grid-column: span 2 / span 2;
        width: 100%;
        display: grid;
        grid-template-columns: subgrid;
        gap: 2rem;
    }

    .details-row_item {
        grid-column: span 1 / span 1;
    }

    .details-col {
        width: 100%;
        min-width: 20rem;
        flex: 1 0;
        display: grid;
        gap: $base-card-padding / 2;
    }

    .details-col_wrapper {
        width: calc(50% - 1rem);
        display: grid;
        flex-direction: row;
        justify-content: start;

        @include respond-below(md) {
            width: 100%;
        }
    }

    .details-col-payout-wrapper {
        width: calc(50% - 1rem);
        display: flex;
        flex-direction: column;

        .details-row {
            display: flex;
        }

        @include respond-below(sm) {
            width: 100%;
        }
    }

    .details-row_item--label {
        font-size: 14px;
        font-weight: 500;
        color: $slate-500;
        min-height: 34px;
    }

    .details-row_item--label-wrapper {
        display: flex;
        flex-direction: row;
        align-items: start;
        justify-content: start;
    }

    .details-row_item--value {
        color: $slate-600;
        font-weight: 600;
        display: flex;
        align-items: start;
        flex-direction: column;
        justify-content: center;
        min-height: 34px;

        .text-danger.ph-text {
            font-size: 10px;
            background-color: $red-75;
            padding: 4px 8px;
            border-radius: 4px;
            font-style: normal;
        }
    }

    .details-row_item--accounts {
        grid-column: span 2 / span 2;
    }

    .details-row_item--icon {
        position: absolute;
        top: 50%;
        transform: translate(100%, -50%);
        font-size: 14px;
        right: 0;
        color: $slate-400;
    }

    .details-row_item--label {
        max-width: 24rem;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 4px;

        .material-icons {
            font-size: 14px;
            color: $slate-400;
        }
    }

    .details-row-accounts {
        display: flex;
        gap: 4px;
    }

    .ph-contact-card {
        position: relative;
        min-width: 300px;
        background-color: white;
        border: 1px solid $surface-75;
        box-shadow: 1px 2px 6px 0px rgba(62, 78, 87, 0.06);
        border-radius: 8px;
        padding: 8px;

        .__icon_edit {
            cursor: pointer;
            padding: 3px 6px;
            transition: background-color 0.2s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;

            i {
                color: $slate-450;
            }

            &:hover {
                background-color: $slate-150;
            }
        }

        .__name {
            margin: 0;
            font-size: 18px;
            color: $slate-600;
        }

        .__role {
            width: auto;
            font-size: 12px;
            background-color: $slate-150;
            line-height: 0.8em;
            text-transform: capitalize;
            display: flex;
            align-items: center;
        }

        strong {
            color: $slate-600;
        }
    }

    .ph-contact-card_info_item {
        font-size: 14px;
        color: $slate-600;
        font-weight: 600;
        display: flex;
        gap: 4px;
        align-items: center;

        .material-icons {
            font-size: 14px;
            color: $slate-400;
        }
    }

    .ph-contact-card_name_wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
        width: 100%;
    }

    .ph-contact-card_header {
        background-color: $slate-50;
        border-radius: 8px;
        padding: 8px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: start;
    }

    .ph-contact-card_info {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        align-items: start;
        margin-top: 1rem;

        .details-row {
            display: grid !important;
        }
    }

    .ph-bank-card {
        .ph-badge.badge-grey {
            background-color: $slate-150;
        }
    }

    .details-row_item--accounts-list {
        flex-direction: row;
        gap: 1.5rem;
        flex-wrap: wrap;
        justify-content: start;
    }

    .ph-card_contacts {
        @include respond-below(xs) {
            flex-direction: column;
        }
    }
}

#kyc-contact-page {
    @include respond-below(md) {
        // .pinned-footer {
        //     width: 100vw;
        //     left: 0px;
        //     position: absolute;
        //     padding: 10px;
        //     margin-top: -27px;
        // }
    }
}

.kyc-loader {
    display: none;
}

.isLoading {
    .card {
        display: none !important;
    }

    .card.kyc-loader {
        display: block !important;
    }
}
