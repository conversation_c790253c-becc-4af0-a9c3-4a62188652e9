section.analytics-page {
    #reportrange {

        background: #fff;
        // color: #1d3b4a;
        padding: 8px;
        line-height: 18px;
        border-radius: 0px;
        border-width: 0 1px 4px;
        border: 1px solid #ddd;
        cursor: pointer;
        float: right!important;

        //box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16), 0 2px 10px rgba(0, 0, 0, 0.12);
        border-bottom: 4px solid #eee;
        .material-icons {
            font-size: 15px;
            position: absolute;
        }
        span {
            padding-left: 23px;
            padding-right: 5px;
        }
        @include respond-below(sm) {
            margin-top: 15px;
        }

    }
    .ga-container {
        padding: 10px;
        margin-bottom: 10px;
        .ga-container-border-l {
            border-left: 1px solid $border-color;
        }
        .ga-info-icon {
            font-size: 14px;
            margin-top: -2px;
            position: absolute;
            margin-left: 2px;
        }
        #pageview-graph-container, #ga_bounce_rate, #ga_lead_events {
            min-height: 300px;
            overflow: hidden;
        }
        #ga_pageview_tables, #ga_social_overview {
            min-height: 72px;
        }
        #ga_referrers, #ga_channels, #ga_cities {
            min-height: 250px;
        }
    }
    .header.analytics {
        background-color: white;
        h2 {
            padding-top: 10px;
        }
    }

    .analytics-title {
        font-size: 20px;
        font-weight: 600;
        color: $slate-600;
    }

    .analytics-date-picker {
        position: relative;
        margin-top: -10px;
        margin-left: auto;
    }
}