.club-management-page {
    .club-id-container,
    .place-id-container {
        #club_id,
        #place_id {
            padding: 5px;
        }
        &:has(code:empty) {
            display: none;
        }
    }
    .info-icon {
        font-size: 14px;
    }

    .row.display-flex {
        display: flex;
        flex-wrap: wrap;
    }
    .row.display-flex > [class*='col-'] {
        display: flex;
        flex-direction: column;
    }
    .inputs-bottom  > div[class*='col-'] {
        justify-content: space-between;
    }

    .club-resources-home {
        a:hover {
            text-decoration: none;
        }
        div[class^='col-']:hover {
            .card {
                .header {
                    transition: 0.25s;
                    background-color: #03a9f4 !important;
                    h2 {
                        color: #fff;
                    }
                }
            }
        }
        div[class^='col-'] {
            .card {
                .header {
                    background-color: #f1f1f1;
                }
            }
        }
    }
    .club-hours-block {
        overflow-x: auto;
    }
    .input-hours {
        border: 1px solid #e8e8e8;
        padding: 10px;
    }
    .save-overlay {
        display: none;
        width: 100vw;
        height: 100vh;
        top: 0px;
        left: 0px;
        background-color: rgba(100, 100, 100, 0.3);
        position: fixed;
        z-index: 9999;

        .spinner-container {
            position: fixed;
            opacity: 1;
            width: 500px;
            padding: 20px;
            height: 200px;
            background-color: white;
            text-align: center;
            margin-top: 200px;
            left: calc(50% - 250px);
            .preloader {
                margin-top: 10px;
            }
        }
    }
    .preload-text {
        padding: 100px;
        text-align: center;
        font-weight: 600;
        font-size: 20px;
    }
    .club-details {
        // display: none;

        #clubLocationMap {
            height: 400px;
            width: 100%;
        }
        .club-hours-input {
            width: 100px;
            margin-bottom: 0px;
        }
        .float-left {
            float: left;
        }

        padding-bottom: 80px;
    }
    .multiple_emails-container {
        border: none;
    }
    #club-opening-hours,
    #extended-access-hours {
        display: flex;
        flex-wrap: wrap;
        gap: 2rem;

        .club-hour,
        .extended-hour {
            flex: 1;
            max-width: 170px;
            flex-grow: 1;

            .title {
                font-weight: 700;
                text-transform: capitalize;
            }

            .tags {
                margin-bottom: 0.5rem;
            }
        }
    }
    #default-club-blocks {
        label {
            margin-bottom: 0;
        }
        .tags {
            margin-bottom: 5px;
        }
        .input-default-hours {
            span {
                display: flex;
                align-items: flex-start;
                gap: 2px;

                i {
                    font-size: 16px;
                }
            }
            &:hover,
            &:focus {
                text-decoration: none;
            }
        }
    }
    #special-club-hours {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        padding: 1px;
        padding-bottom: 1rem;

        .special-hour-container,
        .add-special-hour-container {
            width: Min(225px, 100%);

            &.deleted {
                display: none;
            }
            .card {
                height: 100%;
                margin-bottom: 0px;

                &.add-special-hour {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: background-color 100ms ease-in;
                    color: #00b0e4;
                    min-height: 100px;
                    margin-top: 2rem;

                    i {
                        font-size: 5rem;
                        transition: font-size 100ms ease-in;
                    }

                    &:hover {
                        background-color: rgba(0, 0, 0, 0.05);

                        i {
                            font-size: 7rem;
                        }
                    }
                }

                &.unreviewed::before {
                    content: '';
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    animation: holiday-pulse 2s infinite;
                    pointer-events: none;

                    @keyframes holiday-pulse {
                        0% {
                            box-shadow: 0 0 0 0 rgba(0, 176, 228, 0.4);
                        }
                        70% {
                            box-shadow: 0 0 0 10px rgba(0, 176, 228, 0);
                        }
                        100% {
                            box-shadow: 0 0 0 0 rgba(0, 176, 228, 0);
                        }
                    }
                }

                .body {
                    padding: 12px;

                    .title-container {
                        display: flex;
                        align-items: flex-start;
                        justify-content: space-between;
                        margin-bottom: 5px;

                        .title {
                            font-weight: 700;
                        }
                        .dropdown {
                            i {
                                font-size: 16px;
                                cursor: pointer;
                            }
                            .dropdown-menu {
                                right: 20px;
                                top: 1.75rem;
                                left: unset;

                                .dropdown-item {
                                    display: block;
                                    padding: 0.5rem;
                                    text-decoration: none;
                                    color: unset;

                                    &:hover {
                                        background-color: rgba(0, 0, 0, 0.05);
                                    }
                                }
                            }
                        }
                    }
                    .tags {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 0.5rem;
                        margin-bottom: 1rem;
                    }
                }
            }
        }
    }
    #special-club-hours,
    #club-opening-hours,
    #default-club-blocks,
    #extended-access-hours {
        &::-webkit-scrollbar {
            height: 1rem;
        }

        &::-webkit-scrollbar-thumb {
            background: rgb(202, 200, 200);
            border-radius: 15px;
        }

        ul.block-list {
            padding-left: 20px;
            margin-bottom: 0px;
            min-width: 170px;

            li > div {
                display: flex;
                align-items: flex-start;
                gap: 1rem;
                cursor: pointer;

                i {
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: 700;
                    display: none;
                    transform: translateY(2.5px);
                }
                span:hover,
                span:focus {
                    text-decoration: underline;
                }
            }

            li:hover {
                i {
                    display: unset;
                }
            }
        }

        .input-special-hours,
        .input-club-hours,
        .input-extended-hours {
            span {
                display: flex;
                align-items: flex-start;
                gap: 0.5rem;

                i {
                    font-size: 16px;
                }
            }
            &:hover,
            &:focus {
                text-decoration: none;
            }
        }
    }
    .pinned-footer {
        bottom: -200px;
        transition: bottom 0.5s;

        &.active {
            bottom: 0px;
        }
    }
    #single-images {
        display: flex;
        flex-wrap: wrap;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    #additional-images {
        margin-bottom: 2rem;

        .image-list {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }
    }

    #single-images,
    #additional-images {
        .image-container {
            position: relative;

            .image {
                margin: 0;
                height: 200px;
                width: 200px;
                cursor: pointer;

                @include respond-below(sm) {
                    width: 100%;
                    height: unset;
                }
            }

            &.empty {
                .image {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #00b0e4;
                    transition: background-color 100ms ease-in;

                    i {
                        font-size: 5rem;
                        transition: font-size 100ms ease-in;
                    }

                    &:hover {
                        background-color: rgba(0, 0, 0, 0.05);

                        i {
                            font-size: 7rem;
                        }
                    }
                }
            }

            &:not(.empty) img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            &:hover {
                .update-image,
                .delete-image,
                .star-image {
                    opacity: 1;
                }
            }

            &[starred='starred'] {
                .star-image {
                    fill: gold;
                    opacity: 1;
                }
            }

            &[starred='unstarred'] {
                .star-image {
                    fill: #fff;
                    opacity: 1;
                }
            }

            .update-image,
            .delete-image {
                position: absolute;
                right: 1px;
                bottom: 1px;
                font-size: 2rem;
                transition: color 0.2s, opacity 0.2s;
                cursor: pointer;
                padding: 3px;
                background-color: white;
                box-shadow: 0 0px 5px rgba(0, 0, 0, 0.1);
                border-radius: 15%;
                opacity: 0;
            }

            .star-image {
                position: absolute;
                left: 1px;
                bottom: 1px;
                transition: fill 0.2s, opacity 0.2s;
                cursor: pointer;
                opacity: 0.8;
            }

            .update-image {
                &:hover {
                    color: #2195f3;
                }
            }
            .delete-image {
                &:hover {
                    color: #dd3232;
                }
            }
        }
    }
    .payment-methods {
        display: flex;
        flex-wrap: wrap;
        gap: 2rem;
        margin-bottom: 1rem;
        padding: 10px;

        .select-payment-methods {
            .card {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 31px;
                width: 45px;
                color: #00b0e4;
                cursor: pointer;

                &:hover {
                    background-color: rgba(0, 0, 0, 0.05);

                    i {
                        font-size: 3rem;
                    }
                }

                i {
                    font-size: 2.5rem;
                    transition: font-size 0.2s;
                }
            }
        }
        .payment-method {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;

            img {
                height: 31px;
                width: 45px;
                object-fit: contain;
                border-radius: 6px;
                border: 1px solid map-get($colors-v2, 'border');
            }

            small {
                text-transform: capitalize;
            }
        }
    }
    .digital-profile-config {
        display: flex;
        gap: 2rem;
        justify-content: space-between;

        .digital-profile-settings {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 1rem;
        }

        @include respond-below(sm) {
            & {
                flex-direction: column-reverse;
            }

            .digital-profile-settings {
                align-items: flex-start;
            }
        }
    }
    .toggle-yext-notifs {
        cursor: pointer;
    }

    .landing-pages-container .demo-switch {
        margin-right: -15px;
        label {
            font-size: 14px;
        }
    }

    .input-container {
        display: flex;
        gap: 2rem;
        justify-content: space-around;
        margin-top: 1rem;

        @include respond-below(sm) {
            flex-direction: column;
        }

        & > div {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 1rem;
            gap: 1rem;

            .example {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-grow: 1;
            }
        }
    }
}

#hours-selector-form {
    gap: 1rem;

    & > div {
        gap: 2rem;

        .form-group {
            margin: 0;

            input {
                width: 90px;
            }
        }
    }
}

.image-upload-dialog {
    .dropzone {
        border-style: dashed;
        .dz-button {
            margin-top: 10px;
        }
    }

    // Smaller existing attachments list group...
    .list-group-item {
        padding: 3px 10px;
        .btn {
            margin-left: 10px;
        }
    }

    // Fix for the SVG table icon's margin which seems broken in the current trumbowyg release..
    div.trumbowyg-button-pane .trumbowyg-table-button > svg {
        margin-top: -11px;
    }

    #wysiwyg-editor {
        max-height: 300px;
    }

    .selectbox {
        border-bottom: 1px solid #dddddd;
        background-color: #fbfbfb;
        padding: 2px;
        padding-left: 10px;
        padding-right: 10px;

        .material-icons {
            font-size: 16px;
        }
    }
    .selectbox:hover {
        background-color: #f7f7f7;
    }
    .materialize [type='checkbox'] + label {
        font-size: inherit;
    }
}

.custom-tracking {
    // light code editor
    .ltd .comment {
        color: red;
    }

    .ltd .keyword {
        color: black;
    }

    .tracking-items {
        list-style: none;
        margin: 0 0 10px;
        padding: 0;
        counter-reset: list;

        &:empty {
            display: none;
        }

        li {
            margin: 0 0 10px;
            padding: 0;
            position: relative;

            .badge {
                position: absolute;
                left: 40px;
                pointer-events: none;
                text-transform: capitalize;
                top: 50%;
                transform: translateY(-50%);
            }

            .btn {
                box-shadow: none;
                display: block;
                font-size: 16px;
                width: 100%;
                position: relative;
                padding: 6px 32px 6px 150px;
                font-weight: 700;
                text-align: left;

                &:before {
                    display: block;
                    counter-increment: list;
                    content: counter(list);
                    position: absolute;
                    left: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: rgba(0, 0, 0, 0.1);
                    color: #fff;
                    font-size: 14px;
                    text-align: center;
                    line-height: 22px;
                }
            }

            .tracking-item-delete {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                width: 20px;
                height: 20px;
                border-radius: 50%;
                font-size: 14px;
                line-height: 1;
                background: rgba(0, 0, 0, 0.1);
                color: rgba(255, 0, 0, 0.3);
                border: 0;
                text-align: center;
                padding: 0;
            }
        }
    }

    .tracking-contents {
        display: grid;
        place-items: start stretch;

        &.show-form {
            .tracking-items {
                pointer-events: none !important;
            }
        }

        #tracking-code-wrapper {
            position: relative;
            height: 250px;
            width: auto;
            margin: 15px auto;

            textarea {
                height: 100% !important;
                border: 0;
                border-left: 1px solid;
            }
        }

        // #tracking-code-wrapper::before {
        //     content:"wrapper";
        //     text-transform: uppercase;
        //     left:0px;
        //     position:absolute;
        //     top:-18px;
        //     font-size:16px;
        //     color:red;
        // }

        @media (min-width: 1000px) {
            &.show-form {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
        }
    }

    .tracking-form {
        padding: 20px;
        margin: 0;
        border: 1px solid $border-color;
        border-radius: $border_radius-m;
        display: none;

        &.active {
            display: grid;
            gap: 32px;
        }

        &-actions {
            text-align: right;
        }

        &-options {
            display: grid;
            place-items: end;

            @media (min-width: 340px) {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .checkbox-label {
                font-size: 14px;
            }
        }
    }

    #tracking-form-error {
        color: red;
        font-weight: bold;
        display: none;
        text-align: right;
    }
}

.swal2-popup:has(.preview-image-dialog) {
    img {
        width: 100%;
        max-height: 90vh;
    }
}

.swal2-popup:has(.panorama-selector-dialog) {
    .dropzone {
        position: relative;
        .dz-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0px;
        }
    }

    #pano-map,
    .dropzone:not(.min) {
        width: 100%;
        height: 300px;
        margin-bottom: 1rem;
    }

    .preview,
    .dropzone.min {
        width: 100%;
        height: 250px;
        object-fit: contain;
        margin-bottom: 1.5rem;
    }

    .used-images > .image-thumbnails {
        display: grid;
        gap: 1rem;
        grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));

        & > button {
            box-shadow: none;
            border: none;
            height: 70px;
            overflow: hidden;

            &.select-photo-btn {
                padding: 0px;
                border-radius: 2.33px;
                img {
                    height: 100%;
                    width: 100%;
                    object-fit: cover;
                    border-radius: 6px;
                    border-radius: 2.33px;
                    opacity: 0.5;
                    transition: opacity 0.2s ease-in-out;
                    background-color: #f3f3f3;
                }
            }

            &.add-photo-btn {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 4px;
                font-size: 11px;
                border: 1px dashed #d5dbe1;
                border-radius: 6px;
                background-color: #fff;
            }

            &:hover,
            &.selected {
                img {
                    opacity: 1;
                }
            }

            &.selected {
                outline: 2.33px solid #0094f7 !important;
            }
        }
    }

    .display-type-container {
        border-top: 1px solid #ebeef1;
        padding: 1rem;
        padding-bottom: 0px;

        .heading {
            line-height: 20px;
            color: #6a7383;
            font-size: 16px;
            text-align: center;
            margin: 0px;
            margin-bottom: 6px;
        }
    }
}

.free-trial-grp {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    gap: 20px;
    padding-top: 30px;

    &__dropdown {
        max-width: 200px;
    }
}

.club-management-page {
    .isLoading {
        .card {
            display: block !important;
        }
    }
}
