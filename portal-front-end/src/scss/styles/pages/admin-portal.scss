#admin-portal {
    &>.container-fluid {
        padding: 32px;
        background-color: #fff;
        overflow: auto;

        @media (max-width: 767px) {
            padding: 16px;
        }
    }

    .dt-buttons {
        margin-top: -38px;
        margin-left: 10px;
    }

    .clone-user,
    .row-actions {
        :not(.remove-user):not(.remove-role) {
            i {
                font-size: 16px;
                color: $slate-350;
                transition: color 0.3s;
            }
        }

        .add-role:hover {
            i {
                color: $accent-blue-500;
            }
        }

        .remove-user:hover,
        .remove-role:hover {
            i {
                color: $red-400;
            }
        }
    }

    .users-table-container {
        min-height: 200px;
    }

    .role-card {
        min-width: 200px;
        min-height: 25px;
        // border: 1px solid $border-color;
        margin: 0px;
        border-radius: $border_radius-m;
        box-shadow: $natural-shadow-xs;
        cursor: pointer;
        align-self: stretch;

        a {
            text-decoration: none;
        }

        &:hover {
            background-color: $slate-50;
        }

        i {
            cursor: pointer;
            font-size: 16px;
            color: $slate-350;

            &:hover {
                color: $accent-blue-500;
            }
        }

        .role-name {
            font-size: 14px;
            font-weight: 600;
            color: $accent-blue-500;
        }

        &.add-role-card {
            border: 2px dashed $border-color;
            box-shadow: none;
            align-self: stretch;
            align-items: center;
            justify-content: center;
            display: flex;
            flex-direction: column;
            flex-direction: column;
            max-width: 14rem;
            width: 100%;
            min-width: fit-content;

            &:hover {

                .add-role-text,
                i {
                    color: $accent-blue-400;
                }
            }

            .add-role {
                i {
                    font-size: 24px;
                }
            }

            .add-role-text {
                font-size: 14px;
                color: $slate-450;
            }

            a {
                text-decoration: none;
            }
        }
    }

    .ph-table-footer {
        margin-top: 2rem;
    }

    // Module Groups Accordion Styles
    .module-groups-accordion {
        margin: 0;

        .panel {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            margin-bottom: 8px;
            background-color: #fff;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .panel-heading {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            border-radius: 4px 4px 0 0;

            .panel-title {
                padding: 12px 16px;
                margin: 0;
                font-size: 14px;
                transition: background-color 0.2s ease;

                &:hover {
                    background-color: #e9ecef;
                }

                .fa-folder {
                    color: #6c757d;
                    font-size: 16px;
                }

                .group-title {
                    font-weight: 500;
                    color: #495057;
                }

                .header-controls {
                    .selected-count {
                        font-size: 11px;
                        background-color: #e0e0e0;
                        color: #6c757d;
                        padding: 2px 8px;
                        border-radius: 12px;
                        border: none;
                    }

                    .select-all-group {
                        font-size: 11px;
                        padding: 4px 8px;
                        min-height: auto;
                        line-height: 1.2;
                    }
                }
            }
        }

        .panel-collapse {
            .panel-body {
                padding: 16px;
                background-color: #fff;
                border-radius: 0 0 4px 4px;

                .chips-container {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                }
            }
        }

        // Active/expanded state styling
        .panel-heading[aria-expanded='true'] {
            .panel-title {
                background-color: #e3f2fd;

                .fa-folder {
                    color: #1976d2;
                }
            }
        }

        .module-chip {
            display: inline-block;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            background-color: #f8f9fa;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            user-select: none;

            &:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            &.selected {
                background-color: #007bff;
                border-color: #007bff;
                color: white;

                &:hover {
                    background-color: #0056b3;
                    border-color: #0056b3;
                    transform: translateY(-1px);
                    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
                }
            }
        }
    }

    // Accordion controls styling
    .accordion-controls {
        .expand-all-toggle {
            border: 1px solid #dee2e6;
            background-color: #f8f9fa;
            color: #495057;

            &:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }

            &.expanded {
                background-color: #007bff;
                border-color: #007bff;
                color: white;

                &:hover {
                    background-color: #0056b3;
                    border-color: #0056b3;
                }
            }

            i {
                margin-right: 4px;
            }
        }
    }

    // Hidden inputs for form compatibility
    .hidden-module-inputs {
        display: none;
    }
}

.new-facility-dialog {
    .info-icon {
        font-size: 14px;
        margin-top: -2px;
    }

    text-align: left;

    h4 {
        width: 100%;
        border-bottom: 1px solid #eee;
        padding: 5px;
        margin-bottom: 20px;
    }

    .permissions {
        [class*='col-'] {
            padding: 5px;

            input,
            select {
                width: 100%;
            }
        }

        .col-sm-4 {
            text-align: right;
        }
    }

    .left-padding {
        padding-left: 10px;
    }

    .right-margin {
        margin-right: 20px;
    }

    .form-group {
        margin-bottom: 0px;
    }

    .facility-associations {
        width: 100%;
    }

    .form-line:has(input[name='id']:disabled) {
        border-bottom: 0px;
    }
}

.organisation-dialog {
    .img-not-found {
        background: map-get($colors-v2, 'border') url('../../../assets/images/carbon_no-image.png') no-repeat center
            center;
        background-size: 24px;
    }

    .logo-container {
        $fixed-img-size: 64px;

        img {
            width: $fixed-img-size;
            height: $fixed-img-size;
            border-radius: 50%;
            object-fit: contain;
            background-color: #000;
        }

        .logo-wrapper {
            border-radius: 50%;
        }

        &.isLoading {
            img {
                background-color: #fff;
            }
        }
    }
}
