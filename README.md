## Developing:

### PREREQUISITES

* Docker
* Docker Compose
* Node 14 or Later
* Lots of Coffee ☕️☕️☕️

### CONFIGURING

Configure your AWS profile, so we can access the 12RND AWS account for developing.
Add the following to `~/.aws/credentials`

```
[12round]
aws_access_key_id={YOUR_AWS_ACCESS_KEY_ID}
aws_secret_access_key={YOUR_AWS_SECRET_ACCESS_KEY}
```

By default, local development databases will be used. To use change configuration files (to use either stg or prd)
create a `.env` file in the root directory add either 'staging' or 'production' to use stg/prd config files:

Example .env file:

```
# Can be:
#  - production
#  - staging
#  - dev

NODE_ENV=staging
SHOW_CONFIG=true

SWAGGER_UI=true

# Using DN email for testing
DEFAULT_DEVELOPMENT_USER=<EMAIL>

# Set to 'true' to disable pm2 clustering... Uncomment/don't set to ENABLE pm2 clustering (like prod)
#DEV_CONTAINER=true

# These should not make it into git... 😬
AWS_ACCESS_KEY_ID=xxxxxxxxxxxxxxxxxx
AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxx

```

Note you also add the environment variable: `SHOW_CONFIG=true` to print out the current configuration values when loading the app for the first time...


### STARTING
... **STARTING FOR THE FIRST TIME** ...

You will need to install your `node_modules` onto the local system if you're starting for the first time... Start the main portal-server container and wait for the node_modules to install. Note there's about 1GB+ of modules, so it will take a while... ☕️

```
docker-compose -f docker-compose-dev.yml up portal-server
```

Once all node_modules are installed you can stop the container start BOTH the front-end and the backend.

... **STARTING FOR REGULAR DEV** ...

Simply run a `npm start` in the root directory (this directory), or start with docker-compose.

```
docker network create portal-net
docker-compose -f docker-compose-dev.yml up
```

Note, modules will be installed automatically as well on every future container start.

By default the app will run when developing on TCP:3000 [http://localhost:3000](http://localhost:3000) - with Browsersync support to auto reload the UI when you update CSS, JS etc.

### EXECUTING LAMBDA FUNCTIONS (such as crons) FROM WITHIN THE DOCKER CONTAINER

* docker exec -it XXX the `portal-server-sls` container
* Run the following to execute a local lambda function:
  * Substitute the `ph-dev-` with the correct environment you're using - ie `stg` (if required)
  * Substitute the function name with the function you actually want to invoke.

```
aws lambda invoke /dev/null \
  --endpoint-url http://localhost:3002 \
  --function-name ph-dev-startDailySaasBillingLogQueue \
  --region=local

```


### DOCUMENTATION

Documentation is available via Swagger and deployed automatically to [http://developers.gymsystems.co](http://developers.gymsystems.co).

To access documentation while developing simply navigate to: http://localhost:3000/api-docs