/**
 * Global Toast notification system using SweetAlert 2
 * Provides consistent toast notifications across the application
 */

console.log('🍞 app-toast.js loading...');
console.log('🍞 Swal available?', typeof Swal !== 'undefined', window.Swal);

// Global Toast mixin configuration
let appToast;
try {
    if (typeof Swal === 'undefined') {
        console.error('🍞 SweetAlert 2 (Swal) is not available!');
        return;
    }
    
    appToast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: false,
        didOpen: (toast) => {
            toast.addEventListener("mouseenter", Swal.stopTimer);
            toast.addEventListener("mouseleave", Swal.resumeTimer);
        },
        showClass: {
            backdrop: "swal2-noanimation", // disable backdrop animation
            popup: "", // disable popup animation
        },
    });
    
    console.log('🍞 appToast created successfully:', appToast);
} catch (error) {
    console.error('🍞 Error creating appToast:', error);
    return;
}

// Utility function to show toast notifications
function showToast(message, type = 'success') {
    console.log('🍞 showToast called:', { message, type });
    
    if (!appToast) {
        console.error('🍞 appToast not available, cannot show toast');
        return;
    }
    
    try {
        // Map notification types to SweetAlert 2 icons
        const iconMap = {
            'success': 'success',
            'danger': 'error',
            'error': 'error',
            'warning': 'warning',
            'info': 'info'
        };
        
        console.log('🍞 Firing toast with appToast:', appToast);
        
        return appToast.fire({
            icon: iconMap[type] || type,
            title: message
        });
    } catch (error) {
        console.error('🍞 Error showing toast:', error);
    }
}

// Make appToast and showToast globally available
try {
    window.appToast = appToast;
    window.showToast = showToast;
    console.log('🍞 showToast registered globally:', typeof window.showToast);
} catch (error) {
    console.error('🍞 Error registering toast functions globally:', error);
}