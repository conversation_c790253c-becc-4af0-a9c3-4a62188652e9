/**
 * Global Toast notification system using SweetAlert 2
 * Provides consistent toast notifications across the application
 */

console.log('🍞 app-toast.js loading...');
console.log('🍞 Document ready state:', document.readyState);
console.log('🍞 Swal available?', typeof Swal !== 'undefined', window.Swal);
console.log('🍞 Window object keys containing "swal":', Object.keys(window).filter(key => key.toLowerCase().includes('swal')));
console.log('🍞 Script load order check - jQuery available?', typeof $ !== 'undefined');

// Global Toast mixin configuration
let appToast;

// Function to initialize the toast system
function initializeToastSystem() {
    console.log('🍞 Initializing toast system...');

    if (typeof Swal === 'undefined') {
        console.error('🍞 SweetAlert 2 (Swal) is not available during initialization!');
        return false;
    }

    if (appToast) {
        console.log('🍞 Toast system already initialized');
        return true;
    }

    try {

        appToast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: false,
            didOpen: (toast) => {
                toast.addEventListener("mouseenter", Swal.stopTimer);
                toast.addEventListener("mouseleave", Swal.resumeTimer);
            },
            showClass: {
                backdrop: "swal2-noanimation", // disable backdrop animation
                popup: "", // disable popup animation
            },
        });

        console.log('🍞 appToast created successfully:', appToast);
        return true;
    } catch (error) {
        console.error('🍞 Error creating appToast:', error);
        appToast = null;
        return false;
    }
}

// Utility function to show toast notifications
function showToast(message, type = 'success') {
    console.log('🍞 showToast called:', { message, type });

    if (!appToast) {
        console.log('🍞 appToast not available, attempting to initialize...');
        const initSuccess = initializeToastSystem();
        if (!initSuccess || !appToast) {
            console.error('🍞 Failed to initialize toast system, cannot show toast');
            return;
        }
        console.log('🍞 Toast system initialized successfully, proceeding with toast');
    }

    try {
        // Map notification types to SweetAlert 2 icons
        const iconMap = {
            'success': 'success',
            'danger': 'error',
            'error': 'error',
            'warning': 'warning',
            'info': 'info'
        };

        console.log('🍞 Firing toast with appToast:', appToast);

        return appToast.fire({
            icon: iconMap[type] || type,
            title: message
        });
    } catch (error) {
        console.error('🍞 Error showing toast:', error);
    }
}

// Initialize the toast system
console.log('🍞 Attempting initial toast system initialization...');
let initSuccess = initializeToastSystem();

// If initial initialization failed, try again after a delay
if (!initSuccess) {
    console.log('🍞 Initial initialization failed, retrying in 100ms...');
    setTimeout(() => {
        console.log('🍞 Retrying toast system initialization...');
        initSuccess = initializeToastSystem();
        if (!initSuccess) {
            console.log('🍞 Retry failed, will try again in 500ms...');
            setTimeout(() => {
                console.log('🍞 Final retry of toast system initialization...');
                initializeToastSystem();
            }, 500);
        }
    }, 100);
}

// Make appToast and showToast globally available
try {
    window.appToast = appToast;
    window.showToast = showToast;
    console.log('🍞 showToast registered globally:', typeof window.showToast);
    console.log('🍞 Global registration complete. Testing showToast...');

    // Toast system ready - no test toast needed in production

} catch (error) {
    console.error('🍞 Error registering toast functions globally:', error);
}