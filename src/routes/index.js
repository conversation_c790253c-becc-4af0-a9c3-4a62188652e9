const express = require("express");
const path = require("path");
const router = express.Router();
const fs = require("fs");
const getSlug = require("speakingurl");
const common = require("./common");
const _ = require("lodash");
const mime = require("mime-types");
const url = require("url");
const junk = require("junk");
const walk = require("walk");
const mkdirp = require("mkdirp");
const multer = require("multer");
const glob = require("glob");
const multer_upload = require("multer");
const zipExtract = require("extract-zip");
const rimraf = require("rimraf");
const JSZip = require("jszip");
const sm = require("sitemap");
const classy = require("../public/javascripts/markdown-it-classy");
const config = common.read_config();
const moment = require('moment');
const axios = require('axios');
const markdownit = require('markdown-it')({
    html: true,
    linkify: true,
    typographer: true,
    breaks: false,
    xhtmlOut: true
}).use(classy).use(require('markdown-it-imsize'));

const { getTopicDetails } = require('./services/getTopicDetailsByIdService');
const appDir = path.dirname(require("require-main-filename")());
const { t } = require('application-translator/dist/translate/translate.node');
const { getDb } = require("../lib/db");
const { getAlgoliaConfig, syncArticlesToAlgolia } = require("../lib/algolia");

// The homepage of the site
router.get("/", common.restrict, async (req, res, next) => {
    const db = await getDb();
    const algoliaConfig = await getAlgoliaConfig();
    common.config_expose(req.app);
    //const featuredCount = config.settings.featured_articles_count ? config.settings.featured_articles_count : 4;

    // set the template dir
    //common.setTemplateDir('user', req);
    common.setTemplateDir("admin", req);

    // get sortBy from config, set to 'kb_viewcount' if nothing found
    const sortByField =
        typeof config.settings.sort_by.field !== "undefined" ? config.settings.sort_by.field : "kb_viewcount";
    const sortByOrder = typeof config.settings.sort_by.order !== "undefined" ? config.settings.sort_by.order : -1;
    const sortBy = {};
    sortBy[sortByField] = sortByOrder;

    let search_club = req.session.clubId;
    console.log('topics', req.session)
    //check if there are clubs
    if (search_club && search_club !== "") {
        //get the country of selected club before doing the actual query
        common.getCountryOfClub(search_club).then((country) => {
            let regExpr = new RegExp(".*" + country + ".*", "i");


            common.dbQuery(db.topics, {
                $or: [{ topic_regional_visibility: "" }, { topic_regional_visibility: { $regex: regExpr } }],
                enabled: "true",
            }, { display_order: 1 }, null,

                function (err, topics) {
                    if (err) {
                        console.log(" ---> Error while getting topics for country: " + country, err);
                    }

                    //display directly without getting featured and pinned topics - to make it faster
                    res.render("index", {
                        layout: '../../public/themes/12rnd/views/layouts/layout',
                        title: "openKB",
                        user_page: true,
                        topics: topics,
                        homepage: true,
                        top_results: [],
                        featured_results: [],
                        session: req.session,
                        message: common.clear_session_value(req.session, "message"),
                        message_type: common.clear_session_value(req.session, "message_type"),
                        config: config,
                        current_url: req.protocol + "://" + req.get("host") + req.app_context,
                        fullUrl: req.protocol + "://" + req.get("host") + req.originalUrl,
                        helpers: req.handlebars,
                        show_footer: "show_footer",
                        algoliaConfig,
                        club: search_club,
                    });
                });
        });
    } else {
        //show only topics with empty regionals
        common.dbQuery(db.topics, {
            topic_regional_visibility: "",
            enabled: "true",
        }, { display_order: 1 }, null,

            function (err, topics) {
                if (err) {
                    console.log(" ---> Error while getting topics for country: " + country, err);
                }

                res.render("index", {
                    title: "openKB",
                    user_page: true,
                    topics: topics,
                    homepage: true,
                    top_results: [],
                    featured_results: [],
                    session: req.session,
                    message: common.clear_session_value(req.session, "message"),
                    message_type: common.clear_session_value(req.session, "message_type"),
                    config: config,
                    current_url: req.protocol + "://" + req.get("host") + req.app_context,
                    fullUrl: req.protocol + "://" + req.get("host") + req.originalUrl,
                    helpers: req.handlebars,
                    show_footer: "show_footer",
                });
            });
    }
});

router.post("/protected/action", async (req, res) => {
    const db = await getDb();

    db.kb.findOne({ kb_published: "true", _id: common.getId(req.body.kb_id) }, (err, result) => {
        // check password
        if (req.body.password === result.kb_password) {
            // password correct. Allow viewing the article this time
            req.session.pw_validated = "true";
            res.redirect(req.header("Referer"));
        } else {
            // password incorrect
            req.session.pw_validated = null;
            res.render("error", {
                message: "Password incorrect. Please try again.",
                helpers: req.handlebars,
                config: config,
            });
        }
    });
});

// vote on articles
router.post("/vote", async (req, res) => {
    const db = await getDb();

    // if voting allowed
    if (config.settings.allow_voting === true) {
        // check if voted
        db.votes.findOne({ $and: [{ doc_id: req.body.doc_id }, { session_id: req.sessionID }] }, (err, result) => {
            // if not voted
            if (!result) {
                let vote = req.body.vote_type === "upvote" ? 1 : -1;
                // update kb vote
                db.kb.update(
                    { _id: common.getId(req.body.doc_id) },
                    { $inc: { kb_votes: vote } },
                    (err, numReplaced) => {
                        // insert session id into table to stop muli-voters
                        db.votes.insert({ doc_id: req.body.doc_id, session_id: req.sessionID }, (err, newDoc) => {
                            res.writeHead(200, { "Content-Type": "application/text" });
                            res.end("Vote successful");
                        });
                    }
                );
            } else {
                // User has already voted
                res.writeHead(404, { "Content-Type": "application/text" });
                res.end("User already voted");
            }
        });
    } else {
        // Voting not allowed
        res.writeHead(404, { "Content-Type": "application/text" });
        res.end("Voting now allowed");
    }
});

// Render a version of the article to logged in users
router.get("/" + config.settings.route_name + "/:id/version", common.restrict, async (req, res) => {
    const db = await getDb();
    common.config_expose(req.app);


    // check for logged in user
    if (!req.session.user) {
        res.render("error", {
            message: "404 - Page not found",
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    // get sortBy from config, set to 'kb_viewcount' if nothing found
    let sortByField =
        typeof config.settings.sort_by.field !== "undefined" ? config.settings.sort_by.field : "kb_viewcount";
    let sortByOrder = typeof config.settings.sort_by.order !== "undefined" ? config.settings.sort_by.order : -1;
    let sortBy = {};
    sortBy[sortByField] = sortByOrder;

    let featuredCount = config.settings.featured_articles_count ? config.settings.featured_articles_count : 4;

    db.kb.findOne({ _id: common.getId(req.params.id) }, (err, result) => {
        // show the view
        common.dbQuery(
            db.kb,
            { kb_published: "true", kb_versioned_doc: { $eq: true } },
            sortBy,
            featuredCount,
            (err, featured_results) => {
                res.render("kb", {
                    title: result.kb_title,
                    result: result,
                    user_page: true,
                    kb_body: common.sanitizeHTML(markdownit.render(result.kb_body)),
                    featured_results: featured_results,
                    config: config,
                    session: req.session,
                    current_url: req.protocol + "://" + req.get("host") + req.app_context,
                    fullUrl: req.protocol + "://" + req.get("host") + req.originalUrl,
                    message: common.clear_session_value(req.session, "message"),
                    message_type: common.clear_session_value(req.session, "message_type"),
                    helpers: req.handlebars,
                    show_footer: "show_footer",
                });
            }
        );
    });
});



router.get("/" + config.settings.route_name + "/:id", common.restrict, async (req, res) => {
    const db = await getDb();
    const algoliaConfig = await getAlgoliaConfig();

    const featuredCount = config.settings.featured_articles_count ? config.settings.featured_articles_count : 4;

    const search_club = req.session.clubId;

    //check if there are clubs
    const clubCountry = await common.getCountryOfClub(search_club);
    const regExpr = new RegExp(".*" + clubCountry + ".*", "i");

    // set the template dir
    //common.setTemplateDir('user', req);

    // get sortBy from config, set to 'kb_viewcount' if nothing found
    let sortByField =
        typeof config.settings.sort_by.field !== "undefined" ? config.settings.sort_by.field : "kb_viewcount";
    let sortByOrder = typeof config.settings.sort_by.order !== "undefined" ? config.settings.sort_by.order : -1;
    let sortBy = {};
    sortBy[sortByField] = sortByOrder;

    db.kb.findOne(
        {
            $or: [{ _id: common.getId(req.params.id) }, { kb_permalink: req.params.id }],
            kb_versioned_doc: { $ne: true },
        },
        (err, result) => {
            // render 404 if page is not published
            if (result?.kb_published !== 'true') {
                return res.render("error", {
                    message: "404 - Page not found",
                    helpers: req.handlebars,
                    config: config,
                });
            }

            // check if has a password
            if (result.kb_password) {
                if (result.kb_password !== "") {
                    if (
                        req.session.pw_validated === "false" ||
                        req.session.pw_validated === undefined ||
                        req.session.pw_validated == null
                    ) {
                        res.render("protected_kb", {
                            title: "Protected Article",
                            result: result,
                            config: config,
                            session: req.session,
                            helpers: req.handlebars,
                        });
                        return;
                    }
                }
            }

            // if article is set to private, redirect to login
            if (typeof result.kb_visible_state !== "undefined" && result.kb_visible_state === "private") {
                if (!req.session.user) {
                    req.session.refer_url = req.originalUrl;
                    res.redirect("/login");
                    return;
                }
            }

            // add to old view count
            let old_viewcount = result.kb_viewcount;
            if (old_viewcount == null) {
                old_viewcount = 0;
            }

            let new_viewcount = old_viewcount;
            // increment if the user is logged in and if settings say so
            if (req.session.user && config.settings.update_view_count_logged_in) {
                new_viewcount = old_viewcount + 1;
            }

            // increment if the user is a guest and not logged in
            if (!req.session.user) {
                new_viewcount = old_viewcount + 1;
            }


            // update kb_viewcount
            db.kb.update(
                {
                    $or: [{ _id: common.getId(req.params.id) }, { kb_permalink: req.params.id }],
                },
                {
                    $set: { kb_viewcount: new_viewcount },
                },
                { multi: false },
                (err, numReplaced) => {
                    //new table for pageviews------------------------------------------------------------------------
                    /**
                    PAGEVIEWS:
                        datetime
                        articleId
                        views
                    */
                    let today = new Date();
                    let todayShort = today.toISOString().split('T')[0];
                    let newPageView = { datetime: today, date: todayShort, articleId: result._id, views: 1 };

                    //1 find with the same article and date
                    db.pageviews.findOne({ articleId: result._id, date: todayShort }, (err, foundPageView) => {
                        if (err) {
                            console.log('Error while finding in pageviews:', err);
                        }
                        if (foundPageView) {
                            //needs to update the count
                            db.pageviews.update(
                                { _id: common.getId(foundPageView._id) },
                                {
                                    $set: {
                                        views: parseInt(foundPageView.views) + 1
                                    },
                                },
                                {},
                                (err, numReplaced) => {
                                    if (err) {
                                        console.error("pageviews: Failed to save changes: " + err);
                                    } else {
                                        console.error("pageviews: Changes saved! : ", numReplaced);
                                    }
                                }
                            );
                        } else {
                            //2 insert
                            db.pageviews.insert(newPageView, (err, pageviewsEntry) => {
                                //just logs, no returns
                                if (err) {
                                    console.error("Failed to save new pageviews data: " + err);
                                } else {
                                    console.log('Inserted new pageviews data');
                                }
                            });
                        }
                    });
                    //--------------------------------------------------------------------------------------------------

                    // clear session auth and render page
                    req.session.pw_validated = null;

                    //get the yes/no feedback - july 26, 2022
                    db.helpfulornot.findOne({ user: req.session.user, articleId: result._id }, (err, helpfulornotResult) => {
                        let wasHelpful = null;
                        let helpfulornotUpdated = null;
                        let convertedDate = null;

                        if (err) {
                            console.log('Error in getting helpfulornot:', err);
                        }

                        if (helpfulornotResult) {
                            wasHelpful = helpfulornotResult.helpful;
                            helpfulornotUpdated = new Date(helpfulornotResult.updatedAt);
                            convertedDate = new moment(helpfulornotUpdated).format('Do [of] MMMM YYYY');
                        } else {
                            console.log('No feedback yet');
                        }

                        //get comment
                        db.comments.findOne({ user: req.session.user, articleId: result._id }, (err, commentResult) => {
                            let comment = '';
                            let commentedOn = null;

                            if (err) {
                                console.log('Error in getting comments:', err);
                            }

                            if (commentResult) {
                                comment = commentResult.comment;
                                let commentDate = new Date(commentResult.updatedAt);
                                commentedOn = new moment(commentDate).format('Do [of] MMMM YYYY');;
                            }

                            // get the featured articles before showing the view
                            common.dbQuery(db.kb, { kb_published: "true", kb_regional_visibility: { $regex: regExpr } }, sortBy, featuredCount, async (err, featured_results) => {
                                if (err) {
                                    console.log('Error in getting article details:', err);
                                }
                                let kbBody = result.kb_body;
                                if (result.kb_is_markdown) {

                                    kbBody = common.replaceVideoAudioEmbedWithHtml(kbBody);
                                    kbBody = common.convertBodyToHtml(kbBody)

                                    //kbBody = common.sanitizeHTML();
                                    //kbBody = markdownit.render(kbBody)

                                }



                                const topic = await db.topics.findOne({ _id: parseInt(result.kb_pinned_topic) });

                                if (!topic) {
                                    console.log('No topic found for article', result.kb_title);
                                }

                                const tableOfContents = [];

                                const matches = kbBody.match(/(<h[0-9]>.*?<\/h[0-9]>)+/gm) ?? []

                                matches.forEach(heading => {

                                    tableOfContents.push({
                                        id: heading.toLowerCase().replace(/(<([^>]+)>)/ig, '').replace(/ /g, "-").replace('&amp;', '').replace(/[`~!@#$%^&*()_|+=?;:'",.<>\{\}\[\]\\\/]/g, ""),
                                        heading: heading.replace(/(<([^>]+)>)/ig, ''),
                                    })
                                });

                                const subtopic = (topic && topic.subtopics) ? topic.subtopics.find(s => s.id == result.kb_pinned_subtopic) : null;

                                // kbBody = kbBody.replace(/<img[^>]+>/g, function (match) {
                                //     const width = match.match(/width="([^"]*)"/);
                                //     const height = match.match(/height="([^"]*)"/);

                                //     return `<div class="uploaded-media" style="${width ? `max-width: ${width[1]}px;` : ''}${height ? `max-height: ${height[1]}px;` : ''}"><span>Loading Media...</span>${match}</div>`;
                                // });

                                res.render("kb", {
                                    layout: '../../public/themes/12rnd/views/layouts/layout',
                                    title: result.kb_title,
                                    result: result,
                                    helpfulornot: wasHelpful,
                                    helpfulornotUpdated: convertedDate,
                                    comment: comment,
                                    commentedOn: commentedOn,
                                    prev_page: req.session.prev_page,
                                    user_page: true,
                                    kb_body: kbBody,
                                    featured_results: featured_results,
                                    config: config,
                                    session: req.session,
                                    current_url: req.protocol + "://" + req.get("host") + req.app_context,
                                    fullUrl: req.protocol + "://" + req.get("host") + req.originalUrl,
                                    message: common.clear_session_value(req.session, "message"),
                                    message_type: common.clear_session_value(req.session, "message_type"),
                                    helpers: req.handlebars,
                                    show_footer: "show_footer",
                                    bodyClasses: 'overflow-auto',
                                    algoliaConfig,
                                    club: search_club,
                                    topic: topic,
                                    subtopic: subtopic,
                                    tableOfContents: tableOfContents,
                                });
                            });

                        });//end of getting comments


                    });//end of find helpfulornot

                }//end of callback
            );
            //}
        }
    );
});

// render the settings page with nav or tab defaults
router.get("/settings/:nav?/:tab?", common.restrict, async (req, res) => {
    //settings/articles
    // Allow non-admin access only for User Preferences tab
    const isUserPreferencesTab = req.params.tab === 'userpreferences' || req.query.tab === 'userpreferences';

    if (req.session.is_admin !== "true" && !isUserPreferencesTab) {
        res.render("error", {
            message: "Access denied",
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    let nav = req.params.nav;
    let tab = req.params.tab;

    // path to themes
    let themePath = path.join(__dirname, "../public/themes");

    //get the countries first before showing page
    const countries = await common.getCountries();

    //include the countries in the result
    let countriesOnly = [];
    countries.forEach((c) => {
        countriesOnly.push(c.full_name);
    });

    const db = await getDb();
    const topics = await common.getTopics(db, req.session.userLanguage ?? 'en')
    const languages = await common.getLanguagesOptions(req)

    const articleQuery = { kb_versioned_doc: { $ne: true } }

    if (req.session.user_regions) {
        const regionSubQuery = []
        req.session.user_regions.forEach((region) => {
            const regExpr = new RegExp(".*" + region.country + ".*", "i");
            regionSubQuery.push({ kb_regional_visibility: { $regex: regExpr } })
        })

        articleQuery['$or'] = regionSubQuery;
        articleQuery['$or'].push({ kb_regional_visibility: null })
        articleQuery['$or'].push({ kb_regional_visibility: '' })
    }

    //get articles
    const articles = await db.kb.find(articleQuery, {
        projection: {
            kb_title: 1,
            kb_published_date: 1,
            kb_pinned_topic: 1,
            kb_pinned_subtopic: 1,
            kb_published: 1,
            kb_regional_visibility: 1,
            kb_security_level: 1,
            kb_viewcount: 1,
            kb_translation_title: 1
        }
    })
        .sort({ kb_published_date: -1 })
        .toArray();

    //can only grab iso code of regions/country that are included in the club list PH api
    articles.map(article => {
        const visibilityWithIso = [];
        if (article.kb_regional_visibility) {
            article.kb_regional_visibility.split(/(?:,)\s*/i).forEach((region) => {
                const index = countries.findIndex(c => c.full_name === region)

                if (index !== -1 && visibilityWithIso.findIndex(v => v.iso === countries[index].iso_code.toLowerCase()) === -1) {
                    visibilityWithIso.push({ iso: countries[index].iso_code.toLowerCase(), country: region });
                }
            });
        }

        article.visibilityWithIso = visibilityWithIso;

        const topic = topics.find(t => t._id == article.kb_pinned_topic);
        article.readableTopic = '';
        article.readableSubTopic = '';

        if (topic) {
            article.readableTopic = getReadableTopic(req, topic, 'topic');
            const subtopic = topic.subtopics?.find(s => s.id == article.kb_pinned_subtopic) ?? null;
            if (subtopic) article.readableSubTopic = getReadableTopic(req, subtopic, 'subtopic');
        }

        return article;
    });

    const algoliaConfig = await getAlgoliaConfig();

    fs.readdir(themePath, (err, files) => {
        res.render("settings", {
            title: "Settings",
            is_pseudoadmin: req.session.is_pseudoadmin,
            nav: nav,
            tab: tab,
            countries: countries,
            countriesOnly: countriesOnly,
            topics: topics,
            session: req.session,
            themes: files.filter(junk.not),
            locale: Object.keys(req.i18n.locales),
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
            config: config,
            helpers: req.handlebars,
            data: { articles: articles, userLanguage: req.app.locals.userLanguage },
            languages: languages.map((lang) => { if (lang.value && lang.value !== null) return lang.value }),
            algoliaConfig,
        });
    });
});

function getReadableTopic(req, topic, type = 'topic') {
    let readableTopic = topic[type];
    const translation = topic[`${type}Translations`]?.[req.session.userLanguage ?? 'en'];
    const orgTranslation = topic[`${type}Translations`]?.original;
    const enTranslation = topic[`${type}Translations`]?.en;

    if (translation && translation !== topic[type]) {
        // if the topic is in the original language, show the translation
        readableTopic = `${readableTopic} (${translation})`;
    } else if (orgTranslation && orgTranslation !== translation && orgTranslation !== topic[type]) {
        // if the topic is not in the original language, show the original text
        readableTopic = `${readableTopic} (${orgTranslation})`;
    } else if (enTranslation && enTranslation !== orgTranslation && enTranslation !== topic[type]) {
        // if the translation is the same as the original, show the english translation
        readableTopic = `${readableTopic} (${enTranslation})`;
    }

    return readableTopic;
}

// update the settings
router.post("/update_settings", common.restrict, (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: "Access denied",
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    // get the new settings
    let settings = req.body;

    // possible boolean type values
    let booleanArray = [true, "true", false, "false"];

    // loop settings, update config
    for (let key in settings) {
        if (Object.prototype.hasOwnProperty.call(settings, key)) {
            let settingValue = settings[key];
            // check for style keys
            if (key.split(".")[0] === "style") {
                config.settings.style[key.split(".")[1]] = settingValue;
            } else {
                // if true/false, convert to boolean - TODO: Figure a better way of doing this?
                if (booleanArray.indexOf(settingValue) > -1) {
                    settingValue = settingValue === "true";
                }
                config.settings[key] = settingValue;
            }
        }
    }

    // write settings to file
    let dir = path.join(__dirname, "..", "config");
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
    }
    fs.writeFileSync(path.join(dir, "config.json"), JSON.stringify(config, null, 4), "utf8");

    if (config.settings.locale) {
        req.i18n.setLocale(config.settings.locale);
        res.cookie("locale", config.settings.locale);
        req.i18n.setLocaleFromCookie();
    }

    // set notification
    req.session.message = req.i18n.__("Settings successfully updated.");
    req.session.message_type = "success";

    // redirect back
    //give a little time to save changes
    // setTimeout(() => {
    res.redirect(req.app_context + "/settings");
    // }, 1000);
});

// resets the view count of a given article ID
router.get("/" + config.settings.route_name + "/resetviewCount/:id", common.restrict, async (req, res) => {
    const db = await getDb();
    db.kb.update(
        { _id: common.getId(req.params.id) },
        { $set: { kb_viewcount: 0 } },
        { multi: false },
        (err, numReplaced) => {
            if (err) {
                req.session.message = req.i18n.__("View count could not be reset. Try again.");
                req.session.message_type = "danger";
            } else {
                req.session.message = req.i18n.__("View count successfully reset to zero.");
                req.session.message_type = "success";
            }

            // redirect to new doc
            res.redirect(req.app_context + "/edit/" + req.params.id);
        }
    );
});

// resets the vote count of a given article ID
router.get("/" + config.settings.route_name + "/resetvoteCount/:id", common.restrict, async (req, res) => {
    const db = await getDb();
    db.kb.update(
        { _id: common.getId(req.params.id) },
        { $set: { kb_votes: 0 } },
        { multi: false },
        (err, numReplaced) => {
            if (err) {
                req.session.message = req.i18n.__("Vote count could not be reset. Try again.");
                req.session.message_type = "danger";
            } else {
                req.session.message = req.i18n.__("Vote count successfully reset to zero.");
                req.session.message_type = "success";
            }

            // redirect to new doc
            res.redirect(req.app_context + "/edit/" + req.params.id);
        }
    );
});

// render the editor
router.get("/edit/:id", common.restrict, async (req, res) => {
    //edit the article
    const db = await getDb();
    common.config_expose(req.app);

    try {
        const article = await db.kb.findOne({ _id: common.getId(req.params.id), kb_versioned_doc: { $ne: true } });

        if (!article) {
            res.render("error", {
                message: "404 - Page not found",
                helpers: req.handlebars,
                config: config,
            });
            return;
        }

        if (article.kb_regional_visibility && req.session.user_regions) {
            let hasPermission = false;
            req.session.user_regions.forEach((region) => {
                if (article.kb_regional_visibility && article.kb_regional_visibility.split(/(?:,)\s*/i).includes(region.country)) {
                    hasPermission = true;

                }
            })

            if (!hasPermission) {
                res.render("error", {
                    message: "401 - Unauthorized",
                    helpers: req.handlebars,
                    config: config,
                });
                return;
            }
        }

        const topic = await db.topics.findOne({ keyword: article.kb_pinned_topic });

        //process countries
        const possibleContries = await common.getCountries();


        if (req.session.is_pseudoadmin === true) {
            const filteredCountries = possibleContries.filter((c) => {
                return req.session.user_regions.findIndex(r => {
                    return r.country === c.full_name
                }) !== -1;
            }).map(c => { return c.full_name });

            article.countries = filteredCountries;
            article.possibleCountries = filteredCountries;
            // article.countries = possibleContries.map(c => {return c.full_name});

            // article.possibleCountries = possibleContries.map(c => {return c.full_name});
        } else if (topic && topic.topic_regional_visibility !== '' && topic.topic_regional_visibility !== undefined) {
            article.countries = possibleContries.filter((c) => {
                return topic.topic_regional_visibility.split(/(?:,)\s*/i).includes(c.full_name);
            }).map(c => { return c.full_name });

            article.possibleCountries = possibleContries.map(c => { return c.full_name });
        } else {
            article.countries = possibleContries.map(c => { return c.full_name });

            article.possibleCountries = possibleContries.map(c => { return c.full_name });
        }

        const regionalVisibility = article.kb_regional_visibility ? article.kb_regional_visibility.split(/(?:,)\s*/i).filter((c) => {
            return article.countries.includes(c);
        }) : [];


        article.kb_regional_visibility = regionalVisibility.filter((c, index) => {
            return regionalVisibility.indexOf(c) === index;
        });


        const versions = await db.kb.find({ kb_parent_id: req.params.id }).sort({ kb_last_updated: -1 }).limit(20).toArray();
        const languages = await common.getLanguagesOptions(req)

        res.render("form", {
            title: t("Edit article"),
            result: article,
            versions: versions,
            session: req.session,
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
            config: config,
            editor: true,
            helpers: req.handlebars,
            languages: languages,
            is_pseudoadmin: req.session.is_pseudoadmin,
            is_restricted: (article.kb_admin_restricted === 'true' && req.session.is_pseudoadmin) || (req.session.is_pseudoadmin && article.kb_regional_visibility.length === 0) ? true : false
        });

    } catch (err) {
        console.log("Something went wrong: ", err);

    }
});


// render the editor
router.get("/search-analytics/:type/:start/:end", common.restrict, async (req, res) => {
    common.config_expose(req.app);

    try {
        const algoliaConfig = await getAlgoliaConfig();
        const type = req.params.type;
        const start = req.params.start;
        const end = req.params.end;
        let topSearches = null;
        let topResults = null;
        let topNoResults = null;
        let totalCount = 0;
        let title = '';

        count = await axios.get(`https://analytics.us.algolia.com/2/searches/count?index=${algoliaConfig.index}&clickAnalytics=true&startDate=${start}&endDate=${end}`, {
            headers: {
                'X-Algolia-Application-Id': algoliaConfig.appId,
                'X-Algolia-API-Key': algoliaConfig.apiKey
            },
        })

        totalCount = count.data.count;

        switch (type) {
            case 'top-searches':
                title = t("Top searches");
                topSearches = await axios.get(`https://analytics.us.algolia.com/2/searches?index=${algoliaConfig.index}&limit=1000&clickAnalytics=true&startDate=${start}&endDate=${end}`, {
                    headers: {
                        'X-Algolia-Application-Id': algoliaConfig.appId,
                        'X-Algolia-API-Key': algoliaConfig.apiKey
                    },
                })
                topSearches.data.searches.map((search) => {
                    search.percentage = parseFloat(((search.count / totalCount) * 100)).toFixed(2);
                    search.clickThroughRate = search.clickThroughRate === null ? 'N/A' : search.clickThroughRate;
                    search.averageClickPosition = search.averageClickPosition === null ? 0 : search.averageClickPosition;
                    search.conversionRate = search.conversionRate === null ? 'N/A' : search.conversionRate;
                    return search;
                })
                console.log('topSearches', topSearches.data.searches[0])
                break;
            case 'top-results':
                title = t("Top Results");
                topResults = await axios.get(`https://analytics.us.algolia.com/2/hits?index=${algoliaConfig.index}&limit=1000&clickAnalytics=true&startDate=${start}&endDate=${end}`, {
                    headers: {
                        'X-Algolia-Application-Id': algoliaConfig.appId,
                        'X-Algolia-API-Key': algoliaConfig.apiKey
                    },
                })

                const ids = [];

                topResults.data.hits.forEach((search, index) => {
                    ids.push(search.hit)
                })

                const articles = await axios.post(`${req.protocol}://${req.get("host")}${req.app_context}/api/articles/details/mini`, { ids })


                topResults.data.hits.map((search) => {

                    const articleData = articles.data.articles.find(article => search.hit === article._id);
                    if (!articleData) {
                        return null;
                    }
                    search.url = `${req.app_context}/kb/${articleData.kb_permalink ?? articleData._id}`;
                    search.title = articleData.kb_title;

                    return search;
                })

                console.log('topResults', topResults.data)
                break;
            case 'top-no-results':
                title = t("No Results");
                topNoResults = await axios.get(`https://analytics.us.algolia.com/2/searches/noResults?index=${algoliaConfig.index}&limit=1000&clickAnalytics=true&startDate=${start}&endDate=${end}`, {
                    headers: {
                        'X-Algolia-Application-Id': algoliaConfig.appId,
                        'X-Algolia-API-Key': algoliaConfig.apiKey
                    },
                })

                topNoResults.data.searches.map((search) => {
                    search.percentage = parseFloat(((search.count / totalCount) * 100)).toFixed(2);

                    return search;
                })



                console.log('topNoResults', topNoResults.data)
                break;
            default:
                break;

        }


        res.render("search-analytics", {
            title: title,
            type: type,
            start: start,
            end: end,
            totalCount: totalCount,
            topSearches: topSearches ? topSearches.data.searches : null,
            topResults: topResults ? topResults.data.hits : null,
            topNoResults: topNoResults ? topNoResults.data.searches : null,
            session: req.session,
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
            config: config,
            editor: true,
            helpers: req.handlebars,
            is_pseudoadmin: req.session.is_pseudoadmin,
        });

    } catch (err) {
        console.log("Something went wrong: ", err);

    }
});


// insert new KB form action
router.post("/insert_kb", common.restrict, async (req, res) => {
    const db = await getDb();
    let doc = {
        kb_permalink: req.body.frm_kb_permalink,
        kb_title: req.body.frm_kb_title,
        kb_body: req.body.frm_kb_body,
        kb_published: req.body.frm_kb_published,
        kb_keywords: req.body.frm_kb_keywords,
        kb_published_date: new Date(),
        kb_last_updated: new Date(),
        kb_last_update_user: req.session.users_name + " - " + req.session.user,
        kb_author: req.session.users_name,
        kb_author_email: req.session.user,
        kb_password: req.body.frm_kb_password,
        kb_featured: req.body.frm_kb_featured,
        kb_pinned: req.body.frm_kb_pinned === "on" ? "true" : "false",

        kb_pinned_topic: req.body.frm_kb_pinned === 'true' ? req.body.frm_kb_pinned_topic : '',
        kb_visible_state: req.body.frm_kb_visible_state,
        kb_security_level: parseInt(req.body.frm_kb_security_level),
        kb_regional_visibility: req.body.frm_kb_regional_visibility,
        kb_pinned_subtopic: req.body.frm_kb_pinned === 'true' ? req.body.frm_kb_pinned_subtopic : '',
        kb_translation_title: await common.translateTitle(req, req.body.frm_kb_title)
    };


    db.kb.count({ kb_permalink: req.body.frm_kb_permalink }, (err, kb) => {
        if (kb > 0 && req.body.frm_kb_permalink !== "") {
            // permalink exits
            req.session.message = req.i18n.__("Permalink already exists. Pick a new one.");
            req.session.message_type = "danger";

            // keep the current stuff
            req.session.kb_title = req.body.frm_kb_title;
            req.session.kb_body = req.body.frm_kb_body;
            req.session.kb_keywords = req.body.frm_kb_keywords;
            req.session.kb_permalink = req.body.frm_kb_permalink;

            // redirect to insert
            res.redirect(req.app_context + "/insert");
        } else {
            //inserts actual article

            db.kb.insert(doc, (err, newDoc) => {
                if (err) {
                    console.error("Error inserting document: " + err);

                    // keep the current stuff
                    req.session.kb_title = req.body.frm_kb_title;
                    req.session.kb_body = req.body.frm_kb_body;
                    req.session.kb_keywords = req.body.frm_kb_keywords;
                    req.session.kb_permalink = req.body.frm_kb_permalink;

                    req.session.message = req.i18n.__("Error") + ": " + err;
                    req.session.message_type = "danger";

                    // redirect to insert
                    res.redirect(req.app_context + "/insert");
                } else {
                    console.log('INSERTED NEW DOC', newDoc);
                    let newId = newDoc.insertedIds["0"].toString();

                    //recycle doc
                    doc.kb_versioned_doc = true;
                    doc.kb_password = "";
                    doc.kb_published = false;
                    doc.kb_edit_reason = "Initial Article Created";
                    doc.kb_versioned_doc = true;
                    doc.kb_parent_id = newId;

                    delete doc._id;

                    // insert a doc to track versioning
                    db.kb.insert(doc, (err, version_doc) => {
                        if (err) {
                            console.log("Error while inserting versioned doc", err);
                        } else {
                            console.log("Inserted versioned doc as Initial Article history.", version_doc);
                        }
                    });

                    req.session.message = req.i18n.__("New article successfully created");
                    req.session.message_type = "success";

                    syncArticlesToAlgolia();

                    // redirect to new doc
                    res.redirect(req.app_context + "/edit/" + newId);

                }
            });
        }
    });
});


// Update an existing KB article form action
router.get("/suggest", common.suggest_allowed, (req, res) => {
    // set the template dir
    //common.setTemplateDir('admin', req);

    res.render("suggest", {
        title: t("Suggest article"),
        config: config,
        editor: true,
        is_admin: req.session.is_admin,
        helpers: req.handlebars,
        message: common.clear_session_value(req.session, "message"),
        message_type: common.clear_session_value(req.session, "message_type"),
        session: req.session,
    });
});

// Update an existing KB article form action
router.post("/insert_suggest", common.suggest_allowed, async (req, res) => {
    const db = await getDb();

    // if empty, remove the comma and just have a blank string
    let keywords = req.body.frm_kb_keywords.replace(/<(?:.|\n)*?>/gm, "");
    if (common.safe_trim(keywords) === ",") {
        keywords = "";
    }

    let doc = {
        kb_title: req.body.frm_kb_title + " (SUGGESTION)",
        kb_body: req.body.frm_kb_body,
        kb_published: "false",
        kb_keywords: keywords,
        kb_published_date: new Date(),
        kb_last_updated: new Date(),
    };

    db.kb.insert(doc, (err, newDoc) => {
        if (err) {
            console.error("Error inserting suggestion: " + err);
            req.session.message = t("Suggestion failed. Please contact admin.");
            req.session.message_type = "danger";
            res.redirect(req.app_context + "/");
        } else {
            // redirect to new doc
            req.session.message = t("Suggestion successfully processed");
            req.session.message_type = "success";
            res.redirect(req.app_context + "/");
        }
    });
});

// Update an existing KB article form action
router.post("/save_kb", common.restrict, async (req, res) => {
    const db = await getDb();

    let kb_featured = req.body.frm_kb_featured === "on" ? "true" : "false";
    let kb_pinned = req.body.frm_kb_pinned === "on" ? "true" : "false";
    let kb_dashboard = req.body.frm_kb_dashboard === "on" ? "true" : "false";
    let kb_is_markdown = req.body.frm_kb_is_markdown === "on" ? true : false;

    // if empty, remove the comma and just have a blank string
    let keywords = req.body.frm_kb_keywords.replace(/<(?:.|\n)*?>/gm, "");
    if (common.safe_trim(keywords) === ",") {
        keywords = "";
    }

    db.kb.count(
        {
            kb_permalink: req.body.frm_kb_permalink,
            _id: { $ne: common.getId(req.body.frm_kb_id) },
            kb_versioned_doc: { $ne: true },
        },
        (err, kb) => {
            if (kb > 0 && req.body.frm_kb_permalink !== "") {
                // permalink exits
                req.session.message = t("Permalink already exists. Pick a new one.");
                req.session.message_type = "danger";

                // keep the current stuff
                req.session.kb_title = req.body.frm_kb_title;
                req.session.kb_body = req.body.frm_kb_body;
                req.session.kb_keywords = req.body.frm_kb_keywords;
                req.session.kb_permalink = req.body.frm_kb_permalink;
                req.session.kb_featured = kb_featured;
                req.session.kb_pinned = kb_pinned;
                req.session.kb_dashboard = kb_dashboard;
                req.session.kb_pinned_topic = req.body.frm_kb_pinned_topic;
                req.session.kb_seo_title = req.body.frm_kb_seo_title;
                req.session.kb_seo_description = req.body.frm_kb_seo_description;
                req.session.kb_edit_reason = req.body.frm_kb_edit_reason;
                req.session.kb_visible_state = req.body.frm_kb_visible_state;
                req.session.kb_security_level = parseInt(req.body.frm_kb_security_level);
                req.session.kb_regional_visibility = parseInt(req.body.frm_kb_regional_visibility);
                req.session.kb_pinned_subtopic = req.body.frm_kb_pinned_subtopic;

                syncArticlesToAlgolia();
                // redirect to insert
                res.redirect(req.app_context + "/edit/" + req.body.frm_kb_id);
            } else {
                db.kb.findOne({ _id: common.getId(req.body.frm_kb_id) }, async (err, article) => {

                    console.log('FOUND ID', article._id);

                    // update author if not set
                    let author = article.kb_author ? article.kb_author : req.session.users_name;
                    let author_email = article.kb_author_email ? article.kb_author_email : req.session.user;

                    // set published date to now if none exists
                    let published_date;
                    // if (article.kb_published_date == null || article.kb_published_date === undefined) {
                    //     published_date = new Date();
                    // } else {
                    //     published_date = article.kb_published_date;
                    // }

                    //update kb_published date when there's no published date and article is tagged as published
                    if (JSON.parse(req.body.frm_kb_published) && (article.kb_published_date == null || article.kb_published_date === undefined)) {
                        published_date = new Date();
                    } else {
                        //for older articles - it might have a value that is not a valid date
                        if (isNaN(Date.parse(article.kb_published_date))) {
                            published_date = new Date();
                        } else {
                            published_date = article.kb_published_date;
                        }
                    }

                    console.log('Published Date: ', published_date);

                    const titleTranslation = await common.translateTitle(req, req.body.frm_kb_title);

                    let updateObject = {
                        kb_title: req.body.frm_kb_title,
                        kb_body: req.body.frm_kb_body,
                        kb_published: req.body.frm_kb_published,
                        kb_keywords: keywords,
                        kb_last_updated: new Date(),
                        kb_last_update_user: req.session.users_name + " - " + req.session.user,
                        kb_author: author,
                        kb_author_email: author_email,
                        kb_published_date: published_date,
                        kb_password: req.body.frm_kb_password,
                        kb_permalink: req.body.frm_kb_permalink,
                        kb_featured: kb_featured,
                        kb_pinned: kb_pinned,
                        kb_dashboard: kb_dashboard,
                        kb_pinned_topic: kb_pinned === 'true' ? req.body.frm_kb_pinned_topic : '',
                        kb_seo_title: req.body.frm_kb_seo_title,
                        kb_seo_description: req.body.frm_kb_seo_description,
                        kb_visible_state: req.body.frm_kb_visible_state,
                        kb_security_level: parseInt(req.body.frm_kb_security_level),
                        kb_regional_visibility: req.body.frm_kb_regional_visibility,
                        kb_pinned_subtopic: kb_pinned === 'true' ? req.body.frm_kb_pinned_subtopic : '',
                        kb_translation_title: titleTranslation,
                        kb_is_markdown: kb_is_markdown
                    }

                    // update our old doc
                    db.kb.update(
                        { _id: common.getId(req.body.frm_kb_id) },
                        { $set: updateObject },
                        {}, async (err, numReplaced) => {
                            if (err) {
                                console.error("Failed to save KB: " + err);
                                req.session.message = t("Failed to save. Please try again");
                                req.session.message_type = "danger";
                                res.redirect(req.app_context + "/edit/" + req.body.frm_kb_id);
                            } else {
                                syncArticlesToAlgolia();

                                // version doc
                                let version_doc = {
                                    kb_title: req.body.frm_kb_title,
                                    // kb_parent_id: req.body.frm_kb_id,
                                    kb_parent_id: article._id.toString(),
                                    kb_versioned_doc: true,
                                    kb_edit_reason: req.body.frm_kb_edit_reason,
                                    kb_body: req.body.frm_kb_body,
                                    kb_published: false,
                                    kb_keywords: keywords,
                                    kb_last_updated: new Date(),
                                    kb_last_update_user: req.session.users_name + " - " + req.session.user,
                                    kb_author: author,
                                    kb_author_email: author_email,
                                    kb_published_date: published_date,
                                    kb_password: req.body.frm_kb_password,
                                    kb_permalink: req.body.frm_kb_permalink,
                                    kb_featured: kb_featured,
                                    kb_pinned: kb_pinned,
                                    kb_dashboard: kb_dashboard,
                                    kb_pinned_topic: kb_pinned === 'true' ? req.body.kb_pinned_topic : '',
                                    kb_seo_title: req.body.frm_kb_seo_title,
                                    kb_seo_description: req.body.frm_kb_seo_description,
                                    kb_visible_state: req.body.frm_kb_visible_state,
                                    kb_security_level: parseInt(req.body.frm_kb_security_level),
                                    kb_regional_visibility: req.body.frm_kb_regional_visibility,
                                    kb_pinned_subtopic: kb_pinned === 'true' ? req.body.frm_kb_pinned_subtopic : '',
                                };

                                // insert a doc to track versioning
                                db.kb.insert(version_doc, (err, version_doc) => {
                                    console.log('Done Saving version');
                                    req.session.message = t("Successfully saved");
                                    req.session.message_type = "success";
                                    res.redirect(req.app_context + "/edit/" + req.body.frm_kb_id);
                                });
                                // } else {
                                //     req.session.message = req.i18n.__("Successfully saved");
                                //     req.session.message_type = "success";
                                //     res.redirect(req.app_context + "/edit/" + req.body.frm_kb_id);
                                // }
                            }
                        }
                    );
                });
            }
        }
    );
});

// logout
router.get("/logout", (req, res) => {
    req.session.user = null;
    req.session.users_name = null;
    req.session.is_admin = null;
    req.session.pw_validated = null;
    req.session.message = null;
    req.session.message_type = null;
    res.redirect(req.app_context + "/");
});

// users
router.get("/users", common.restrict, async (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: t("Access denied"),
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    const db = await getDb();
    common.dbQuery(db.users, {}, null, null, (err, users) => {
        res.render("users", {
            title: t("Users"),
            users: users,
            config: config,
            is_admin: req.session.is_admin,
            helpers: req.handlebars,
            session: req.session,
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
        });
    });
});

// users
router.get("/user/edit/:id", common.restrict, async (req, res) => {
    const db = await getDb();
    db.users.findOne({ _id: common.getId(req.params.id) }, (err, user) => {
        // if the user we want to edit is not the current logged in user and the current user is not
        // an admin we render an access denied message
        if (user.user_email !== req.session.user && req.session.is_admin === "false") {
            req.session.message = t("Access denied");
            req.session.message_type = "danger";
            res.redirect(req.app_context + "/Users/");
            return;
        }

        res.render("user_edit", {
            title: t("User edit"),
            user: user,
            session: req.session,
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
            helpers: req.handlebars,
            config: config,
        });
    });
});

// users
router.get("/users/new", common.restrict, (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: "Access denied",
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    res.render("user_new", {
        title: "User - New",
        session: req.session,
        message: common.clear_session_value(req.session, "message"),
        message_type: common.clear_session_value(req.session, "message_type"),
        config: config,
        helpers: req.handlebars,
    });
});

// kb list
router.get("/articles", common.restrict, (req, res) => {
    //just in case this route is hit, redirect to the correct route
    res.redirect(req.app_context + "/settings/articles");
});

router.get("/articles/all", common.restrict, async (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: "Access denied",
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    const db = await getDb();

    common.dbQuery(db.kb, { kb_versioned_doc: { $ne: true } }, { kb_published_date: -1 }, null, (err, articles) => {
        res.render("articles", {
            title: "Articles",
            articles: articles,
            session: req.session,
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
            config: config,
            helpers: req.handlebars,
        });
    });
});

router.get("/articles/minimal", async (req, res) => {
    const db = await getDb();

    //NOTE : displaying specific fields requires the `projection` property for versions 3.1 and above
    db.kb.find({ kb_versioned_doc: { $ne: true } }, {
        projection: {
            kb_title: 1,
            kb_published_date: 1,
            kb_pinned_topic: 1,
            kb_pinned_subtopic: 1,
            kb_published: 1,
            kb_regional_visibility: 1,
            kb_security_level: 1,
            kb_viewcount: 1,
            kb_translation_title: 1
        }
    })
        .sort({ kb_last_updated: -1 })
        .toArray((err, results) => {
            if (err) {
                return res.status(400).json({ message: "fail" });
            } else {
                return res.status(200).json({ message: "success", data: results });
            }
        });
});

router.get("/articles/:tag", async (req, res) => {
    const db = await getDb();
    let searchTerm = req.params.tag;

    common.dbQuery(db.kb, {}, { kb_published_date: -1 }, null, (err, results) => {
        res.render("articles", {
            title: t("Articles"),
            results: results,
            session: req.session,
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
            search_term: searchTerm,
            config: config,
            helpers: req.handlebars,
        });
    });
});

// update the published state based on an ajax call from the frontend
router.post("/published_state", common.restrict, async (req, res) => {
    const db = await getDb();
    db.kb.update(
        { _id: common.getId(req.body.id) },
        { $set: { kb_published: req.body.state } },
        { multi: false },
        (err, numReplaced) => {
            if (err) {
                console.error("Failed to update the published state: " + err);
                res.writeHead(400, { "Content-Type": "application/text" });
                res.end("Published state not updated");
            } else {
                syncArticlesToAlgolia();
                res.writeHead(200, { "Content-Type": "application/text" });
                res.end("Published state updated");
            }
        }
    );
});

// update a user
router.post("/user_update", common.restrict, async (req, res) => {
    const db = await getDb();
    let bcrypt = req.bcrypt;
    let is_admin = req.body.user_admin === "on" ? "true" : "false";

    // get the user we want to update
    db.users.findOne({ _id: common.getId(req.body.user_id) }, (err, user) => {
        // if the user we want to edit is not the current logged in user and the current user is not
        // an admin we render an access denied message
        if (user.user_email !== req.session.user && req.session.is_admin === "false") {
            req.session.message = req.i18n.__("Access denied");
            req.session.message_type = "danger";
            res.redirect(req.app_context + "/Users/");
            return;
        }

        // if editing your own account, retain admin true/false
        if (user.user_email === req.session.user) {
            is_admin = user.is_admin;
        }

        // create the update doc
        let update_doc = {};
        const saltRounds = 10;
        update_doc.is_admin = is_admin;
        update_doc.users_name = req.body.users_name;
        if (req.body.user_password) {
            update_doc.user_password = bcrypt.hashSync(req.body.user_password, saltRounds);
        }

        db.users.update(
            { _id: common.getId(req.body.user_id) },
            {
                $set: update_doc,
            },
            { multi: false },
            (err, numReplaced) => {
                if (err) {
                    console.error("Failed updating user: " + err);
                    req.session.message = req.i18n.__("Failed to update user");
                    req.session.message_type = "danger";
                    res.redirect(req.app_context + "/user/edit/" + req.body.user_id);
                } else {
                    // show the view
                    req.session.message = req.i18n.__("User account updated.");
                    req.session.message_type = "success";
                    res.redirect(req.app_context + "/user/edit/" + req.body.user_id);
                }
            }
        );
    });
});

// setup form is shown when there are no users setup in the DB
router.get("/setup", async (req, res) => {
    const db = await getDb();
    db.users.count({}, (err, user_count) => {
        // dont allow the user to "re-setup" if a user exists.
        // set needs_setup to false as a user exists
        req.session.needs_setup = false;
        if (user_count !== 0) {
            res.render("setup", {
                title: "Setup",
                config: config,
                message: common.clear_session_value(req.session, "message"),
                message_type: common.clear_session_value(req.session, "message_type"),
                show_footer: "show_footer",
                helpers: req.handlebars,
            });
        } else {
            // res.redirect(req.app_context + '/login');
            res.redirect(req.app_context + "/");
        }
    });
});

// Loops files on the disk, checks for their existance in any KB articles and removes non used files.
router.get("/file_cleanup", common.restrict, async (req, res) => {
    const db = await getDb();
    let walkPath = path.join(appDir, "public", "uploads", "inline_files");
    let walker = walk.walk(walkPath, { followLinks: false });

    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: t("Access denied"),
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    walker.on("file", (root, stat, next) => {
        let file_name = path.resolve(root, stat.name);

        // find posts with the file in question
        common.dbQuery(db.kb, { kb_body: new RegExp(stat.name) }, null, null, (err, posts) => {
            // if the images doesn't exists in any posts then we remove it
            if (posts.length === 0) {
                fs.unlinkSync(file_name);
            }
            next();
        });
    });

    walker.on("end", () => {
        req.session.message = t("All unused files have been removed");
        req.session.message_type = "success";
        res.redirect(req.app_context + req.header("Referer"));
    });
});

// login the user and check the password
router.post("/login_action", async (req, res) => {
    const db = await getDb();
    let bcrypt = req.bcrypt;

    db.users.findOne({ user_email: req.body.email }, (err, user) => {
        // check if user exists with that email
        if (user === undefined || user === null) {
            req.session.message = req.i18n.__("A user with that email does not exist.");
            req.session.message_type = "danger";
            // res.redirect(req.app_context + '/login');
            res.redirect(req.app_context + "/");
        } else {
            // we have a user under that email so we compare the password
            if (bcrypt.compareSync(req.body.password, user.user_password) === true) {
                req.session.user = req.body.email;
                req.session.users_name = user.users_name;
                req.session.user_id = user._id.toString();
                req.session.is_admin = user.is_admin;
                if (req.body.frm_referring_url === undefined || req.body.frm_referring_url === "") {
                    res.redirect(req.app_context + "/");
                } else {
                    // eslint-disable-next-line node/no-deprecated-api
                    let url_parts = url.parse(req.body.frm_referring_url, true);
                    if (url_parts.pathname !== "/setup" && url_parts.pathname !== req.app_context + "/login") {
                        res.redirect(req.body.frm_referring_url);
                    } else {
                        res.redirect(req.app_context + "/");
                    }
                }
            } else {
                // password is not correct
                req.session.message = t("Access denied. Check password and try again.");
                req.session.message_type = "danger";
                // res.redirect(req.app_context + '/login');
                res.redirect(req.app_context + "/");
            }
        }
    });
});

// delete user
router.get("/user/delete/:id", common.restrict, async (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: t("Access denied"),
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    const db = await getDb();
    // remove the article
    if (req.session.is_admin === "true") {
        db.users.remove({ _id: common.getId(req.params.id) }, {}, (err, numRemoved) => {
            req.session.message = t("User deleted.");
            req.session.message_type = "success";
            res.redirect(req.app_context + "/users");
        });
    } else {
        req.session.message = t("Access denied.");
        req.session.message_type = "danger";
        res.redirect(req.app_context + "/users");
    }
});

router.get("/delete/:id", common.restrict, async (req, res) => {
    const db = await getDb();

    db.kb.remove({ _id: common.getId(req.params.id) }, {}, (err, numRemoved) => {
        const successMessage = t("Article successfully deleted");

        // Check if modern toasts are enabled for wiki_editor
        const useModernToasts = config.settings.use_modern_toasts &&
            config.settings.use_modern_toasts.wiki_editor;

        if (useModernToasts) {
            // Use modern toast system - add notification parameters to URL
            const redirectUrl = req.app_context + "/settings/articles" +
                "?notification_message=" + encodeURIComponent(successMessage) +
                "&notification_type=success";
            res.redirect(redirectUrl);
        } else {
            // Use legacy session message system
            req.session.message = successMessage;
            req.session.message_type = "success";
            res.redirect(req.app_context + "/settings/articles");
        }
    });
});

const inline_upload = multer_upload({
    dest: path.join(appDir, "public", "uploads", "inline_files"),
});
router.post("/file/upload_file", common.restrict, inline_upload.single("file"), (req, res, next) => {
    if (req.file) {
        // check for upload select
        const upload_dir = path.join(appDir, "public", "uploads", "inline_files");
        const relative_upload_dir = req.app_context + "/uploads/inline_files";

        const file = req.file;
        const source = fs.createReadStream(file.path);
        const dest = fs.createWriteStream(path.join(upload_dir, file.originalname));

        // save the new file
        source.pipe(dest);
        source.on("end", () => { });

        // delete the temp file.
        fs.unlink(file.path, (err) => { });

        // uploaded
        res.writeHead(200, { "Content-Type": "application/json" });
        res.end(
            JSON.stringify({
                filename: relative_upload_dir + "/" + file.originalname,
            })
        );
        return;
    }
    res.writeHead(500, { "Content-Type": "application/json" });
    res.end(JSON.stringify({ filename: "fail" }, null, 3));
});

router.post("/file/new_dir", common.restrict, (req, res, next) => {
    // if new directory exists
    if (req.body.custom_dir) {
        mkdirp(path.join(appDir, "public", "uploads", req.body.custom_dir), (err) => {
            if (err) {
                console.error("Directory creation error: " + err);
                req.session.message = t("Directory creation error. Please try again");
                req.session.message_type = "danger";
                res.redirect(req.app_context + "/files");
            } else {
                req.session.message = t("Directory successfully created");
                req.session.message_type = "success";
                res.redirect(req.app_context + "/files");
            }
        });
    } else {
        req.session.message = t("Please enter a directory name");
        req.session.message_type = "danger";
        res.redirect(req.app_context + "/files");
    }
});

// upload the file

let upload = multer({ dest: path.join(appDir, "public", "uploads") });
router.post("/file/upload", common.restrict, upload.single("upload_file"), (req, res, next) => {
    if (req.file) {
        // check for upload select
        let upload_dir = path.join(appDir, "public", "uploads");
        if (req.body.directory !== "/uploads") {
            upload_dir = path.join(appDir, "public/", req.body.directory);
        }

        let file = req.file;
        let source = fs.createReadStream(file.path);
        let dest = fs.createWriteStream(path.join(upload_dir, file.originalname.replace(/ /g, "_")));

        // save the new file
        source.pipe(dest);
        source.on("end", () => { });

        // delete the temp file.
        fs.unlink(file.path, (err) => { });

        req.session.message = t("File uploaded successfully");
        req.session.message_type = "success";
        res.redirect(req.app_context + "/files");
    } else {
        req.session.message = t("File upload error. Please select a file.");
        req.session.message_type = "danger";
        res.redirect(req.app_context + "/files");
    }
});

// delete a file via ajax request
router.post("/file/delete", common.restrict, (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.writeHead(400, { "Content-Type": "application/text" });
        res.end("Access denied");
        return;
    }

    req.session.message = null;
    req.session.message_type = null;

    fs.unlink("public/" + req.body.img, (err) => {
        if (err) {
            console.error("File delete error: " + err);
            res.status(400).send("Failed to delete file");
        } else {
            res.status(200).send("File deleted successfully");
        }
    });
});

router.get("/files", common.restrict, (req, res) => {
    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: t("Access denied"),
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    // loop files in /public/uploads/
    glob("public/uploads/**", { nosort: true }, (er, files) => {
        // sort array
        files.sort();

        // declare the array of objects
        let file_list = [];
        let dir_list = [];

        // loop these files
        for (let i = 0; i < files.length; i++) {
            if (fs.existsSync(files[i])) {
                if (fs.lstatSync(files[i]).isDirectory() === false) {
                    // declare the file object and set its values
                    let file = {
                        id: i,
                        path: files[i].substring(6),
                    };

                    // push the file object into the array
                    file_list.push(file);
                } else {
                    let dir = {
                        id: i,
                        path: files[i].substring(6),
                    };

                    // push the dir object into the array
                    dir_list.push(dir);
                }
            }
        }

        // render the files route
        res.render("files", {
            title: "Files",
            files: file_list,
            dirs: dir_list,
            session: req.session,
            config: config,
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
            helpers: req.handlebars,
        });
    });
});

// insert form
router.get("/insert", common.restrict, async (req, res) => {

    const languages = await common.getLanguagesOptions(req)

    //empty article object
    const article = {

        //kb_permalink: req.body.frm_kb_permalink,
        kb_title: '',
        kb_body: '',
        kb_published: '',
        kb_keywords: '',
        kb_regional_visibility: "",
        kb_is_markdown: false
    }

    const possibleCountries = await common.getCountries();

    if (req.session.is_pseudoadmin === true) {
        const filteredCountries = possibleCountries.filter((c) => {
            return req.session.user_regions.findIndex(r => {
                return r.country === c.full_name
            }) !== -1;
        }).map(c => { return c.full_name });

        article.countries = filteredCountries;
        article.possibleCountries = filteredCountries;
        article.kb_regional_visibility = filteredCountries.join(',');
    } else {
        article.countries = possibleCountries.map(c => { return c.full_name });
        article.possibleCountries = possibleCountries.map(c => { return c.full_name });
        article.kb_regional_visibility = article.kb_regional_visibility ? article.kb_regional_visibility.split(/(?:,)\s*/i).filter((c) => {
            return article.countries.includes(c);
        }).join(',') : '';
    }

    res.render("form", {
        title: t("Insert new"),
        result: article,
        session: req.session,
        is_pseudoadmin: req.session.is_pseudoadmin,
        kb_title: common.clear_session_value(req.session, "kb_title"),
        kb_body: common.clear_session_value(req.session, "kb_body"),
        kb_keywords: common.clear_session_value(req.session, "kb_keywords"),
        kb_permalink: common.clear_session_value(req.session, "kb_permalink"),
        message: common.clear_session_value(req.session, "message"),
        message_type: common.clear_session_value(req.session, "message_type"),
        editor: true,
        helpers: req.handlebars,
        config: config,
        languages: languages
    });
});

// redirect home with a null topic
router.get("/topic", (req, res) => {
    res.redirect("/");
});

router.get('/topic/:id/articles', common.restrict, async (req, res) => {
    common.config_expose(req.app);
    const topicId = req.params.id;
    const subTopicId = req.query.subtopic;

    //the selected club id in perfhub
    //let search_club = req.params.club;
    const search_club = req.session.clubId;

    if (!search_club || search_club === "") {
        res.render("error", {
            message: t("Search Club is not set. Please set the club in your profile."),
            helpers: req.handlebars,
            config: config,
        });

        return;
    }

    //check if there are clubs
    const clubCountry = await common.getCountryOfClub(search_club);

    try {
        const algoliaConfig = await getAlgoliaConfig();
        const topic = await getTopicDetails(topicId, clubCountry);

        if (!topic) {
            return res.render("error", {
                message: t("Topic not found or not available for your facility or user. Please contact your administrator to gain access to this resource."),
                helpers: req.handlebars,
                config: config,
            });
        }

        topic.subtopics.map((subtopic) => {
            subtopic.cleanSubtopic = subtopic.subtopic.replace(/\s/g, "");

            return subtopic;
        })

        res.render("articles_by_topic", {
            layout: '../../public/themes/12rnd/views/layouts/layout',
            title: t("Topic:") + " " + topic.topic,
            session: req.session,
            message: common.clear_session_value(req.session, "message"),
            message_type: common.clear_session_value(req.session, "message_type"),
            config: config,
            helpers: req.handlebars,
            topic: topic,
            show_footer: "show_footer",
            subTopics: topic.subtopics,
            subtopicId: subTopicId ?? '',
            algoliaConfig,
            club: search_club
        });
    } catch (err) {
        console.log("Something went wrong: ", err);
    }

});


// search via subtopic
router.get(["/subtopic/:tag"], common.restrict, async (req, res) => {
    const db = await getDb();

    common.config_expose(req.app);
    let search_term = req.params.tag;
    let search_club = req.session.clubId;

    let featuredCount = config.settings.featured_articles_count ? config.settings.featured_articles_count : 4;
    const sortBy = { kb_published_date: -1 };

    //check if there are clubs
    if (search_club && search_club !== "") {
        //get the country of selected club before doing the actual query
        common.getCountryOfClub(search_club).then((country) => {
            let regExpr = new RegExp(".*" + country + ".*", "i");

            //get the featured articles first
            common.dbQuery(
                db.kb,
                { kb_published: "true", kb_featured: "true", kb_pinned_subtopic: search_term, kb_versioned_doc: { $ne: true } },
                sortBy,
                featuredCount,
                (err, featured_results) => {
                    if (err) {
                        console.log("Error while getting featured results", err);
                    }

                    //actual query
                    common.dbQuery(
                        db.kb,
                        {
                            kb_published: "true",
                            kb_pinned: "true",
                            kb_pinned_subtopic: search_term,
                            kb_security_level: { $lte: req.session.user_level },
                            $or: [{ kb_regional_visibility: "" }, { kb_regional_visibility: { $regex: regExpr } }],
                        },
                        sortBy,
                        100,
                        (err, pinned_results) => {
                            res.render("search_results", {
                                title: t("Search results:") + " " + search_term,
                                // search_results: results,
                                search_results: pinned_results,
                                user_page: true,
                                searchfor: "Subtopic",
                                session: req.session,
                                featured_results: featured_results,
                                pinned_results: pinned_results,
                                routeType: "topic",
                                message: common.clear_session_value(req.session, "message"),
                                message_type: common.clear_session_value(req.session, "message_type"),
                                search_term: search_term,
                                config: config,
                                helpers: req.handlebars,
                                show_footer: "show_footer",
                            });
                        }
                    );
                }
            );
        });
        //});
    } else {
        //search Subtopic without Club

        //get the featured articles first
        common.dbQuery(
            db.kb,
            { kb_published: "true", kb_featured: "true", kb_pinned_subtopic: search_term, kb_versioned_doc: { $ne: true } },
            sortBy,
            featuredCount,
            (err, featured_results) => {
                if (err) {
                    console.log("Error while getting featured results", err);
                }

                //actual query
                common.dbQuery(
                    db.kb,
                    {
                        kb_published: "true",
                        kb_pinned: "true",
                        kb_pinned_subtopic: search_term,
                        kb_security_level: { $lte: req.session.user_level },
                        kb_regional_visibility: "",
                    },
                    sortBy,
                    100,
                    (err, pinned_results) => {
                        res.render("search_results", {
                            title: t("Search results:") + " " + search_term,
                            search_results: pinned_results,
                            user_page: true,
                            searchfor: "Subtopic",
                            session: req.session,
                            featured_results: featured_results,
                            pinned_results: pinned_results,
                            message: common.clear_session_value(req.session, "message"),
                            message_type: common.clear_session_value(req.session, "message_type"),
                            search_term: search_term,
                            config: config,
                            helpers: req.handlebars,
                            show_footer: "show_footer",
                        });
                    }
                );
            }
        );
    }
});

//this route recieves the GET request when page is reloaded after searching in PerfHub..
router.get("/search", common.restrict, async (req, res) => {
    const db = await getDb();
    common.config_expose(req.app);
    let search_term = req.session.searchTerm;
    let search_club = req.session.clubId;

    const _ids = []

    data.hits.forEach(_article => {
        _ids.push(_article._id)
    })

    console.log("---> FROM GET SEARCH ROUTE : SESSION SEARCH TERM : ", req.session.searchTerm);
    const sortBy = { kb_published_date: -1 };

    //check if there are clubs
    if (search_club && search_club !== "") {
        //get the country of selected club before doing the actual query
        common.getCountryOfClub(search_club).then((country) => {
            let regExpr = new RegExp(".*" + country + ".*", "i");
            console.log("---> Country: " + country);

            common.dbQuery(
                db.kb,
                {
                    _id: { $in: _ids },
                    kb_published: "true",
                    kb_versioned_doc: { $ne: true },
                    kb_security_level: { $lte: req.session.user_level },
                    $or: [{ kb_regional_visibility: "" }, { kb_regional_visibility: { $regex: regExpr } }],
                },
                sortBy,
                9999999,
                (err, results) => {
                    if (err) {
                        console.error("Error on Regular Search with ClubId" + err);
                        return res.status(500).json({ message: "fail" });
                    }

                    res.render("search_results", {
                        title: t("Search results:") + " " + search_term,
                        search_results: results,
                        user_page: true,
                        session: req.session,
                        search_term: search_term,
                        featured_results: [],
                        routeType: "search",
                        // pinned_results: pinned_results,
                        message: common.clear_session_value(req.session, "message"),
                        message_type: common.clear_session_value(req.session, "message_type"),
                        config: config,
                        helpers: req.handlebars,
                        show_footer: "show_footer",
                    });
                }
            );
            // });
        });
    } else {
        console.log("Regular Search with no clubId");

        common.dbQuery(
            db.kb,
            {
                _id: { $in: _ids },
                kb_published: "true",
                kb_versioned_doc: { $ne: true },
                kb_security_level: { $lte: req.session.user_level },
                kb_regional_visibility: "",
            },
            sortBy,
            9999999,
            (err, results) => {
                if (err) {
                    console.error("Error on Regular Search with ClubId" + err);
                    return res.status(500).json({ message: "fail" });
                }

                res.render("search_results", {
                    title: t("Search results:") + " " + search_term,
                    search_results: results,
                    user_page: true,
                    session: req.session,
                    search_term: search_term,
                    routeType: "search",
                    featured_results: [],
                    message: common.clear_session_value(req.session, "message"),
                    message_type: common.clear_session_value(req.session, "message_type"),
                    config: config,
                    helpers: req.handlebars,
                    show_footer: "show_footer",
                });
            }
        );
    }
});

// Manual Search - this is where we record the search analytics
router.post("/search", common.restrict, async (req, res) => {
    const db = await getDb();
    common.config_expose(req.app);
    let search_term = req.body.frm_search;
    //the selected club id in perfhub
    let search_club = req.session.clubId;

    //store search_term in session
    req.session.searchTerm = search_term;

    console.log(" ----> REGULAR search with term: " + search_term);
    console.log(" ----> REGULAR search with club: " + search_club);

    console.log("SESSION SEARCH TERM : ", req.session.searchTerm);

    const sortBy = { kb_published_date: -1 }


    //save search data
    async function saveSearchData(user, club, keyword, no_of_results) {
        /*schema:
                user
                club
                keyword
                no_of_results
                datetime
        */

        const today = new Date();
        //let newEntry = {user, club, keyword, no_of_results, date: today.toISOString().split('T')[0], datetime: today};

        //searches table - always insert, no update - this way, we can track what EACH USER searched
        // db.searches.insert(newEntry, (err, searchEntry) => {
        // 	//just logs, no returns
        // 	if (err) {
        // 		console.error("searches: Failed to save new search data: " + err);
        // 	} else {
        // 		console.log('searches: Inserted new search data');
        // 	}
        // });

        //this is for search totals - no user and club but with count
        let newSearchTotal = { keyword, count: 1, no_of_results, date: today.toISOString().split('T')[0], datetime: today }

        //1 find with the same keyword and date
        db.searchtotals.findOne({ keyword: search_term, date: today.toISOString().split('T')[0] }, (err, foundSearch) => {
            if (err) {
                console.log('Error while finding in searchtotals:', err);
            }

            if (foundSearch) {
                //needs to update the count
                db.searchtotals.update(
                    { _id: common.getId(foundSearch._id) },
                    {
                        $set: {
                            count: parseInt(foundSearch.count) + 1,
                            no_of_results: parseInt(no_of_results)
                        },
                    },
                    {},
                    (err, numReplaced) => {
                        if (err) {
                            console.error("searchtotals: Failed to save changes: " + err);
                        } else {
                            console.error("searchtotals: Changes saved! : ");
                        }
                    }
                );
            } else {
                //2 insert or update
                db.searchtotals.insert(newSearchTotal, (err, searchEntry) => {
                    //just logs, no returns
                    if (err) {
                        console.error("Failed to save new searchtotals data: " + err);
                    } else {
                        console.log('Inserted new searchtotals data');
                    }
                });
            }

        });
    }

    //check if there are clubs
    if (search_club && search_club !== "") {
        //get the country of selected club before doing the actual query
        common.getCountryOfClub(search_club).then((country) => {
            let regExpr = new RegExp(".*" + country + ".*", "i");
            console.log("---> Country: " + country);

            common.dbQuery(
                db.kb,
                {
                    kb_published: "true",
                    kb_versioned_doc: { $ne: true },
                    kb_security_level: { $lte: req.session.user_level },
                    $or: [{ kb_regional_visibility: "" }, { kb_regional_visibility: { $regex: regExpr } }],
                },
                sortBy,
                9999999,
                (err, results) => {
                    if (err) {
                        console.error("Error on Regular Search with ClubId" + err);
                        return res.status(500).json({ message: "fail" });
                    }

                    console.log('search results: ', results.length);

                    //save search
                    saveSearchData(req.session.user, search_club, search_term, results.length);

                    res.render("search_results", {
                        title: t("Search results:") + " " + search_term,
                        search_results: results,
                        user_page: true,
                        session: req.session,
                        search_term: search_term,
                        featured_results: [],
                        routeType: "search",
                        //pinned_results: pinned_results,
                        message: common.clear_session_value(req.session, "message"),
                        message_type: common.clear_session_value(req.session, "message_type"),
                        config: config,
                        helpers: req.handlebars,
                        show_footer: "show_footer",
                    });
                }
            );
            // });
        });
    } else {
        console.log("Regular Search with no clubId");

        common.dbQuery(
            db.kb,
            {
                kb_published: "true",
                kb_versioned_doc: { $ne: true },
                kb_security_level: { $lte: req.session.user_level },
                kb_regional_visibility: "",
            },
            sortBy,
            99999999,
            (err, results) => {
                if (err) {
                    console.error("Error on Regular Search with ClubId" + err);
                    return res.status(500).json({ message: "fail" });
                }

                //save search
                saveSearchData(req.session.user, 'none', search_term, results.length);

                res.render("search_results", {
                    title: t("Search results:") + " " + search_term,
                    search_results: results,
                    user_page: true,
                    session: req.session,
                    search_term: search_term,
                    routeType: "search",
                    featured_results: [],
                    message: common.clear_session_value(req.session, "message"),
                    message_type: common.clear_session_value(req.session, "message_type"),
                    config: config,
                    helpers: req.handlebars,
                    show_footer: "show_footer",
                });
            }
        );
    }
});

// import form
router.get("/import", common.restrict, (req, res) => {
    res.render("import", {
        title: t("Import"),
        session: req.session,
        helpers: req.handlebars,
        message: common.clear_session_value(req.session, "message"),
        message_type: common.clear_session_value(req.session, "message_type"),
        config: config,
    });
});

router.post("/importer", common.restrict, upload.single("import_file"), async (req, res, next) => {
    const db = await getDb();
    let file = req.file;

    // check for allowed file type
    let checkMime = _.includes("application/zip", mime.lookup(file.originalname));
    if (checkMime === false) {
        // clean up temp file
        fs.unlinkSync(file.path);

        // return error
        res.writeHead(400, { "Content-Type": "application/text" });
        res.end("File type not permitted. Please upload a zip of Markdown documents.");
        return;
    }

    // extract our zip
    zipExtract(file.path, { dir: path.join(__dirname, "..", "public", "temp", "import") }, (err) => {
        // remove the zip
        fs.unlinkSync(file.path);

        // loop extracted files
        fs.readdir(path.join(__dirname, "..", "public", "temp", "import"), (err, files) => {
            files.forEach((file) => {
                // check for blank permalink field and set a nice one base on the title of the FAQ
                let fileNoExt = file.replace(/\.[^/.]+$/, "");
                let permalink = getSlug(fileNoExt);
                let faq_body = fs.readFileSync(path.join(__dirname, "..", "public", "temp", "import", file), "utf-8");
                if (faq_body === "") {
                    faq_body = "FAQ body";
                }

                // setup the doc to insert
                let doc = {
                    kb_permalink: permalink,
                    kb_title: fileNoExt,
                    kb_body: faq_body,
                    kb_published: "false",
                    kb_keywords: "",
                    kb_published_date: new Date(),
                    kb_last_updated: new Date(),
                    kb_featured: "false",
                    kb_pinned: "false",
                    kb_pinned_topic: "",
                    kb_dashboard: "false",
                    kb_last_update_user: req.session.users_name + " - " + req.session.user,
                    kb_author: req.session.users_name,
                    kb_author_email: req.session.user,
                };

                // check permalink if it exists
                common.validate_permalink(db, doc, (err, result) => {
                    // duplicate permalink
                    if (!err) {
                        // insert article
                        db.kb.insert(doc, (err, newDoc) => { });
                    }
                });
            });

            // clean up dir
            rimraf.sync("public/temp/import");
            req.session.message = t("Articles imported successfully");
            req.session.message_type = "success";
            res.redirect("/import");
        });
    });
});

// export files into .md files and serve to browser
router.get("/export", common.restrict, async (req, res) => {
    const db = await getDb();

    // only allow admin
    if (req.session.is_admin !== "true") {
        res.render("error", {
            message: t("Access denied"),
            helpers: req.handlebars,
            config: config,
        });
        return;
    }

    // dump all articles to .md files. Article title is the file name and body is contents
    common.dbQuery(db.kb, {}, null, null, (err, results) => {
        // files are written and added to zip.
        let zip = new JSZip();
        for (let i = 0; i < results.length; i++) {
            // add and write file to zip
            zip.file(results[i].kb_title + ".md", results[i].kb_body);
        }

        // save the zip and serve to browser
        let buffer = zip.generate({ type: "nodebuffer" });
        fs.writeFile("data/export.zip", buffer, (err) => {
            if (err) throw err;
            res.set("Content-Type", "application/zip");
            res.set("Content-Disposition", "attachment; filename=data/export.zip");
            res.set("Content-Length", buffer.length);
            res.end(buffer, "binary");
        });
    });
});

// return sitemap
router.get("/sitemap.xml", async (req, res, next) => {
    const db = await getDb();

    // get the articles
    common.dbQuery(
        db.kb,
        { kb_published: "true", kb_visible_state: { $ne: "private" } },
        null,
        null,
        (err, articles) => {
            let urlArray = [];

            // push in the base url
            urlArray.push({ url: "/", changefreq: "weekly", priority: 1.0 });

            // get the article URL's
            for (let key in articles) {
                if (Object.prototype.hasOwnProperty.call(articles, key)) {
                    // check for permalink
                    let pageUrl = "/" + config.settings.route_name + "/" + articles[key]._id;
                    if (articles[key].kb_permalink !== "") {
                        pageUrl = "/" + config.settings.route_name + "/" + articles[key].kb_permalink;
                    }
                    urlArray.push({ url: pageUrl, changefreq: "weekly", priority: 1.0 });
                }
            }

            // create the sitemap
            let sitemap = sm.createSitemap({
                hostname: req.protocol + "://" + req.headers.host,
                cacheTime: 600000, // 600 sec - cache purge period
                urls: urlArray,
            });

            // render the sitemap
            sitemap.toXML((err, xml) => {
                if (err) {
                    return res.status(500).end();
                }
                res.header("Content-Type", "application/xml");
                return res.send(xml);
            });
        }
    );
});



module.exports = router;
