{{> header hideConfig=is_pseudoadmin}}

<div class="tab-content" id="nav-tabContent">
	{{!-- <div class="tab-pane fade show active" id="navContentDashboard" role="tabpanel" aria-labelledby="nav-home-tab">
		--}}
		<div class="" id="navContentDashboard">
			<div class="main-content">
				<div id="settingsAnalyticsContainer">
					<input type="hidden" value="" id="startDate">
					<input type="hidden" value="" id="endDate">
					<!-- Modal -->
					<div class="modal fade" id="modalViewComments" tabindex="-1" aria-labelledby="modalViewCommentsLabel"
						aria-hidden="true">
						<div class="modal-dialog modal-lg">
							<div class="modal-content">
								<div class="modal-header">
									<h5 class="modal-title" id="modalViewCommentsLabel">{{translate "translate Article Comments"}}</h5>
									<button type="button" class="close" data-dismiss="modal" aria-label="Close">
										<span aria-hidden="true">&times;</span>
									</button>
								</div>
								<div class="modal-body" id="modalViewCommentsBody"
									style="max-height: calc(100vh - 400px);overflow-y: auto;overflow-x: hidden;">

								</div>

							</div>
						</div>
					</div>

					<div class="row mt-2">

						<div class="col-12 daterange-analytics">
							<div id="dateRangeAnalytics" class="pull-right"
								style="background: #fff; cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
								<i class="fa fa-calendar"></i>&nbsp;
								<span></span> <i class="fa fa-caret-down"></i>
							</div>
						</div>

					</div>
					<div class="mt-4 mb-4" id="analytics-charts">

						<div class="" id="analytics-overview-container">
							<h2 class="chart-header">
								{{translate "Trends Overview"}}
								<i class="info-tooltip" data-toggle="tooltip" data-placement="right"
									title="{{translate "Overview of searchers for the last 14 days"}}">
									<span class="use-skeleton use-skeleton--absolute rounded--full"></span>
								</i>
							</h2>
							<div class="overview-wrap"><canvas id="analytics-overview"></canvas></div>
							<div class="overview-statistics">
								<h2 class="heading-w-badge"><span class="use-skeleton rounded">{{translate "Statistics"}}</span> <span
										class="reporting-period custom-badge custom-badge--success display-period use-skeleton rounded"></span>
								</h2>
								<dl class="overview-stats">
									<div class="overview-stat">
										<dt class="use-skeleton rounded">{{translate "Total Users"}}</dt>
										<dd><span id="totalUsers" class="use-skeleton rounded"></span></dd>
									</div>
									<div class="overview-stat">
										<dt class="use-skeleton rounded">{{translate "Total Clicks"}}</dt>
										<dd><span id="totalClicks" class="use-skeleton rounded"></span></dd>
									</div>
									<div class="overview-stat">
										<dt class="use-skeleton rounded">{{translate "Total No Clicks"}}</dt>
										<dd><span id="totalNoClicks" class="use-skeleton rounded"></span></dd>
									</div>
									<div class="overview-stat">
										<dt class="use-skeleton rounded"><span>{{translate "Total No Results"}}</span> 
										</dt>
										<dd><span id="totalNoResults" class="use-skeleton rounded"></span></dd>
									</div>
									<div class="overview-stat">
										<dt class="use-skeleton rounded"><span>{{translate "Total Conversions"}}</span> 
										</dt>
										<dd><span id="totalConversions" class="use-skeleton rounded"></span></dd>
									</div>
									<div class="overview-stat">
										<dt class="use-skeleton rounded"><span>{{translate "Tracked Searches"}}</span> 
										</dt>
										<dd><span id="totalTrackedSearches" class="use-skeleton rounded"></span></dd>
									</div>
								</dl>
							</div>
						</div>


						<div class="chart-wrap-mini mb-4">
							<h2 class="chart-header">
								{{translate "Total Searches"}}
								<i class="info-tooltip" data-toggle="tooltip" data-placement="right"
									title="{{translate "Number of searches performed. As-you-type searches are aggregated(ie. the querles b, ba, 'ban', 'bana o Results Ra & 'banana' count as one search)."}}">
									<span class="use-skeleton use-skeleton--absolute rounded--full"></span>
								</i>
							</h2><canvas id="analytics-total-searches">
						</div>
						<div class="chart-wrap-mini">
							<h2 class="chart-header">
								{{translate "No Results Rate"}}
								<i class="info-tooltip" data-toggle="tooltip" data-placement="right"
									title="{{translate "Percentage of searches in which 0 results were found."}}">
									<span class="use-skeleton use-skeleton--absolute rounded--full"></span>
								</i>
							</h2><canvas id="analytics-no-results">
						</div>

					</div>
					<div class="page-topics-analytics">
					
							{{!-- This is the Pie Chart --}}
							<div class="chart-container">
								<canvas id="topicsSubtopicsChart" class=""></canvas>
							</div>
						
							{{!-- This is the Bar Chart --}}
							<div class="chart-container">
								<canvas id="pageViewsChart"></canvas>
							</div>
						
					</div>
					<div class="row search-data mt-4">
						<div class="col-md-4 mb-4">
							<div class="search-data-table" id="top-searches">
								<h2 class="chart-header">{{translate "Top Searches"}}</h2>
								<a href="#" id="top-searches-all" target="_parent" class="analytics-view-all waves-effect">{{translate "View All"}}</a>
								<table>
									<thead>
										<tr>
											<td></td>
											<td>{{translate "Query"}}</td>
											<td>{{translate "Count"}}</td>
										</tr>
									</thead>
									<tbody></tbody>
								</table>
							</div>
						</div>
						<div class="col-md-4 mb-4">
							<div class="search-data-table" id="top-results">
								<h2 class="chart-header">{{translate "Top Results"}}</h2>
								<a href="#" id="top-results-all" target="_parent" class="analytics-view-all waves-effect">{{translate "View All"}}</a>
								<table>
									<thead>
										<tr>
											<td></td>
											<td>{{translate "Result"}}</td>
											<td>{{translate "Count"}}</td>
										</tr>
									</thead>
									<tbody></tbody>
								</table>
							</div>
						</div>
						<div class="col-md-4 mb-4">
							<div class="search-data-table" id="no-results">
								<h2 class="chart-header">{{translate "Searches without results"}}</h2>
								<a href="#" id="top-no-results-all" target="_parent" class="analytics-view-all waves-effect">{{translate "View All"}}</a>
								<table>
									<thead>
										<tr>
											<td></td>
											<td>{{translate "Query"}}</td>
											<td>{{translate "Count"}}</td>
										</tr>
									</thead>
									<tbody></tbody>
								</table>
							</div>
						</div>
					</div>
					
					
				</div>
			</div>
		</div>

		<div class="tab" id="navContentArticles">

			{{> articles data=data }}

			<input type="hidden" value="{{languages}}" id="languages-options" />

		</div>
		<div class="" id="navContentConfiguration">
			<div class="main-content">

				<form method="post" action="{{app_context}}/update_settings" id="settingsForm">
					<h4 class=" mt-3">{{translate "Wiki Configuration"}}
						<div class="pull-right">
							<button class="btn btn-sm btn-outline-success" type="submit" form="settingsForm">
								<i class="fa fa-save" aria-hidden="true"></i> {{translate "Save Changes"}}
							</button>
						</div>
					</h4>

					<nav data-tabtoopen="{{tab}}" id="navContainer">
						<div class="nav nav-tabs" id="nav-tab" role="tablist">
							<a class="nav-item nav-link active" id="tabUserPreferences" data-toggle="tab" href="#" aria-controls="nav-userpreferences"
								aria-selected="true">{{translate "User Preferences"}}</a>

							<a class="nav-item nav-link" id="tabWebsite" data-toggle="tab" href="#" aria-controls="nav-website"
								aria-selected="false">{{translate "Website"}}</a>

							<a class="nav-item nav-link" id="tabArticle" data-toggle="tab" href="#" aria-controls="nav-article"
								aria-selected="false">{{translate "Article"}}</a>

							<a class="nav-item nav-link" id="tabDisplay" data-toggle="tab" href="#" aria-controls="nav-display"
								aria-selected="false">{{translate "Display"}}</a>

							<a class="nav-item nav-link" id="tabStyle" data-toggle="tab" href="#" aria-controls="nav-style"
								aria-selected="false">{{translate "Style"}}</a>

							<a class="nav-item nav-link" id="tabTopicSettings" data-toggle="tab" href="#" aria-controls="nav-style"
								aria-selected="false">{{translate "Topic Settings"}}</a>

							<a class="nav-item nav-link" id="tabFiles" data-toggle="tab" href="#" aria-controls="nav-style"
								aria-selected="false">{{translate "Files"}}</a>
  
							<a class="nav-item nav-link" id="tabImport" data-toggle="tab" href="#" aria-controls="nav-style"
								aria-selected="false">{{translate "Import"}}</a>

							<a class="nav-item nav-link" id="nav-topics-tab" href="{{app_context}}/export" aria-controls="nav-topics"
								aria-selected="false">{{translate "Export"}}</a>
						</div>
					</nav>

					<div class="tabcontent-settings" id="tabContentWebsite">
						<div class="form-group mt-4">
							<label class="control-label" for="website_title">{{translate "Website title"}}:</label>
							<input type="text" class="form-control" name="website_title" value="{{config.settings.website_title}}"
								required>
							<small>{{translate "The title of your website"}}.</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="website_description">{{translate "Website description"}}:</label>
							<input type="text" class="form-control" name="website_description"
								value="{{config.settings.website_description}}" required>
							<small class="help-block">
								{{translate "A short website description when listing the homepage URL in search engines"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_website_logo">{{translate "Show website logo"}}:</label>
							<select class="form-control" name="show_website_logo">
								<option {{select_state config.settings.show_website_logo true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.show_website_logo false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Controls whether to show the \"Website title\" text or a logo located:
								\"/public/logo.png\" (by default)"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="app_context">{{translate "Website context/base URL"}}:</label>
							<input type="text" class="form-control" name="app_context" value="{{config.settings.app_context}}">
							<small class="help-block">
								{{translate "Allows for the website to be run from a non root path. Eg:
								http://127.0.0.1:4444/openkb/"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="api_allowed">{{translate "Allow API access"}}:</label>
							<select class="form-control" name="api_allowed">
								<option {{select_state config.settings.api_allowed true}} value="true">{{translate "true"}}</option>
								<option {{select_state config.settings.api_allowed false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Whether to allow API access to insert articles - See documentation for further
								information"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="api_auth_token">{{translate "API access token"}}:</label>
							<input type="text" class="form-control" name="api_auth_token" value="{{config.settings.api_auth_token}}">
							<small class="help-block">
								{{translate "Requires \"Allow API access\" to be set to \"true\". The value is the access token
								required to access the public API. Please set to a hard to guess value"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="password_protect">{{translate "Password protect"}}:</label>
							<select class="form-control" name="password_protect">
								<option {{select_state config.settings.password_protect true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.password_protect false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Setting to \"true\" will require a user to login before viewing any pages"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="index_article_body">{{translate "Index article body"}}:</label>
							<select class="form-control" name="index_article_body">
								<option {{select_state config.settings.index_article_body true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.index_article_body false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Whether to add the body of your articles to the search index (requires restart)"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_kb_meta">{{translate "Select a theme"}}:</label>
							<select class="form-control" name="theme">
								<option></option>
								{{#each themes}}
								<option {{select_state @root.config.settings.theme this}}>{{this}}</option>
								{{/each}}
							</select>
							<small class="help-block">
								{{translate "The theme to use for public facing pages. Leave blank for default"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_kb_meta">{{translate "Select a language"}}:</label>
							<select class="form-control" name="locale">
								<option></option>
								{{#each locale}}
								<option {{select_state @root.config.settings.locale this}}>{{this}}</option>
								{{/each}}
							</select>
							<small class="help-block">
								{{translate "The language to use for public facing pages. Leave blank for default (English)"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_logon">{{translate "Show logon link"}}:</label>
							<select class="form-control" name="show_logon">
								<option {{select_state config.settings.show_logon true}} value="true">{{translate "true"}}</option>
								<option {{select_state config.settings.show_logon false}} value="false">{{translate "false"}}</option>
							</select>
							<small class="help-block">
								{{translate "Whether to show/hide the logon link in the top right of screen"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="date_format">{{translate "Date format"}}:</label>
							<input type="text" class="form-control" name="date_format" value="{{config.settings.date_format}}"
								required>
							<small class="help-block">
								{{translate "Sets the global date formatting. Uses moment.js date formatting, see more here:
								http://momentjs.com/docs/#/displaying"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="suggest_allowed">{{translate "Article suggestions allowed"}}:</label>
							<select class="form-control" name="suggest_allowed">
								<option {{select_state config.settings.suggest_allowed true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.suggest_allowed false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "If enabled non authenticated users can submit article suggestions for approval"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="links_blank_page">{{translate "Google analytics code"}}:</label>
							<textarea rows="5" class="form-control"
								name="google_analytics">{{config.settings.google_analytics}}</textarea>
							<small class="help-block">
								{{translate "Adds Google Analytics to public facing pages. Include the entire code from Google
								including the
								script123 tags"}}.
							</small>
						</div>
					</div>

					<div class="tabcontent-settings" id="tabContentArticle">
						<div class="form-group mt-4">
							<label class="control-label" for="allow_voting">{{translate "Allow voting"}}:</label>
							<select class="form-control" name="allow_voting">
								<option {{select_state config.settings.allow_voting true}} value="true">{{translate "true"}}</option>
								<option {{select_state config.settings.allow_voting false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Whether to allow users to vote on an article"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_kb_meta">{{translate "Show article meta data"}}:</label>
							<select class="form-control" name="show_kb_meta">
								<option {{select_state config.settings.show_kb_meta true}} value="true">{{translate "true"}}</option>
								<option {{select_state config.settings.show_kb_meta false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Whether to show article meta data including published date, last updated date, author
								etc"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_author_email">{{translate "Show author email"}}:</label>
							<select class="form-control" name="show_author_email">
								<option {{select_state config.settings.show_author_email true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.show_author_email false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Controls whether the authors email address is displayed in the meta. Requires \"Show
								article meta
								data\" to be true"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="links_blank_page">{{translate "Article links open new page"}}:</label>
							<select class="form-control" name="links_blank_page">
								<option {{select_state config.settings.links_blank_page true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.links_blank_page false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Controls whether links within articles open a new page (tab)"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="add_header_anchors">{{translate "Add header anchors"}}:</label>
							<select class="form-control" name="add_header_anchors">
								<option {{select_state config.settings.add_header_anchors true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.add_header_anchors false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Whether to add HTML anchors to all heading tags for linking within articles or direct
								linking from
								other articles"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="enable_spellchecker">{{translate "Enable editor
								spellchecker"}}:</label>
							<select class="form-control" name="enable_spellchecker">
								<option {{select_state config.settings.enable_spellchecker true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.enable_spellchecker false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Controls whether to enable the editor spellchecker"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="article_versioning">{{translate "Allow article versioning"}}:</label>
							<select class="form-control" name="article_versioning">
								<option {{select_state config.settings.article_versioning true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.article_versioning false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Whether to track article versions with each save of the editor"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="mermaid">{{translate "Allow Mermaid"}}:</label>
							<select class="form-control" name="mermaid">
								<option {{select_state config.settings.mermaid true}} value="true">{{translate "true"}}</option>
								<option {{select_state config.settings.mermaid false}} value="false">{{translate "false"}}</option>
							</select>
							<small class="help-block">
								{{{translate "Whether to enable <a href=\"http://knsv.github.io/mermaid/\"
									target=\"_blank\">mermaid</a>"}}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="mathjax">{{translate "Allow MathJax"}}:</label>
							<select class="form-control" name="mathjax">
								<option {{select_state config.settings.mathjax true}} value="true">{{translate "true"}}</option>
								<option {{select_state config.settings.mathjax false}} value="false">{{translate "false"}}</option>
							</select>
							<small class="help-block">
								{{{translate "Whether to enable <a href=\"https://www.mathjax.org/\" target=\"_blank\">MathJax</a>"}}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="mathjax_input_mode">{{ translate "MathJax input mode"}}:</label>
							<input type="text" class="form-control" name="mathjax_input_mode"
								value="{{config.settings.mathjax_input_mode}}" required>
							<small class="help-block">
								{{{translate "Configure the mode MathJax operates in - <a
									href=\"http://docs.mathjax.org/en/latest/configuration.html#configuring-mathjax\"
									target=\"_blank\">Configure
									MathJax</a>"}}}.
							</small>
						</div>
					</div>

					<div class="tabcontent-settings" id="tabContentDisplay">
						<div class="form-group mt-4">
							<label class="control-label" for="num_top_results">{{translate "Number of top articles shown on
								homepage"}}:</label>
							<input type="number" class="form-control" name="num_top_results"
								value="{{config.settings.num_top_results}}" required>
							<small class="help-block">
								{{translate "Sets the number of results shown on the home page"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_published_date">{{translate "Show published date"}}:</label>
							<select class="form-control" name="show_published_date">
								<option {{select_state config.settings.show_published_date true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.show_published_date false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Shows the published date next to the results on the homepage and search"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_view_count">{{translate "Show view count"}}:</label>
							<select class="form-control" name="show_view_count">
								<option {{select_state config.settings.show_view_count true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.show_view_count false}} value="false">{{translate "false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Shows the view count next to the results on the homepage and search"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="update_view_count_logged_in">{{translate "Update view count when
								logged
								in"}}:</label>
							<select class="form-control" name="update_view_count_logged_in">
								<option {{select_state config.settings.update_view_count_logged_in true}} value="true">{{translate
									"true"}}
								</option>
								<option {{select_state config.settings.update_view_count_logged_in false}} value="false">{{translate
									"false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Updates the view count also when users are logged in"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_featured_articles">{{translate "Show featured
								articles"}}:</label>
							<select class="form-control" name="show_featured_articles">
								<option {{select_state config.settings.show_featured_articles true}} value="true">{{translate "true"}}
								</option>
								<option {{select_state config.settings.show_featured_articles false}} value="false">{{translate
									"false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Whether to show any articles set to featured in a sidebar"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="show_featured_in_article">{{translate "Show featured articles when
								viewing
								article"}}:</label>
							<select class="form-control" name="show_featured_in_article">
								<option {{select_state config.settings.show_featured_in_article true}} value="true">{{translate
									"true"}}
								</option>
								<option {{select_state config.settings.show_featured_in_article false}} value="false">{{translate
									"false"}}
								</option>
							</select>
							<small class="help-block">
								{{translate "Whether to show any articles set to featured in a sidebar when viewing an article"}}.
							</small>
						</div>
						<div class="form-group">
							<label class="control-label" for="featured_articles_count">{{translate "Featured article
								count"}}:</label>
							<input type="number" class="form-control" name="featured_articles_count"
								value="{{config.settings.featured_articles_count}}" required>
							<small class="help-block">
								{{translate "The number of featured articles shown"}}.
							</small>
						</div>
					</div>

					<div class="tabcontent-settings" id="tabContentStyle">
						<div class="">
							<div class="row mt-4">
								<p class="col-md-12">
									{{translate "You can add simple styling to various elements of your website below:"}}
								</p>
								<div class="col-md-6">
									<div class="form-group">
										<label class="control-label" for="cssHeaderBackgroundColor">{{translate "Header background color
											(HEX):"}}</label>
										<input type="text" class="form-control" name="style.cssHeaderBackgroundColor"
											value="{{config.settings.style.cssHeaderBackgroundColor}}" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="form-group">
										<label class="control-label" for="cssHeaderTextColor">{{translate "Header text color
											(HEX):"}}</label>
										<input type="text" class="form-control" name="style.cssHeaderTextColor"
											value="{{config.settings.style.cssHeaderTextColor}}" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="form-group">
										<label class="control-label" for="cssFooterBackgroundColor">{{translate "Footer background color
											(HEX):"}}</label>
										<input type="input" class="form-control" name="style.cssFooterBackgroundColor"
											value="{{config.settings.style.cssFooterBackgroundColor}}" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="form-group">
										<label class="control-label" for="cssFooterTextColor">{{translate "Footer text color
											(HEX):"}}</label>
										<input type="input" class="form-control" name="style.cssFooterTextColor"
											value="{{config.settings.style.cssFooterTextColor}}" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="form-group">
										<label class="control-label" for="cssButtonBackgroundColor">{{translate "Button background color
											(HEX):"}}</label>
										<input type="input" class="form-control" name="style.cssButtonBackgroundColor"
											value="{{config.settings.style.cssButtonBackgroundColor}}" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="form-group">
										<label class="control-label" for="cssButtonTextColor">{{translate "Button text color
											(HEX):"}}</label>
										<input type="input" class="form-control" name="style.cssButtonTextColor"
											value="{{config.settings.style.cssButtonTextColor}}" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="form-group">
										<label class="control-label" for="cssLinkColor">{{translate "Link color (HEX):"}}</label>
										<input type="text" class="form-control" name="style.cssLinkColor"
											value="{{config.settings.style.cssLinkColor}}" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="form-group">
										<label class="control-label" for="cssTextColor">{{translate "Page text color (HEX):"}}</label>
										<input type="text" class="form-control" name="style.cssTextColor"
											value="{{config.settings.style.cssTextColor}}" />
									</div>
								</div>
								<div class="col-md-6">
									<div class="form-group">
										<label class="control-label" for="cssFontFamily">{{translate "Page font:"}}</label>
										<input type="text" class="form-control" name="style.cssFontFamily"
											value="{{config.settings.style.cssFontFamily}}" />
									</div>
								</div>
							</div>
						</div>
					</div>
				</form>

				<div class="tabcontent-settings" id="tabContentTopicSettings">

					<h4 class=" mt-3">{{translate "Topics and Sub-topics Management"}}
						{{!-- <button class="btn btn-outline-primary btn-sm" id="btnSetRegionUserLevel"> Set Articles Default
							Region and User Level Once</button> --}}
						<button class="btn btn-outline-primary btn-sm pull-right mr-1" type="button" id="btnNewTopic">
							<i class="fa fa-plus"></i> {{translate "New Topic"}}
						</button>
					</h4>

					<div class="row mt-3">
						<div class="col-xs-12 col-sm-5">
							<label for="">{{translate "Topics list"}}</label>
							<div id="topics-contents">
								<div class="list-group topic-list" id="topicsListGroup">

								</div>
							</div>
						</div>
						<div class="col-xs-12 col-sm-7" id="topics-card">
							<label for="">{{translate "Topic details"}}</label>

							<div>
								<div class="card card-body">

									<form method="post" action="" id="topicDetailsForm">
										<input type="hidden" value="" name="_id" id="hiddenTopicId">
										<div class="row">
											<div class="col-sm-12">
												<div class="form-inline">
													<small for="display_order">{{translate "Order"}}&nbsp;&nbsp;</small>
													<input class="text-center mr-auto form-control form-control-sm" type="text" id="displayOrder"
														name="display_order" value="" style="max-width: 50px;">
													<div class="custom-control custom-switch pull-right">
														<input type="checkbox" class="custom-control-input" id="enableSwitch" value="">
														<label class="custom-control-label" id="enableSwitchLabel" for="enableSwitch">{{translate
															"Disabled"}}</label>
													</div>
												</div>
												<br>
												<small>{{translate "Topic"}} </small>
												<input class="form-control" type="text" name="topic" id="topic" value="">
												{{!-- <br>
												<small>{{translate "Keyword"}} </small>
												<input class="form-control" type="text" name="keyword" id="keyword" value=""> --}}
												<br>
												<small>{{translate "SVG Icon"}}</small>
												<textarea class="form-control" type="text" name="svg" id="svg"></textarea>
												<br>

											</div>

										</div>
										<div class="row mt-2">
											<div class="col-sm-12">

												<label class="mt-2">{{translate "Sub-topics"}}</label>

												<button class="btn btn-outline-primary btn-sm pull-right" id="btnNewSubtopic"
													type="button">&nbsp;<i class="fa fa-plus"></i>&nbsp;
												</button>

												<div class="card card-body" id="cardSubtopics">

												</div>
											</div>
										</div>
										<div class="row mt-3">
											<div class="col-sm-12">
												<div class="form-group">
													<div class="selectBox">
														<input type="hidden" id="topic_regional_visibility" name="topic_regional_visibility"
															value="">
														<small>{{translate "Regional Visibility"}}</small>
														<br>
														<small class="font-italic">{{translate "Topics without any regions configured will be
															globally visible"}}</small>
														<select class="form-control" id="selectTopicCountry" data-options="{{countriesOnly}}">
															<option value="" id="optionSelectTopicCountry"></option>
														</select>

														<div class="overSelect" id="overSelect"></div>
													</div>
													<div id="countryCheckboxes">
													</div>
												</div>
											</div>
										</div>

										<div class="row mt-3">
											<div class="col-sm-12 mt-4">
												<button type="button" class="btn btn-outline-danger btn-sm" id="btnDeleteTopic">
													<i class="fa fa-trash"></i> {{translate "Delete Permanently"}}</button>

												<div class="pull-right">
													<button type="button" class="btn btn-outline-success btn-sm" id="btnSaveTopicChanges">
														<i class="fa fa-save"></i> {{translate "Save"}}</button>
												</div>
											</div>
										</div>
									</form>

								</div>

							</div>
						</div>
					</div>


				</div>

				<div class="tabcontent-settings" id="tabContentFiles">

					<div class="">

						<h3 class="mt-3">{{translate "Files"}}</h3>

						<form class="form-horizontal" role="form" method="post" action="{{app_context}}/file/upload"
							enctype="multipart/form-data">
							<div class="form-group">
								<span class="btn btn-outline-info btn-file">
									{{translate "Select file"}}<input type="file" name="upload_file" id="upload_file">
								</span>
								{{!-- <div class="form-group file-form"> --}}
									<button type="submit" class="btn btn-outline-success pull-right">{{translate "Upload
										file"}}</button>
									{{!--
								</div> --}}
							</div>

							<div class="form-group">
								<label for="custom_dir">{{translate "Upload directory"}}</label>
								<select class="form-control" name="directory">
									{{#each dirs}}
									<option>{{path}}</option>
									{{/each}}
								</select>
							</div>

							<ul class="list-group">
								{{#each files}}
								<li class="list-group-item" id="file-{{this.id}}">
									<span><i class="fa fa-file-image-o"></i></span>
									<span class="align-left"><a href="{{app_context}}{{this.path}}"
											target="_blank">{{this.path}}</a></span>
									<span class="pull-right" style="padding-left: 10px;"><a
											class="btn btn-outline-danger btn-xs file_delete_confirm" data-id="{{this.id}}"
											data-path="{{this.path}}"><i class="fa fa-trash-o"></i></a></span>
								</li>
								{{/each}}
							</ul>
							{{!-- <p class="text-warning">{{__ "Tip"}}:
								{{__ "To insert an image right click the link, copy the link address and add it to your Markdown"}}
							</p> --}}
							<small>{{translate "Tip"}}:
								{{translate "To insert an image right click the link, copy the link address and add it to your
								Markdown"}}</small>
						</form>

						<form class="form-horizontal mt-3" role="form" method="post" action="{{app_context}}/file/new_dir">
							<label for="directory">{{translate "New directory"}}</label>
							<div class="input-group">
								<input type="text" class="form-control" name="custom_dir" id="custom_dir" placeholder="{{translate "
									new/directory"}}">
								<span class="input-group-btn">
									<button class="btn btn-outline-success" type="submit">{{translate "Create"}}</button>
								</span>
							</div>
							<small>{{translate "Multiple directories can be created using a \"/\" separator"}}</small>
						</form>

					</div>

				</div>

				<div class="tabcontent-settings" id="tabContentImport">
					<h4 class="mt-3 mt-3">{{translate "Import"}}</h4>
					<div class="row">

						<div class="col-md-12">
							<p class="text-muted">
								{{translate "You are able to bulk import articles by uploading a Zip file containing files with
								Markdown content. When new articles are created, the name of the file will become the title/permalink.
								All imported articles will be set to a draft status."}}
							</p>
							<form class="form-horizontal" role="form" method="post" action="{{app_context}}/importer"
								enctype="multipart/form-data">
								<div class="row">
									<div class="col-md-6">
										<div class="fileinput fileinput-new" data-provides="fileinput">
											<span class="btn btn-outline-primary btn-file"><span class="fileinput-new">{{translate "Select
													zip"}}</span><span class="fileinput-exists">{{translate "Change"}}</span><input type="file"
													name="import_file" id="import_file"></span>
											<span class="fileinput-filename"></span>
											<a href="#" class="close fileinput-exists" data-dismiss="fileinput"
												style="float: none">&times;</a>
										</div>
									</div>
									<div class="col-md-6">
										<div class="form-group file-form">
											<button type="submit" class="btn btn-outline-success pull-right">{{translate "Import
												zip"}}</button>
										</div>
									</div>
								</div>
							</form>
						</div>
					</div>
				</div>

				<div class="tabcontent-settings" id="tabContentUserPreferences">
					<h4 class="mt-3">{{translate "User Preferences"}}</h4>
					<div class="row">
						<div class="col-md-12">
							<p class="text-muted">
								{{translate "Customize your personal preferences for using the wiki application."}}
							</p>
							
							<div class="form-group ph-form-group mt-4">
								<div class="row">
									<div class="col-md-6 ph-text">
										<div class="d-flex flex-column flex-gap-2 mb-2">
											<div class="form-label text-secondary">{{translate "Changelog Settings"}}</div>
											<hr class="ph-hr"/>
										</div>
										<div class="mt-3">
											<div class="form-check d-flex align-items-center mt-2">
												<input type="checkbox" class="form-check-input ph-checkbox" id="skip_changelog_modal_setting" />
												<label class="form-check-label text-fg" for="skip_changelog_modal_setting">
													{{translate "Dont show the changelog summary modal when saving articles"}}
												</label>
											</div>
											<small class="text-muted">
												{{translate "AI will continue to automatically generate changelog summaries whenever articles are saved."}}
											</small>
										</div>	
									</div>
								</div>
							</div>
							
							<div class="form-group mt-4">
								<button type="button" class="ph-btn btn-primary" id="save-user-preferences">
									{{translate "Save Preferences"}}
								</button>
								<div id="preferences-message" class="alert mt-3" style="display: none;"></div>
							</div>
						</div>
					</div>
				</div>

			</div>
		</div>
	</div>
</div>


<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/js/bootstrap-select.min.js"></script>


<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/topic-settings.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/js-yaml/4.1.0/js-yaml.min.js"></script>


<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/articles-script.js"></script>
<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/settings.js"></script>
<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/duplicate-article.js"></script>
<script type="text/javascript" src="{{app_context}}/themes/12rnd/js/articles-datatable.js"></script>