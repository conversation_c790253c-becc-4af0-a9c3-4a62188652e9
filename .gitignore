# Packages pulled down from NPM/Yarn
node_modules
.serverless
root

# ignore balena token dir...
balena

# Log files
*.log

# VIM
*.swp
*.swo

# System files
.DS_Store
Thumbs.db

# Env files
.env

# We don't want any of the dist files going into git repo...
portal-front-end/dist

# We don't want the api spec in git... That should be generated on the fly as part of the build...
openapi.json

# Exclude serverless layer for imagemagick
image-magick/build
portal-server/serverless-layers/image-magick/build

# Default dynamodb files..
.db

# Local redis files
.redis_data

# ignore asset uploads
portal-server/public

docker-compose-test.yml
docker-compose-local.yml
docker-compose-sls.yml
portal-server/Dockerfile.sls
portal-server/start-local.sh
Dockerfile.sls

# vscode settings
.vscode